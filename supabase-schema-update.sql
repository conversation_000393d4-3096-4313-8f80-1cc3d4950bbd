-- Updated Supabase Schema for Visa AI Admin Panel
-- Execute this after the existing supabase-setup.sql

-- Create companies table (replacing admin_companies)
DROP TABLE IF EXISTS public.admin_companies CASCADE;
CREATE TABLE public.companies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  phone_number TEXT,
  email TEXT,
  is_blocked BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create employees table (replacing admin_users with enhanced structure)
DROP TABLE IF EXISTS public.admin_users CASCADE;
CREATE TABLE public.employees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('super_admin', 'visa_admin', 'employee')),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  full_name TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create price_list table
CREATE TABLE IF NOT EXISTS public.price_list (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  country TEXT NOT NULL,
  title TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  duration TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add status fields to visa_applications table
ALTER TABLE public.visa_applications 
ADD COLUMN IF NOT EXISTS service_payment_status TEXT DEFAULT 'не оплачено' CHECK (service_payment_status IN ('оплачено', 'не оплачено')),
ADD COLUMN IF NOT EXISTS service_package_id UUID REFERENCES price_list(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS visa_status TEXT DEFAULT 'ожидает' CHECK (visa_status IN ('ожидает', 'одобрено', 'отклонено', 'подано')),
ADD COLUMN IF NOT EXISTS client_progress_status TEXT DEFAULT 'прошли опросник',
ADD COLUMN IF NOT EXISTS manual_fix_status TEXT,
ADD COLUMN IF NOT EXISTS consular_fee_paid BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS requires_fixes BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS fix_comment TEXT,
ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES companies(id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_employees_company_id ON public.employees(company_id);
CREATE INDEX IF NOT EXISTS idx_employees_email ON public.employees(email);
CREATE INDEX IF NOT EXISTS idx_price_list_company_id ON public.price_list(company_id);
CREATE INDEX IF NOT EXISTS idx_price_list_country ON public.price_list(country);
CREATE INDEX IF NOT EXISTS idx_visa_applications_service_package_id ON public.visa_applications(service_package_id);
CREATE INDEX IF NOT EXISTS idx_visa_applications_service_payment_status ON public.visa_applications(service_payment_status);
CREATE INDEX IF NOT EXISTS idx_visa_applications_visa_status ON public.visa_applications(visa_status);
CREATE INDEX IF NOT EXISTS idx_visa_applications_company_id ON public.visa_applications(company_id);

-- Enable RLS on new tables
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.price_list ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Allow read access to companies"
  ON public.companies FOR SELECT
  USING (true);

CREATE POLICY "Allow insert companies for super admin"
  ON public.companies FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update companies for super admin"
  ON public.companies FOR UPDATE
  USING (true);

CREATE POLICY "Allow delete companies for super admin"
  ON public.companies FOR DELETE
  USING (true);

CREATE POLICY "Allow read access to employees"
  ON public.employees FOR SELECT
  USING (true);

CREATE POLICY "Allow insert employees"
  ON public.employees FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update employees"
  ON public.employees FOR UPDATE
  USING (true);

CREATE POLICY "Allow delete employees"
  ON public.employees FOR DELETE
  USING (true);

CREATE POLICY "Allow read access to price_list"
  ON public.price_list FOR SELECT
  USING (true);

CREATE POLICY "Allow insert price_list"
  ON public.price_list FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update price_list"
  ON public.price_list FOR UPDATE
  USING (true);

CREATE POLICY "Allow delete price_list"
  ON public.price_list FOR DELETE
  USING (true);

-- Insert sample data
INSERT INTO public.companies (id, name, phone_number, email) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'Global Visa Services', '+1234567890', '<EMAIL>'),
  ('550e8400-e29b-41d4-a716-446655440002', 'Premium Immigration Solutions', '+0987654321', '<EMAIL>'),
  ('550e8400-e29b-41d4-a716-446655440003', 'Elite Visa Consultancy', '+1122334455', '<EMAIL>')
ON CONFLICT (id) DO NOTHING;

-- Insert sample employees (super admin and visa admins)
INSERT INTO public.employees (id, email, password_hash, role, company_id, full_name) VALUES 
  ('660e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$10$hashedPassword', 'super_admin', NULL, 'Super Administrator'),
  ('660e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2b$10$hashedPassword', 'visa_admin', '550e8400-e29b-41d4-a716-446655440001', 'Global Visa Admin'),
  ('660e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '$2b$10$hashedPassword', 'visa_admin', '550e8400-e29b-41d4-a716-446655440002', 'Premium Immigration Admin'),
  ('660e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '$2b$10$hashedPassword', 'visa_admin', '550e8400-e29b-41d4-a716-446655440003', 'Elite Visa Admin')
ON CONFLICT (email) DO NOTHING;

-- Insert comprehensive price list data - multiple packages per country per company
INSERT INTO public.price_list (company_id, country, title, price, duration, description) VALUES 
  -- Global Visa Services (Company 1) - USA
  ('550e8400-e29b-41d4-a716-446655440001', 'США', 'Базовый', 450.00, '4-5 недель', 'Стандартное оформление туристической визы'),
  ('550e8400-e29b-41d4-a716-446655440001', 'США', 'Стандарт', 650.00, '3-4 недели', 'Ускоренное оформление с консультацией'),
  ('550e8400-e29b-41d4-a716-446655440001', 'США', 'Экспресс', 850.00, '1-2 недели', 'Максимально быстрое оформление'),
  
  -- Global Visa Services - UK
  ('550e8400-e29b-41d4-a716-446655440001', 'Великобритания', 'Стандарт', 550.00, '3-4 недели', 'Оформление британской визы'),
  ('550e8400-e29b-41d4-a716-446655440001', 'Великобритания', 'Премиум', 750.00, '2-3 недели', 'Ускоренное оформление с поддержкой'),
  
  -- Global Visa Services - Germany
  ('550e8400-e29b-41d4-a716-446655440001', 'Германия', 'Шенген Базовый', 400.00, '2-3 недели', 'Шенгенская виза через Германию'),
  ('550e8400-e29b-41d4-a716-446655440001', 'Германия', 'Шенген Экспресс', 600.00, '1-2 недели', 'Быстрое оформление шенгена'),
  
  -- Premium Immigration Solutions (Company 2) - USA
  ('550e8400-e29b-41d4-a716-446655440002', 'США', 'Эконом', 500.00, '5-6 недель', 'Бюджетное оформление американской визы'),
  ('550e8400-e29b-41d4-a716-446655440002', 'США', 'Стандарт Плюс', 700.00, '3-4 недели', 'Стандарт с дополнительными услугами'),
  ('550e8400-e29b-41d4-a716-446655440002', 'США', 'Премиум', 950.00, '1-2 недели', 'Премиум пакет с гарантией'),
  ('550e8400-e29b-41d4-a716-446655440002', 'США', 'VIP', 1200.00, '1 неделя', 'VIP обслуживание с личным менеджером'),
  
  -- Premium Immigration Solutions - Canada
  ('550e8400-e29b-41d4-a716-446655440002', 'Канада', 'Стандарт', 600.00, '4-5 недель', 'Оформление канадской визы'),
  ('550e8400-e29b-41d4-a716-446655440002', 'Канада', 'Экспресс', 800.00, '2-3 недели', 'Ускоренное оформление'),
  
  -- Elite Visa Consultancy (Company 3) - Multiple countries
  ('550e8400-e29b-41d4-a716-446655440003', 'США', 'Элит Стандарт', 750.00, '2-3 недели', 'Элитное оформление американской визы'),
  ('550e8400-e29b-41d4-a716-446655440003', 'США', 'Элит Премиум', 1100.00, '1 неделя', 'Максимальный сервис и скорость'),
  
  ('550e8400-e29b-41d4-a716-446655440003', 'Великобритания', 'UK Элит', 650.00, '2-3 недели', 'Элитное оформление британской визы'),
  ('550e8400-e29b-41d4-a716-446655440003', 'Франция', 'Шенген Элит', 500.00, '2-3 недели', 'Элитное оформление шенгена через Францию'),
  ('550e8400-e29b-41d4-a716-446655440003', 'Испания', 'Испания Стандарт', 450.00, '3-4 недели', 'Оформление испанской визы'),
  ('550e8400-e29b-41d4-a716-446655440003', 'Италия', 'Италия Экспресс', 550.00, '2-3 недели', 'Быстрое оформление итальянской визы')
ON CONFLICT DO NOTHING;

-- Temporary solution: Assign existing visa applications to random companies
-- This will be changed in the future when proper company assignment is implemented
UPDATE public.visa_applications 
SET company_id = (
  SELECT id FROM public.companies 
  ORDER BY RANDOM() 
  LIMIT 1
)
WHERE company_id IS NULL;

-- Add is_blocked column to companies table if it doesn't exist
ALTER TABLE companies ADD COLUMN IF NOT EXISTS is_blocked BOOLEAN DEFAULT false;

-- Update existing companies to have is_blocked = false by default
UPDATE companies SET is_blocked = false WHERE is_blocked IS NULL;

-- Create a simple function to update company blocking status
CREATE OR REPLACE FUNCTION update_company_blocking(
  company_uuid UUID,
  blocked_status BOOLEAN
) RETURNS BOOLEAN AS $$
BEGIN
  UPDATE companies 
  SET is_blocked = blocked_status, updated_at = NOW()
  WHERE id = company_uuid;
  
  -- Return true if update was successful
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_company_blocking(UUID, BOOLEAN) TO authenticated;

-- Create a separate table for company blocking status as workaround
CREATE TABLE IF NOT EXISTS company_blocking_status (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  is_blocked BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id)
);

-- Enable RLS on the new table
ALTER TABLE company_blocking_status ENABLE ROW LEVEL SECURITY;

-- Create policy for the blocking status table
CREATE POLICY "Allow authenticated users to read and write company blocking status" 
ON company_blocking_status FOR ALL 
TO authenticated 
USING (true) 
WITH CHECK (true);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_company_blocking_status_updated_at 
    BEFORE UPDATE ON company_blocking_status 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 