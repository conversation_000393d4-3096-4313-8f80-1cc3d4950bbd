-- Add WhatsApp API columns to companies table with random dummy data
-- Run this in your Supabase SQL Editor

-- Step 1: Add the missing WhatsApp API columns
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS wapi_token TEXT,
ADD COLUMN IF NOT EXISTS wapi_profile_id TEXT,
ADD COLUMN IF NOT EXISTS wapi_webhook_url TEXT;

-- Step 2: Add comments for documentation
COMMENT ON COLUMN public.companies.wapi_token IS 'WhatsApp API Token for integration';
COMMENT ON COLUMN public.companies.wapi_profile_id IS 'WhatsApp API Profile ID';
COMMENT ON COLUMN public.companies.wapi_webhook_url IS 'WhatsApp API Webhook URL for callbacks';

-- Step 3: Update all companies with random dummy data
UPDATE public.companies 
SET 
  wapi_token = 'asxazxaDsdjksdj_' || substr(md5(random()::text), 1, 10),
  wapi_profile_id = 'profile_' || substr(md5(random()::text), 1, 8) || '_001',
  wapi_webhook_url = 'https://dsadas.kz/webhook/' || substr(md5(random()::text), 1, 6),
  updated_at = NOW()
WHERE wapi_token IS NULL;

-- Alternative update with more random dummy data patterns
UPDATE public.companies 
SET 
  wapi_token = CASE 
    WHEN random() < 0.5 THEN 'asxazxaDsdjksdj_' || substr(id::text, 1, 8)
    ELSE 'randomToken_' || substr(md5(name || id::text), 1, 12)
  END,
  wapi_profile_id = 'prof_' || substr(md5(name || '_profile'), 1, 10),
  wapi_webhook_url = CASE 
    WHEN random() < 0.3 THEN 'https://dsadas.kz/wh/' || substr(id::text, 1, 6)
    WHEN random() < 0.6 THEN 'https://randomapi.kz/webhook/' || substr(md5(name), 1, 8)
    ELSE 'https://testwebhook.com/api/' || substr(id::text, 1, 8)
  END,
  updated_at = NOW()
WHERE wapi_token LIKE 'asxazxaDsdjksdj_%' OR wapi_token IS NULL;

-- Step 4: Create index for performance
CREATE INDEX IF NOT EXISTS idx_companies_wapi_profile_id ON public.companies(wapi_profile_id) WHERE wapi_profile_id IS NOT NULL;

-- Step 5: Verify the dummy data was added
SELECT 
  name,
  wapi_token,
  wapi_profile_id,
  wapi_webhook_url
FROM public.companies 
ORDER BY name; 