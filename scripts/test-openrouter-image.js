// Test OpenRouter Image processing
require('./load-env');

const testOpenRouterImage = async () => {
  console.log('=== OPENROUTER IMAGE PROCESSING TEST ===');
  
  const apiKey = process.env.NEXT_OR_KEY;
  
  if (!apiKey) {
    console.error('❌ NEXT_OR_KEY not found');
    return;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 10) + '...');
  
  // Test image URL you provided
  const imageUrl = 'https://jobktdnksbweojxfqvcs.supabase.co/storage/v1/object/public/temp-ocr-files/temp/1752061423644/d0b2dee0-b069-45bd-875a-388500060735.jpeg';
  
  console.log('\n🖼️ Testing image URL:', imageUrl);
  
  const testRequest = {
    model: process.env.NEXT_OR_MODEL || 'anthropic/claude-3.5-sonnet',
    max_tokens: 2000,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `Проанализируй это изображение паспорта или ID документа и извлеки следующие данные.
Верни результат строго в формате JSON без дополнительных комментариев:

{
  "name": "имя ТОЛЬКО в латинице (если найдено)",
  "surname": "фамилия ТОЛЬКО в латинице (если найдено)", 
  "dateOfBirth": "дата рождения в формате DD.MM.YYYY",
  "passportIssueDate": "дата выдачи в формате DD.MM.YYYY",
  "passportExpiryDate": "дата окончания в формате DD.MM.YYYY",
  "passportNumber": "номер паспорта",
  "nationality": "национальность/гражданство"
}`
          },
          {
            type: 'image_url',
            image_url: {
              url: imageUrl
            }
          }
        ]
      }
    ]
  };
  
  try {
    const startTime = Date.now();
    
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://visaai.vercel.app',
        'X-Title': 'VisaAI Image Test'
      },
      body: JSON.stringify(testRequest)
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const result = await response.json();
    
    console.log(`⏱️  Response time: ${duration}ms`);
    console.log('📊 Response status:', response.status);
    
    if (response.ok) {
      console.log('✅ Image processing - SUCCESS');
      console.log('🤖 Model used:', result.model);
      console.log('📝 Response:', result.choices[0]?.message?.content?.substring(0, 500) + '...');
      console.log('💰 Usage:', result.usage);
      
      // Try to parse JSON from response
      try {
        const content = result.choices[0]?.message?.content || '';
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonData = JSON.parse(jsonMatch[0]);
          console.log('📋 Parsed data:', jsonData);
        }
      } catch (parseError) {
        console.log('⚠️  Could not parse JSON from response');
      }
    } else {
      console.log('❌ Image processing - FAILED');
      console.log('🔍 Error:', result.error);
      console.log('📄 Full response:', result);
    }
    
  } catch (error) {
    console.log('💥 Image processing - ERROR');
    console.log('🔍 Error:', error.message);
  }
  
  console.log('\n=== IMAGE TESTING COMPLETE ===');
};

// Check credits first
const checkCredits = async () => {
  const apiKey = process.env.NEXT_OR_KEY;
  if (!apiKey) return;
  
  try {
    const response = await fetch('https://openrouter.ai/api/v1/credits', {
      headers: { 'Authorization': `Bearer ${apiKey}` }
    });
    const credits = await response.json();
    console.log('💰 Current credits:', credits);
  } catch (error) {
    console.log('⚠️  Could not check credits:', error.message);
  }
};

// Run tests
checkCredits().then(() => testOpenRouterImage());