require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY
);

console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set');
console.log('Service Role Key:', process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set');

async function initializeStorage() {
  try {
    console.log('Initializing Supabase Storage...');
    
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('Error listing buckets:', listError);
      return;
    }

    console.log('Existing buckets:', buckets?.map(b => b.name));

    const bucketExists = buckets?.some(bucket => bucket.name === 'temp-ocr-files');
    
    if (!bucketExists) {
      console.log('Creating temp-ocr-files bucket...');
      
      const { data, error: createError } = await supabase.storage.createBucket('temp-ocr-files', {
        public: true,
        fileSizeLimit: 10 * 1024 * 1024 // 10MB
      });

      if (createError) {
        console.error('Error creating bucket:', createError);
        
        // Try with minimal options
        console.log('Trying with minimal options...');
        const { data: minimalData, error: minimalError } = await supabase.storage.createBucket('temp-ocr-files');
        
        if (minimalError) {
          console.error('Failed to create bucket:', minimalError);
        } else {
          console.log('Bucket created successfully with minimal options:', minimalData);
        }
      } else {
        console.log('Bucket created successfully:', data);
      }
    } else {
      console.log('Bucket temp-ocr-files already exists');
    }

    // List buckets again to confirm
    const { data: finalBuckets } = await supabase.storage.listBuckets();
    console.log('Final buckets:', finalBuckets?.map(b => b.name));
    
  } catch (error) {
    console.error('Error initializing storage:', error);
  }
}

// Run the initialization
initializeStorage();
