// Test OpenRouter API connectivity
require('./load-env');

const testOpenRouterAPI = async () => {
  console.log('=== OPENROUTER API TEST ===');
  
  const apiKey = process.env.NEXT_OR_KEY;
  
  if (!apiKey) {
    console.error('❌ NEXT_OR_KEY not found');
    console.log('📝 Please set NEXT_OR_KEY in your .env.local file');
    return;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 10) + '...');
  
  // Test simple text request
  const testRequest = {
    model: 'anthropic/claude-3.5-sonnet',
    max_tokens: 100,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Hello, please respond with "OpenRouter API test successful"'
          }
        ]
      }
    ]
  };
  
  console.log('\n🔄 Testing OpenRouter API connectivity...');
  
  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://visaai.vercel.app',
        'X-Title': 'VisaAI OCR'
      },
      body: JSON.stringify(testRequest)
    });
    
    const result = await response.json();
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      console.log('✅ OpenRouter API Test successful!');
      console.log('Model:', result.model);
      console.log('Response:', result.choices[0]?.message?.content || 'No content');
      console.log('Usage:', result.usage);
    } else {
      console.error('❌ OpenRouter API Test failed');
      console.error('Error:', result);
    }
    
  } catch (error) {
    console.error('❌ OpenRouter API Test error:', error.message);
  }
};

testOpenRouterAPI();