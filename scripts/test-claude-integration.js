const { getClaudeOcrStatus, isClaudeOcrAvailable } = require('../src/utils/claude-vision-ocr');

console.log('=== CLAUDE INTEGRATION TEST ===');

// Test 1: Check if <PERSON> is available
console.log('\n1. Checking Claude OCR availability...');
const isAvailable = isClaudeOcrAvailable();
console.log('Claude OCR available:', isAvailable);

// Test 2: Get detailed status
console.log('\n2. Getting Claude OCR status...');
const status = getClaudeOcrStatus();
console.log('Status:', JSON.stringify(status, null, 2));

// Test 3: Environment check
console.log('\n3. Environment check...');
console.log('NEXT_CLAUDE_KEY set:', !!process.env.NEXT_CLAUDE_KEY);
console.log('NEXT_CLAUDE_KEY length:', process.env.NEXT_CLAUDE_KEY?.length || 0);

// Test 4: Configuration validation
console.log('\n4. Configuration validation...');
if (status.available) {
  console.log('✅ Claude integration is properly configured');
  console.log('Model:', status.model);
} else {
  console.log('❌ Claude integration is NOT configured');
  if (!status.hasApiKey) {
    console.log('- Missing NEXT_CLAUDE_KEY environment variable');
  }
}

console.log('\n=== TEST COMPLETE ===');