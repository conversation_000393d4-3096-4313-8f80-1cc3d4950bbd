// Test script to check environment variables in Next.js context
const path = require('path');
const fs = require('fs');

console.log('=== ENVIRONMENT VARIABLES TEST ===');

// Check if .env.local exists
const envPath = path.join(__dirname, '..', '.env.local');
console.log('1. .env.local file exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log('2. NEXT_CLAUDE_KEY in .env.local:', envContent.includes('NEXT_CLAUDE_KEY'));
  
  // Extract the key
  const match = envContent.match(/NEXT_CLAUDE_KEY=(.+)/);
  if (match) {
    const key = match[1].trim();
    console.log('3. Key format check:', {
      starts_with_sk: key.startsWith('sk-'),
      length: key.length,
      valid_format: key.startsWith('sk-ant-api03-')
    });
  }
}

// Check process.env
console.log('\n4. Process environment:');
console.log('NEXT_CLAUDE_KEY set:', !!process.env.NEXT_CLAUDE_KEY);
console.log('NEXT_CLAUDE_KEY length:', process.env.NEXT_CLAUDE_KEY?.length || 0);

// Try to manually load .env.local
console.log('\n5. Manual .env.local loading:');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n');
  
  lines.forEach(line => {
    if (line.trim() && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').trim();
      
      if (key === 'NEXT_CLAUDE_KEY') {
        console.log('Found NEXT_CLAUDE_KEY in file:', value.substring(0, 20) + '...');
        process.env.NEXT_CLAUDE_KEY = value;
      }
    }
  });
  
  console.log('After manual loading:');
  console.log('NEXT_CLAUDE_KEY set:', !!process.env.NEXT_CLAUDE_KEY);
  console.log('NEXT_CLAUDE_KEY length:', process.env.NEXT_CLAUDE_KEY?.length || 0);
}

console.log('\n=== TEST COMPLETE ===');