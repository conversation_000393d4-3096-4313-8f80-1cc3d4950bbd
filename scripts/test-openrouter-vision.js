// Test OpenRouter Vision API
require('./load-env');

const testOpenRouterVision = async () => {
  console.log('=== OPENROUTER VISION API TEST ===');
  
  const apiKey = process.env.NEXT_OR_KEY;
  
  if (!apiKey) {
    console.error('❌ NEXT_OR_KEY not found');
    return;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 10) + '...');
  
  // Test with a simple image URL
  const testRequest = {
    model: 'anthropic/claude-3.5-sonnet',
    max_tokens: 200,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'What do you see in this image? Please respond with a brief description.'
          },
          {
            type: 'image_url',
            image_url: {
              url: 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg'
            }
          }
        ]
      }
    ]
  };
  
  console.log('\n🔄 Testing OpenRouter Vision API...');
  console.log('Model:', testRequest.model);
  console.log('Image URL:', testRequest.messages[0].content[1].image_url.url);
  
  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://visaai.vercel.app',
        'X-Title': 'VisaAI Vision Test'
      },
      body: JSON.stringify(testRequest)
    });
    
    const result = await response.json();
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      console.log('✅ OpenRouter Vision API Test successful!');
      console.log('Model used:', result.model);
      console.log('Response:', result.choices[0]?.message?.content || 'No content');
      console.log('Usage:', result.usage);
    } else {
      console.error('❌ OpenRouter Vision API Test failed');
      console.error('Error:', result);
    }
    
  } catch (error) {
    console.error('❌ OpenRouter Vision API Test error:', error.message);
  }
};

testOpenRouterVision();