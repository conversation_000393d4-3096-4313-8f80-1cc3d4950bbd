// Test available OpenRouter models
require('./load-env');

const testOpenRouterModels = async () => {
  console.log('=== OPENROUTER MODELS TEST ===');
  
  const apiKey = process.env.NEXT_OR_KEY;
  
  if (!apiKey) {
    console.error('❌ NEXT_OR_KEY not found');
    return;
  }
  
  // Test different Claude models
  const modelsToTest = [
    'anthropic/claude-3.5-sonnet',
    'anthropic/claude-3-sonnet',
    'anthropic/claude-3-opus',
    'anthropic/claude-3-haiku',
    'anthropic/claude-3.5-haiku',
    'anthropic/claude-3.7-sonnet',
    'anthropic/claude-sonnet-4',
    'anthropic/claude-4-sonnet',
    'google/gemini-2.0-flash-001' // Working model for comparison
  ];
  
  const testRequest = {
    max_tokens: 50,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Hello, just respond with "OK"'
          }
        ]
      }
    ]
  };
  
  for (const model of modelsToTest) {
    console.log(`\n🔄 Testing model: ${model}`);
    
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://visaai.vercel.app',
          'X-Title': 'VisaAI Model Test'
        },
        body: JSON.stringify({
          model: model,
          ...testRequest
        })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        console.log(`✅ ${model} - WORKS`);
        console.log(`   Response: ${result.choices[0]?.message?.content || 'No content'}`);
        if (result.usage) {
          console.log(`   Usage: ${result.usage.total_tokens} tokens`);
        }
      } else {
        console.log(`❌ ${model} - FAILED`);
        console.log(`   Error: ${result.error?.message || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log(`❌ ${model} - ERROR`);
      console.log(`   Error: ${error.message}`);
    }
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n=== TESTING COMPLETE ===');
};

testOpenRouterModels();