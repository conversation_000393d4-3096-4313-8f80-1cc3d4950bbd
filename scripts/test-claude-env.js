// Load environment variables
require('./load-env');

console.log('=== CLAUDE ENVIRONMENT TEST ===');

// Check environment variables
console.log('\n1. Environment Variables:');
console.log('NEXT_CLAUDE_KEY set:', !!process.env.NEXT_CLAUDE_KEY);
console.log('NEXT_CLAUDE_KEY length:', process.env.NEXT_CLAUDE_KEY?.length || 0);

// Check if key looks valid (should start with sk- and be long enough)
if (process.env.NEXT_CLAUDE_KEY) {
  const key = process.env.NEXT_CLAUDE_KEY;
  console.log('Key starts with correct prefix:', key.startsWith('sk-'));
  console.log('Key length adequate:', key.length > 50);
  
  if (key.startsWith('sk-') && key.length > 50) {
    console.log('✅ Claude API key appears to be valid format');
  } else {
    console.log('❌ Claude API key format may be invalid');
  }
} else {
  console.log('❌ NEXT_CLAUDE_KEY not found');
  console.log('📝 Please set NEXT_CLAUDE_KEY in your .env.local file');
}

console.log('\n2. Integration Status:');
if (process.env.NEXT_CLAUDE_KEY) {
  console.log('✅ Claude integration will be ENABLED');
  console.log('🔄 OCR priority: Claude > Mistral > Tesseract');
} else {
  console.log('❌ Claude integration will be DISABLED');
  console.log('🔄 OCR priority: Mistral > Tesseract');
}

console.log('\n=== TEST COMPLETE ===');