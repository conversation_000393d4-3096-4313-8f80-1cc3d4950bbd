// Test Claude API connectivity
require('./load-env');

const testClaudeAPI = async () => {
  console.log('=== CLAUDE API TEST ===');
  
  const apiKey = process.env.NEXT_CLAUDE_KEY;
  
  if (!apiKey) {
    console.error('❌ NEXT_CLAUDE_KEY not found');
    return;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 20) + '...');
  console.log('✅ Key format:', apiKey.startsWith('sk-ant-api03-') ? 'Valid' : 'Invalid');
  
  // Test simple text request
  const testRequest = {
    model: 'claude-3-5-sonnet-20241022',
    max_tokens: 100,
    messages: [
      {
        role: 'user',
        content: 'Hello, please respond with "API test successful"'
      }
    ]
  };
  
  console.log('\n🔄 Testing API connectivity...');
  
  try {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': api<PERSON>ey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(testRequest)
    });
    
    const result = await response.json();
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      console.log('✅ API Test successful!');
      console.log('Response:', result.content[0]?.text || 'No content');
    } else {
      console.error('❌ API Test failed');
      console.error('Error:', result);
    }
    
  } catch (error) {
    console.error('❌ API Test error:', error.message);
  }
};

testClaudeAPI();