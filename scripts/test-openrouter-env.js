// Load environment variables
require('./load-env');

console.log('=== OPENROUTER ENVIRONMENT TEST ===');

// Check environment variables
console.log('\n1. Environment Variables:');
console.log('NEXT_OR_KEY set:', !!process.env.NEXT_OR_KEY);
console.log('NEXT_OR_KEY length:', process.env.NEXT_OR_KEY?.length || 0);

// Check if key looks valid (OpenRouter keys typically start with sk-)
if (process.env.NEXT_OR_KEY) {
  const key = process.env.NEXT_OR_KEY;
  console.log('Key starts with sk-:', key.startsWith('sk-'));
  console.log('Key length adequate:', key.length > 20);
  
  if (key.startsWith('sk-') && key.length > 20) {
    console.log('✅ OpenRouter API key appears to be valid format');
  } else {
    console.log('❌ OpenRouter API key format may be invalid');
    console.log('💡 OpenRouter keys should start with "sk-" and be sufficiently long');
  }
} else {
  console.log('❌ NEXT_OR_KEY not found');
  console.log('📝 Please set NEXT_OR_KEY in your .env.local file');
}

console.log('\n2. Model Configuration:');
console.log('NEXT_OR_MODEL set:', !!process.env.NEXT_OR_MODEL);
console.log('NEXT_OR_MODEL value:', process.env.NEXT_OR_MODEL || 'default (anthropic/claude-3.5-sonnet)');

console.log('\n3. Integration Status:');
if (process.env.NEXT_OR_KEY) {
  console.log('✅ OpenRouter integration will be ENABLED');
  console.log('🔄 OCR priority: OpenRouter > Claude > Mistral > Tesseract');
  console.log('🤖 Model:', process.env.NEXT_OR_MODEL || 'anthropic/claude-3.5-sonnet');
} else {
  console.log('❌ OpenRouter integration will be DISABLED');
  console.log('🔄 OCR priority: Claude > Mistral > Tesseract');
}

console.log('\n4. Other OCR Engines:');
console.log('Claude (NEXT_CLAUDE_KEY):', !!process.env.NEXT_CLAUDE_KEY);
console.log('Mistral (MISTRAL_API_KEY):', !!process.env.MISTRAL_API_KEY);

console.log('\n=== TEST COMPLETE ===');