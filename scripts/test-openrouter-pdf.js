// Test OpenRouter PDF processing
require('./load-env');
const fs = require('fs');
const path = require('path');

const testOpenRouterPDF = async () => {
  console.log('=== OPENROUTER PDF PROCESSING TEST ===');
  
  const apiKey = process.env.NEXT_OR_KEY;
  
  if (!apiKey) {
    console.error('❌ NEXT_OR_KEY not found');
    return;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 10) + '...');
  
  // Test PDF URL you provided
  const pdfUrl = 'https://jobktdnksbweojxfqvcs.supabase.co/storage/v1/object/public/temp-ocr-files/temp/1752061323975/13925d8a-984f-427e-a572-753c27b20f2a.pdf';
  
  console.log('\n📄 Testing PDF URL:', pdfUrl);
  
  // Download PDF first
  console.log('📥 Downloading PDF...');
  const pdfResponse = await fetch(pdfUrl);
  if (!pdfResponse.ok) {
    console.error('❌ Failed to download PDF:', pdfResponse.status);
    return;
  }
  
  const pdfBuffer = await pdfResponse.arrayBuffer();
  const base64PDF = Buffer.from(pdfBuffer).toString('base64');
  const pdfDataUrl = `data:application/pdf;base64,${base64PDF}`;
  
  console.log('✅ PDF downloaded and converted to base64');
  console.log('📏 Base64 length:', base64PDF.length);
  
  // Test with different engines
  const engines = ['native', 'mistral-ocr', 'pdf-text'];
  
  for (const engine of engines) {
    console.log(`\n🔄 Testing with engine: ${engine}`);
    
    const testRequest = {
      model: process.env.NEXT_OR_MODEL || 'anthropic/claude-3.5-sonnet',
      max_tokens: 2000,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `Проанализируй это изображение паспорта или ID документа и извлеки следующие данные.
Верни результат строго в формате JSON без дополнительных комментариев:

{
  "name": "имя ТОЛЬКО в латинице (если найдено)",
  "surname": "фамилия ТОЛЬКО в латинице (если найдено)", 
  "dateOfBirth": "дата рождения в формате DD.MM.YYYY",
  "passportIssueDate": "дата выдачи в формате DD.MM.YYYY",
  "passportExpiryDate": "дата окончания в формате DD.MM.YYYY",
  "passportNumber": "номер паспорта",
  "nationality": "национальность/гражданство"
}`
            },
            {
              type: 'file',
              file: {
                filename: 'passport.pdf',
                file_data: pdfDataUrl
              }
            }
          ]
        }
      ],
      plugins: [
        {
          id: 'file-parser',
          pdf: {
            engine: engine
          }
        }
      ]
    };
    
    try {
      const startTime = Date.now();
      
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://visaai.vercel.app',
          'X-Title': 'VisaAI PDF Test'
        },
        body: JSON.stringify(testRequest)
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = await response.json();
      
      console.log(`⏱️  Response time: ${duration}ms`);
      console.log('📊 Response status:', response.status);
      
      if (response.ok) {
        console.log(`✅ ${engine} - SUCCESS`);
        console.log('🤖 Model used:', result.model);
        console.log('📝 Response:', result.choices[0]?.message?.content?.substring(0, 200) + '...');
        console.log('💰 Usage:', result.usage);
        
        // Try to parse JSON from response
        try {
          const content = result.choices[0]?.message?.content || '';
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const jsonData = JSON.parse(jsonMatch[0]);
            console.log('📋 Parsed data:', jsonData);
          }
        } catch (parseError) {
          console.log('⚠️  Could not parse JSON from response');
        }
      } else {
        console.log(`❌ ${engine} - FAILED`);
        console.log('🔍 Error:', result.error);
        console.log('📄 Full response:', result);
      }
      
    } catch (error) {
      console.log(`💥 ${engine} - ERROR`);
      console.log('🔍 Error:', error.message);
    }
    
    console.log('─'.repeat(50));
  }
  
  console.log('\n=== PDF TESTING COMPLETE ===');
};

// Check credits first
const checkCredits = async () => {
  const apiKey = process.env.NEXT_OR_KEY;
  if (!apiKey) return;
  
  try {
    const response = await fetch('https://openrouter.ai/api/v1/credits', {
      headers: { 'Authorization': `Bearer ${apiKey}` }
    });
    const credits = await response.json();
    console.log('💰 Current credits:', credits);
  } catch (error) {
    console.log('⚠️  Could not check credits:', error.message);
  }
};

// Run tests
checkCredits().then(() => testOpenRouterPDF());