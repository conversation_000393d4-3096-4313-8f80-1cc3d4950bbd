-- Phone Number Formatting Migration
-- This migration normalizes all phone numbers in the visa_applications table
-- to the standard Kazakhstan format: +7XXXXXXXXXX

-- Create a function to normalize phone numbers
CREATE OR REPLACE FUNCTION normalize_phone_number(phone_input TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Return empty string if input is null or empty
    IF phone_input IS NULL OR phone_input = '' THEN
        RETURN '';
    END IF;
    
    -- Remove all non-digit characters except +
    phone_input := regexp_replace(phone_input, '[^\d+]', '', 'g');
    
    -- Remove + if present
    phone_input := regexp_replace(phone_input, '^\+', '');
    
    -- If phone starts with 8, replace with 7
    IF phone_input ~ '^8' THEN
        phone_input := '7' || substring(phone_input from 2);
    END IF;
    
    -- Handle different cases based on length and starting digits
    IF phone_input ~ '^7' AND length(phone_input) = 11 THEN
        -- Already has Kazakhstan country code and correct length (7XXXXXXXXXX)
        -- Do nothing, it's already correct
    ELSIF phone_input ~ '^7' AND length(phone_input) = 10 THEN
        -- This is a local Kazakhstan number starting with 7 (7XXXXXXXXX)
        -- Add country code 7 to make it 77XXXXXXXXX
        phone_input := '7' || phone_input;
    ELSIF length(phone_input) = 10 THEN
        -- This is a local Kazakhstan number without country code (XXXXXXXXXX)
        -- Add country code 7
        phone_input := '7' || phone_input;
    ELSIF phone_input ~ '^7' AND length(phone_input) > 11 THEN
        -- Too many digits, truncate to 11
        phone_input := substring(phone_input from 1 for 11);
    ELSIF length(phone_input) > 10 THEN
        -- Too many digits for local number, add 7 and truncate
        phone_input := '7' || substring(phone_input from 1 for 10);
    ELSE
        -- Default case: add Kazakhstan country code if less than 10 digits
        phone_input := '7' || phone_input;
    END IF;
    
    -- Add + prefix and return
    RETURN '+' || phone_input;
END;
$$ LANGUAGE plpgsql;

-- Update all phone numbers in visa_applications table
UPDATE visa_applications 
SET phone_number = normalize_phone_number(phone_number)
WHERE phone_number IS NOT NULL 
  AND phone_number != ''
  AND phone_number != '+7 XXX XXX XXXX'; -- Skip placeholder values

-- Log the changes for verification
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % phone numbers in visa_applications table', updated_count;
END $$;

-- Show sample of updated phone numbers for verification
SELECT 
    'Sample of normalized phone numbers:' as info,
    phone_number,
    COUNT(*) as count
FROM visa_applications 
WHERE phone_number IS NOT NULL 
  AND phone_number != ''
  AND phone_number != '+7 XXX XXX XXXX'
GROUP BY phone_number 
ORDER BY count DESC 
LIMIT 10;

-- Clean up the function (optional - remove if you want to keep it for future use)
-- DROP FUNCTION IF EXISTS normalize_phone_number(TEXT); 