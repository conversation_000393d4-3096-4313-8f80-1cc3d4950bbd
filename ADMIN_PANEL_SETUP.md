# Visa AI Admin Panel Setup Guide

## Environment Variables

Create a `.env.local` file in your project root with the following variables:

```bash
# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Optional: Production URL
# NEXT_PUBLIC_BASE_URL=https://yourdomain.com
```

## Database Setup

1. **Apply the Schema Update:**
   - Go to your Supabase Dashboard
   - Navigate to SQL Editor
   - Copy and paste the contents of `supabase-schema-update.sql`
   - Run the SQL to create all necessary tables and sample data

2. **Verify Tables Created:**
   - `companies` - Company management
   - `employees` - Employee/admin user management  
   - `price_list` - Service packages
   - `visa_applications` - Updated with new status fields

## Default Login Credentials

After running the schema update, you can login with these sample accounts:

### Super Admin
- **Email:** `<EMAIL>`
- **Password:** `admin123` (development only)

### Visa Admin (Global Visa Services)
- **Email:** `<EMAIL>`
- **Password:** `admin123` (development only)

### Visa Admin (Premium Immigration)
- **Email:** `<EMAIL>`
- **Password:** `admin123` (development only)

## Features Implemented

### ✅ Completed Features

1. **Company Management (Super Admin Only)**
   - Create, edit, delete companies
   - Automatic creation of visa admin accounts
   - Integration with Supabase Auth

2. **Employee Management**
   - Visa Admins can manage employees under their company
   - Three roles: `super_admin`, `visa_admin`, `employee`
   - Supabase Auth integration with proper metadata

3. **Applications Display**
   - All applications visible to all admins (as requested)
   - Advanced filtering by payment status, visa status, etc.
   - Status management with proper database fields

4. **Dashboard Analytics**
   - Total clients (completed questionnaire)
   - Paid clients
   - Submitted applications  
   - Total revenue calculation
   - Breakdown by region, profession, salary, case type
   - Monthly submission trends

5. **Status Management**
   - `service_payment_status`: 'оплачено' / 'не оплачено'
   - `visa_status`: 'ожидает' / 'одобрено' / 'отклонено' / 'подано'
   - `client_progress_status`: Custom status descriptions
   - `requires_fixes`: Boolean flag for manual fixes needed
   - `fix_comment`: Comments for required fixes

6. **Special Sections**
   - "Ожидается оплата": Clients who completed questionnaire but haven't paid
   - "Требуются правки": Clients with `requires_fixes = true`

7. **Add Client Form**
   - Manual client entry with validation
   - Phone number normalization
   - Duplicate client detection
   - Automatic form link generation
   - Company and package assignment

8. **Price List Management**
   - Visa Admins can manage their company's packages
   - CRUD operations with validation
   - Integration with applications

## API Endpoints

### Authentication
- `POST /api/admin/auth` - Login with email/password

### Companies (Super Admin)
- `GET /api/admin/companies` - List all companies
- `POST /api/admin/companies` - Create company with admin user
- `PUT /api/admin/companies` - Update company
- `DELETE /api/admin/companies` - Delete company

### Employees
- `GET /api/admin/employees?companyId=X` - List employees
- `POST /api/admin/employees` - Create employee
- `PUT /api/admin/employees` - Update employee
- `DELETE /api/admin/employees` - Delete employee

### Applications
- `GET /api/admin/applications` - List applications with filters
- `PUT /api/admin/applications` - Update application status

### Analytics
- `GET /api/admin/analytics` - Dashboard metrics and data

### Price List
- `GET /api/admin/price-list?companyId=X` - List packages
- `POST /api/admin/price-list` - Create package
- `PUT /api/admin/price-list` - Update package
- `DELETE /api/admin/price-list` - Delete package

### Add Client
- `POST /api/admin/add-client` - Manually add client

## User Roles & Permissions

### Super Admin
- Full access to all features
- Company management
- View all applications across companies
- System-wide analytics

### Visa Admin
- Manage employees within their company
- Manage price list for their company
- View and manage applications
- Company-specific analytics

### Employee
- View and manage applications
- Basic dashboard access
- No administrative functions

## Development Notes

1. **Password Hashing:** Production ready with bcrypt
2. **Supabase Auth Integration:** Full integration with user metadata
3. **Transaction Safety:** Rollback mechanisms for failed operations
4. **Error Handling:** Comprehensive error handling and logging
5. **Type Safety:** Full TypeScript implementation

## Running the Application

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables in `.env.local`

3. Apply database schema (see Database Setup above)

4. Run development server:
   ```bash
   npm run dev
   ```

5. Access admin panel:
   ```
   http://localhost:3000/admin/login
   ```

## Security Notes

- Service role key is only used server-side for admin operations
- Row Level Security (RLS) policies are enabled
- Password hashing with bcrypt
- Session management with localStorage (development)
- For production, consider implementing proper JWT tokens

## Troubleshooting

### Common Issues:

1. **"Cannot find module 'bcryptjs'"**
   - Run: `npm install bcryptjs @types/bcryptjs`

2. **Supabase connection errors**
   - Verify environment variables are correct
   - Check Supabase project URL and keys

3. **Authentication not working**
   - Ensure database schema is applied
   - Check if sample users exist in employees table

4. **Applications not showing**
   - Verify visa_applications table has new status columns
   - Check if applications exist in database

For additional help, check the console for detailed error messages. 