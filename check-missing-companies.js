const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkMissingCompanies() {
  console.log('🔍 Checking for missing companies and applications...\n');

  try {
    // 1. Check existing companies
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('id, name, slug, is_blocked')
      .order('created_at', { ascending: false });

    if (companiesError) {
      console.error('❌ Error fetching companies:', companiesError);
      return;
    }

    console.log('📋 Existing companies:');
    companies.forEach(company => {
      console.log(`  - ${company.name} (slug: ${company.slug}, id: ${company.id}, blocked: ${company.is_blocked})`);
    });

    // 2. Check if visa-pro-kz and 123 exist
    const targetSlugs = ['visa-pro-kz', '123'];
    const existingSlugs = companies.map(c => c.slug);
    const missingSlugs = targetSlugs.filter(slug => !existingSlugs.includes(slug));

    if (missingSlugs.length > 0) {
      console.log(`\n⚠️  Missing companies with slugs: ${missingSlugs.join(', ')}`);
    } else {
      console.log('\n✅ All target companies exist');
    }

    // 3. Check applications with null company_id
    const { data: nullCompanyApps, error: nullError } = await supabase
      .from('visa_applications')
      .select('id, agent_id, form_data, company_id, created_at')
      .is('company_id', null)
      .order('created_at', { ascending: false })
      .limit(10);

    if (nullError) {
      console.error('❌ Error fetching null company applications:', nullError);
    } else {
      console.log(`\n📊 Applications with null company_id: ${nullCompanyApps.length}`);
      if (nullCompanyApps.length > 0) {
        console.log('Recent examples:');
        nullCompanyApps.forEach(app => {
          const name = `${app.form_data?.name || ''} ${app.form_data?.surname || ''}`.trim() || 'Unknown';
          console.log(`  - ${name} (${app.agent_id}) - ${new Date(app.created_at).toLocaleDateString()}`);
        });
      }
    }

    // 4. Check total applications by company
    const { data: appsByCompany, error: statsError } = await supabase
      .from('visa_applications')
      .select('company_id')
      .not('company_id', 'is', null);

    if (statsError) {
      console.error('❌ Error fetching application stats:', statsError);
    } else {
      const companyStats = {};
      appsByCompany.forEach(app => {
        companyStats[app.company_id] = (companyStats[app.company_id] || 0) + 1;
      });

      console.log('\n📈 Applications by company:');
      for (const [companyId, count] of Object.entries(companyStats)) {
        const company = companies.find(c => c.id === companyId);
        const companyName = company ? company.name : `Unknown (${companyId})`;
        console.log(`  - ${companyName}: ${count} applications`);
      }
    }

    // 5. Check recent applications (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: recentApps, error: recentError } = await supabase
      .from('visa_applications')
      .select('id, company_id, created_at, form_data')
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: false });

    if (recentError) {
      console.error('❌ Error fetching recent applications:', recentError);
    } else {
      console.log(`\n🕐 Recent applications (last 30 days): ${recentApps.length}`);
      
      const recentByCompany = {};
      recentApps.forEach(app => {
        const key = app.company_id || 'null';
        recentByCompany[key] = (recentByCompany[key] || 0) + 1;
      });

      for (const [companyId, count] of Object.entries(recentByCompany)) {
        if (companyId === 'null') {
          console.log(`  - No company: ${count} applications`);
        } else {
          const company = companies.find(c => c.id === companyId);
          const companyName = company ? company.name : `Unknown (${companyId})`;
          console.log(`  - ${companyName}: ${count} applications`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the check
checkMissingCompanies();