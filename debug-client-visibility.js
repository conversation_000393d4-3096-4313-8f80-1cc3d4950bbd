const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://oevdmwqpaagkyhaknubf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ldmRtd3FwYWFna3loYWtudWJmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjM0NjY3NDIsImV4cCI6MjAzOTA0Mjc0Mn0.kSbJpUvrxltUO7jBmTiHfmMEiJD2Wlf6LGKel-xZneM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugClientVisibility() {
  console.log('🔍 Debugging client visibility issue...\n');
  
  const clientId = '18ac5fc1-5a62-443a-87ee-47449d8bd28e';
  const companyId = '550e8400-e29b-41d4-a716-446655440001';
  
  // 1. Fetch the specific client
  const { data: client, error: clientError } = await supabase
    .from('visa_applications')
    .select('*')
    .eq('id', clientId)
    .single();
    
  if (clientError) {
    console.error('Error fetching client:', clientError);
    return;
  }
  
  console.log('📋 Client details:');
  console.log('ID:', client.id);
  console.log('Company ID:', client.company_id);
  console.log('client_progress_status:', client.client_progress_status);
  console.log('service_payment_status:', client.service_payment_status);
  console.log('visa_status:', client.visa_status);
  console.log('created_at:', client.created_at);
  console.log('Form data country:', client.form_data?.visaCountry || client.form_data?.country);
  console.log('\n');
  
  // 2. Check what the API would return for this company
  console.log('🔍 Checking API query simulation...\n');
  
  // Query without date filter
  const { data: allCompanyApps, error: allError, count: allCount } = await supabase
    .from('visa_applications')
    .select('id, client_progress_status, service_payment_status', { count: 'exact' })
    .eq('company_id', companyId)
    .order('created_at', { ascending: false });
    
  if (allError) {
    console.error('Error fetching all company apps:', allError);
  } else {
    console.log(`Total applications for company: ${allCount}`);
    
    // Check if our client is in the results
    const clientInResults = allCompanyApps.some(app => app.id === clientId);
    console.log(`Client ${clientId} is in results: ${clientInResults}`);
    
    if (clientInResults) {
      const clientData = allCompanyApps.find(app => app.id === clientId);
      console.log('Client data from query:', clientData);
    }
  }
  
  // 3. Check with date filters
  console.log('\n🔍 Checking with date filters...\n');
  
  const dateFrom = '2025-06-01';
  const dateTo = new Date().toISOString();
  
  const { data: filteredApps, count: filteredCount } = await supabase
    .from('visa_applications')
    .select('id, client_progress_status, service_payment_status, created_at', { count: 'exact' })
    .eq('company_id', companyId)
    .gte('created_at', dateFrom)
    .lte('created_at', dateTo)
    .order('created_at', { ascending: false });
    
  console.log(`Applications with date filter (${dateFrom} to ${dateTo}): ${filteredCount}`);
  
  const clientInFilteredResults = filteredApps?.some(app => app.id === clientId) || false;
  console.log(`Client ${clientId} is in filtered results: ${clientInFilteredResults}`);
  
  // 4. Check case sensitivity of status
  console.log('\n🔍 Checking status variations...\n');
  
  const { data: statusVariations } = await supabase
    .from('visa_applications')
    .select('client_progress_status')
    .eq('company_id', companyId)
    .in('client_progress_status', ['Прошли опросник', 'прошли опросник', 'ПРОШЛИ ОПРОСНИК']);
    
  const uniqueStatuses = [...new Set(statusVariations?.map(app => app.client_progress_status) || [])];
  console.log('Unique client_progress_status variations found:', uniqueStatuses);
  
  // 5. Check column mapping
  console.log('\n🔍 Checking Trello board column mapping...\n');
  
  const progressToStepMap = {
    'Прошли опросник': 1,
    'Оплатили пакет услуг': 2,
    'Сбор информации ботом': 3,
    'Ожидает приглашения': 4,
    'Согласование кейса': 5,
    'Заполнение анкеты + обучение': 6,
    'Подано': 7
  };
  
  const clientStatus = client.client_progress_status;
  const mappedStep = progressToStepMap[clientStatus];
  
  console.log(`Client status "${clientStatus}" maps to step: ${mappedStep || 'NOT FOUND'}`);
  
  if (!mappedStep && clientStatus) {
    console.log('\n⚠️  Status not found in mapping!');
    console.log('Checking if lowercase matches...');
    const lowercaseMatch = Object.keys(progressToStepMap).find(key => 
      key.toLowerCase() === clientStatus.toLowerCase()
    );
    console.log('Lowercase match found:', lowercaseMatch || 'NONE');
  }
  
  // 6. Check if client appears in step 1 column
  console.log('\n🔍 Checking Step 1 (Прошли опросник) column...\n');
  
  const { data: step1Clients } = await supabase
    .from('visa_applications')
    .select('id, client_progress_status, service_payment_status')
    .eq('company_id', companyId)
    .eq('service_payment_status', 'не оплачено')
    .or('client_progress_status.eq.Прошли опросник,client_progress_status.is.null')
    .limit(10);
    
  console.log('Sample Step 1 clients:', step1Clients?.length || 0);
  const clientInStep1 = step1Clients?.some(app => app.id === clientId) || false;
  console.log(`Client ${clientId} would appear in Step 1: ${clientInStep1}`);
  
  process.exit(0);
}

debugClientVisibility().catch(console.error);