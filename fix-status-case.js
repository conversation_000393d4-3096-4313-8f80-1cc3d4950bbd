const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixStatusCase() {
  console.log('🔧 Fixing status case sensitivity issues...\n');
  
  // Update all lowercase "прошли опросник" to proper case
  const { data, error } = await supabase
    .from('visa_applications')
    .update({
      client_progress_status: 'Прошли опросник'
    })
    .eq('client_progress_status', 'прошли опросник')
    .select();
    
  if (error) {
    console.error('❌ Error updating:', error);
    return;
  }
  
  console.log(`✅ Successfully updated ${data.length} records from "прошли опросник" to "Прошли опросник"`);
  
  // Show updated counts
  const { data: apps, error: countError } = await supabase
    .from('visa_applications')
    .select('client_progress_status')
    .in('client_progress_status', ['Прошли опросник', 'прошли опросник']);
    
  if (!countError) {
    const counts = {};
    apps.forEach(app => {
      counts[app.client_progress_status] = (counts[app.client_progress_status] || 0) + 1;
    });
    
    console.log('\n📊 Updated Status Counts:');
    Object.entries(counts).forEach(([status, count]) => {
      console.log(`   - ${status}: ${count}`);
    });
  }
}

fixStatusCase().catch(console.error);