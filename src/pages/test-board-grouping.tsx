import React, { useState } from 'react';
import Head from 'next/head';

interface TestResult {
  totalClients: number;
  tableViewClients: number;
  boardViewGroups: { [key: string]: number };
  statusDistribution: { [key: string]: number };
  missingStatusClients: any[];
  paidPackageClients: any[];
  discrepancy: {
    tableVsBoard: number;
    description: string;
  };
}

export default function TestBoardGrouping() {
  const [result, setResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const runTest = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/test/board-grouping');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Test failed');
      }
      
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Board Grouping Test</title>
      </Head>
      
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">Board Grouping Test</h1>
            
            <button
              onClick={runTest}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Running Test...' : 'Run Board Grouping Test'}
            </button>

            {error && (
              <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <strong>Error:</strong> {error}
              </div>
            )}

            {result && (
              <div className="mt-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-100 p-4 rounded">
                    <h3 className="font-semibold text-blue-800">Total Clients</h3>
                    <p className="text-2xl font-bold text-blue-900">{result.totalClients}</p>
                  </div>
                  <div className="bg-green-100 p-4 rounded">
                    <h3 className="font-semibold text-green-800">Table View (US)</h3>
                    <p className="text-2xl font-bold text-green-900">{result.tableViewClients}</p>
                  </div>
                  <div className="bg-purple-100 p-4 rounded">
                    <h3 className="font-semibold text-purple-800">Board View Total</h3>
                    <p className="text-2xl font-bold text-purple-900">
                      {Object.values(result.boardViewGroups).reduce((a, b) => a + b, 0)}
                    </p>
                  </div>
                </div>

                <div className={`p-4 rounded ${result.discrepancy.tableVsBoard === 0 ? 'bg-green-100 border border-green-400' : 'bg-red-100 border border-red-400'}`}>
                  <h3 className={`font-semibold ${result.discrepancy.tableVsBoard === 0 ? 'text-green-800' : 'text-red-800'}`}>
                    Discrepancy Check
                  </h3>
                  <p className={result.discrepancy.tableVsBoard === 0 ? 'text-green-700' : 'text-red-700'}>
                    {result.discrepancy.description}
                  </p>
                </div>

                <div className="bg-gray-100 p-4 rounded">
                  <h3 className="font-semibold text-gray-800 mb-3">Board View Groups</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {Object.entries(result.boardViewGroups).map(([step, count]) => (
                      <div key={step} className="flex justify-between py-1">
                        <span className="text-sm text-gray-600">{step}:</span>
                        <span className="font-semibold">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-yellow-100 p-4 rounded">
                  <h3 className="font-semibold text-yellow-800 mb-3">Status Distribution</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {Object.entries(result.statusDistribution).map(([status, count]) => (
                      <div key={status} className="flex justify-between py-1">
                        <span className="text-sm text-gray-600">{status || 'null/undefined'}:</span>
                        <span className="font-semibold">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-orange-100 p-4 rounded">
                  <h3 className="font-semibold text-orange-800 mb-3">Paid Package Clients ({result.paidPackageClients.length})</h3>
                  {result.paidPackageClients.length > 0 ? (
                    <div className="space-y-1">
                      {result.paidPackageClients.slice(0, 5).map((client, index) => (
                        <div key={index} className="text-sm">
                          {client.name} (ID: {client.id.slice(0, 8)}...)
                        </div>
                      ))}
                      {result.paidPackageClients.length > 5 && (
                        <div className="text-sm text-gray-600">
                          ... and {result.paidPackageClients.length - 5} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-600">No paid package clients found</p>
                  )}
                </div>

                <div className="bg-red-100 p-4 rounded">
                  <h3 className="font-semibold text-red-800 mb-3">Missing Status Clients ({result.missingStatusClients.length})</h3>
                  {result.missingStatusClients.length > 0 ? (
                    <div className="space-y-1">
                      {result.missingStatusClients.slice(0, 5).map((client, index) => (
                        <div key={index} className="text-sm">
                          {client.name} (ID: {client.id.slice(0, 8)}...) - Step: {client.step_status}, Paid: {client.is_paid ? 'Yes' : 'No'}
                        </div>
                      ))}
                      {result.missingStatusClients.length > 5 && (
                        <div className="text-sm text-gray-600">
                          ... and {result.missingStatusClients.length - 5} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-600">All clients have progress status</p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}