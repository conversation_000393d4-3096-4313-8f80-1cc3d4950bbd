import React, { useRef } from 'react';
import Step6_ContactInfo, { Step6Ref } from '../components/Step6_ContactInfo';
import { Step6Data } from '../utils/types';

export default function TestStep6() {
  const step6Ref = useRef<Step6Ref>(null);

  const initialValues: Step6Data = {
    address: '',
    city: '',
    stateProvince: '',
    country: '',
    zipCode: '',
    phone: '+7 ',
    email: '',
    socialMediaLinks: [],
  };

  const handleSubmit = (values: Step6Data) => {
    console.log('Form submitted:', values);
    alert('Form is valid and submitted!');
  };

  const checkValidation = () => {
    console.log('Current validation state:', step6Ref.current?.isValid);
    alert(`Form is ${step6Ref.current?.isValid ? 'VALID' : 'INVALID'}`);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">Step 6 Validation Test</h1>
      
      <div className="mb-4">
        <button 
          onClick={checkValidation}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Check Validation State
        </button>
      </div>

      <Step6_ContactInfo
        ref={step6Ref}
        initialValues={initialValues}
        onSubmit={handleSubmit}
      />

      <div className="mt-6">
        <button
          onClick={() => step6Ref.current?.submitForm()}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Submit Form
        </button>
      </div>
    </div>
  );
} 