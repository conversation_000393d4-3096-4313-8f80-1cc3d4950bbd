import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import { isFeatureEnabled } from '../config/featureFlags';

import StepWrapper from '../components/StepWrapper';
import Step1_Welcome, { Step1Ref } from '../components/Step1_Welcome';
import Step2_DocumentUpload, { Step2Ref } from '../components/Step2_DocumentUpload';
import Step3_PersonalInfo, { Step3Ref } from '../components/Step3_PersonalInfo';
import Step4_TravelPurpose, { Step4Ref } from '../components/Step4_TravelPurpose';
import Step5_VisaHistory, { Step5Ref } from '../components/Step5_VisaHistory';
import Step6_ContactInfo, { Step6Ref } from '../components/Step6_ContactInfo';
import Step7_FamilyInfo, { Step7Ref } from '../components/Step7_FamilyInfo';
import Step8_EducationWork, { Step8Ref } from '../components/Step8_EducationWork';
import Step9_TravelHistory, { Step9Ref } from '../components/Step9_TravelHistory';

import { saveFormData, getOrCreateUserData, markWhatsappRedirected, testSupabaseConnection } from '../utils/supabase';
import {
  getOrCreateAgentId,
  getCurrentStep,
  setCurrentStep,
  isLocalStorageAvailable
} from '../utils/localStorage';
import ThankYouPage from '../components/ThankYouPage';
import {
  VisaFormData,
  Step1Data,
  Step2Data,
  Step3Data,
  Step4Data,
  Step5Data,
  Step6Data,
  Step7Data,
  Step8Data,
  Step9Data
} from '../utils/types';
import { Company } from '../types/admin';
import { supabase } from '../utils/supabase';

const totalSteps = 9;

interface CompanyFormPageProps {
  company: Company;
}

export default function CompanyFormPage({ company }: CompanyFormPageProps) {
  const [step, setStep] = useState(1);
  const [agentId, setAgentId] = useState('');
  const [isFormCompleted, setIsFormCompleted] = useState(false);
  const [, setIsNewUser] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [validationTrigger, setValidationTrigger] = useState(0);
  const [paymentStatus, setPaymentStatus] = useState<{ isPaid: boolean; companyName: string }>({ isPaid: false, companyName: 'Visa Pro' });

  // Refs for step components
  const step1Ref = useRef<Step1Ref>(null);
  const step2Ref = useRef<Step2Ref>(null);
  const step3Ref = useRef<Step3Ref>(null);
  const step4Ref = useRef<Step4Ref>(null);
  const step5Ref = useRef<Step5Ref>(null);
  const step6Ref = useRef<Step6Ref>(null);
  const step7Ref = useRef<Step7Ref>(null);
  const step8Ref = useRef<Step8Ref>(null);
  const step9Ref = useRef<Step9Ref>(null);
  const [formData, setFormData] = useState<VisaFormData>({
    // Step 1
    visaDestination: '',

    // Step 2
    surname: '',
    name: '',
    dateOfBirth: '',
    citizenship: '',
    passportNumber: '',
    passportIssueDate: '',
    passportExpiryDate: '',
    iin: '',
    idNumber: '',

    // Step 3
    fullNameCyrillic: '',
    hasOtherNames: false,
    otherNames: '',
    gender: '',
    maritalStatus: '',
    cityOfBirth: '',
    countryOfBirth: '',
    hasOtherCitizenship: false,
    otherCitizenship: '',
    isPermanentResidentOtherCountry: false,
    permanentResidenceCountry: '',
    nationality: '',
    hasSSN: false,
    ssn: '',
    hasTaxpayerId: false,
    taxpayerId: '',

    // Step 4
    hasOwnTravelPurpose: false,
    travelPurposeDescription: '',
    departureDate: '',
    returnDate: '',
    destination: '',
    hasInvitation: false,
    invitationFile: '',
    travelWithOthers: false,
    travelAsGroup: false,
    groupName: '',
    companions: [],

    // Step 5
    hasBeenToUSA: false,
    hasUSVisa: false,
    lastVisaDate: '',
    visaNumber: '',
    isSameVisaType: false,
    isSameCountry: false,
    hasVisaRejections: false,
    rejectionVisaType: '',
    rejectionDate: '',
    visaRejections: [],
    hasPreviousDS160: false,
    previousDS160File: '',

    // Step 6
    address: '',
    city: '',
    stateProvince: '',
    zipCode: '',
    phone: '',
    email: '',
    socialMediaLinks: [],

    // Step 7
    hasSpouse: false,
    spouseLastName: '',
    spouseFirstName: '',
    spouseMiddleName: '',
    spouseCityOfBirth: '',
    spouseCountryOfBirth: '',
    spouseDateOfBirth: '',
    spouseCitizenship: '',
    wasSpouseInUSA: false,
    spouseUSAEntryDate: '',
    spouseUSAStayDuration: '',
    spouseUSAStayDurationType: '',
    fatherSurname: '',
    fatherName: '',
    fatherDateOfBirth: '',
    isFatherDateOfBirthUnknown: false,
    isFatherInUSA: false,
    fatherUSAReason: '',
    motherSurname: '',
    motherName: '',
    motherDateOfBirth: '',
    isMotherDateOfBirthUnknown: false,
    isMotherInUSA: false,
    motherUSAReason: '',
    hasRelativesInUSA: false,
    relatives: [],

    // Step 8
    occupation: '',
    educationStatus: '',
    educationLocation: '',
    lastCompletedEducation: '',
    otherEducation: '',
    isCurrentStudent: false,
    hasJob: false,
    hasBusiness: false,
    companyName: '',
    position: '',
    workAddress: '',
    workAddress2: '',
    workCity: '',
    workState: '',
    workStateNA: false,
    workZipCode: '',
    workZipCodeNA: false,
    workCountry: '',
    workPhone: '',
    workExperience: '',
    income: '',
    incomeNA: false,
    duties: '',
    institutionName: '',
    institutionAddress1: '',
    institutionAddress2: '',
    institutionCity: '',
    institutionState: '',
    institutionStateNA: false,
    institutionZipCode: '',
    institutionZipCodeNA: false,
    institutionCountry: '',
    courseOfStudy: '',
    attendanceFrom: '',
    attendanceTo: '',
    universityName: '',
    universityAddress: '',
    faculty: '',
    startDate: '',
    endDate: '',
    hasStudentJob: false,
    hasStateGrant: false,
    additionalStudentInfo: '',
    businessType: '',
    businessName: '',
    businessRegistrationType: '',
    businessRegistrationNumber: '',
    businessRegistrationDate: '',
    businessActivity: '',
    monthlyBusinessIncome: '',
    hasEmployees: false,
    employeeCount: 0,
    businessStatus: '',
    businessAddress: '',
    businessWebsite: '',

    // Step 9
    visitedCountries: [],
  });

  // Initialize form data and loading state
  const initializeFormData = useCallback(async () => {
    if (typeof window === 'undefined') return;

    try {
      await testSupabaseConnection();
      
      const currentAgentId = getOrCreateAgentId();
      setAgentId(currentAgentId);

      if (isLocalStorageAvailable()) {
        const savedStep = getCurrentStep();
        if (savedStep >= 1 && savedStep <= totalSteps) {
          setStep(savedStep);
        }
      }

      // Get or create user data
      const { data: userData, isNewUser } = await getOrCreateUserData(currentAgentId);
      setIsNewUser(isNewUser);

      if (userData && typeof userData === 'object' && 'form_data' in userData && userData.form_data) {
        setFormData(userData.form_data as VisaFormData);
      }

      if (userData && typeof userData === 'object' && 'step_status' in userData && userData.step_status && userData.step_status >= 1 && userData.step_status <= totalSteps) {
        setStep(userData.step_status);
        if (isLocalStorageAvailable()) {
          setCurrentStep(userData.step_status);
        }
      }

      if (userData && typeof userData === 'object' && 'step_status' in userData && userData.step_status === 10) {
        setIsFormCompleted(true);
      }

      // Handle WhatsApp redirection if needed
      const urlParams = new URLSearchParams(window.location.search);
      const fromWhatsapp = urlParams.get('from_whatsapp');
      if (fromWhatsapp === 'true' && currentAgentId) {
        await markWhatsappRedirected(currentAgentId);
      }
    } catch (error) {
      console.error('Error initializing form data:', error);
      setErrorMessage('Произошла ошибка при загрузке данных. Пожалуйста, обновите страницу.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    initializeFormData();
  }, [initializeFormData]);

  // Триггер обновления валидации с дебаунсом при изменении формы или шага
  useEffect(() => {
    const timer = setTimeout(() => {
      setValidationTrigger(prev => prev + 1);
    }, 300); // Дебаунс 300ms для обновления валидации

    return () => clearTimeout(timer);
  }, [formData, step]);


  // Auto-save functionality
  const autoSaveData = useCallback(async () => {
    if (!agentId) return;

    setIsAutoSaving(true);
    try {
      await saveFormData(agentId, formData, step, company.id, uploadedFiles);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsAutoSaving(false);
    }
  }, [agentId, formData, step, company.id, uploadedFiles]);

  // Trigger auto-save when form data changes
  useEffect(() => {
    if (!agentId) return; // Don't auto-save until we have an agent ID

    // Clear existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // Set new timeout for auto-save
    autoSaveTimeoutRef.current = setTimeout(() => {
      autoSaveData();
    }, 2000); // Auto-save after 2 seconds of inactivity

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [formData, autoSaveData, agentId]);

  const updateFormData = useCallback((newData: Partial<VisaFormData>) => {
    setFormData(prev => ({ ...prev, ...newData }));
  }, []);

  const handleNext = async () => {
    const currentStepSubmitFunction = getCurrentStepSubmitFunction();
    
    if (currentStepSubmitFunction) {
      try {
        currentStepSubmitFunction();
        
        // Always proceed to next step if no error thrown
        if (step < totalSteps) {
          setStep(step + 1);
          if (isLocalStorageAvailable()) {
            setCurrentStep(step + 1);
          }
        } else if (step === totalSteps) {
          setIsFormCompleted(true);
          const updatedFormData = { ...formData };
          await saveFormData(agentId, updatedFormData, 10, company.id, uploadedFiles);
          if (isLocalStorageAvailable()) {
            setCurrentStep(10);
          }
        }
      } catch (error) {
        console.error('Error in step submission:', error);
        setErrorMessage('Пожалуйста, заполните все обязательные поля');
      }
    } else {
      // Fallback: if no submit function, just go to next step
      if (step < totalSteps) {
        setStep(step + 1);
        if (isLocalStorageAvailable()) {
          setCurrentStep(step + 1);
        }
      }
    }
  };

  const handlePrev = () => {
    if (step > 1) {
      setStep(step - 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step - 1);
      }
    }
  };

  // Helper functions to get current step's form submission and validation
  const getCurrentStepSubmitFunction = () => {
    switch (step) {
      case 1:
        return step1Ref.current?.submitForm;
      case 2:
        return step2Ref.current?.submitForm;
      case 3:
        return step3Ref.current?.submitForm;
      case 4:
        return step4Ref.current?.submitForm;
      case 5:
        return step5Ref.current?.submitForm;
      case 6:
        return step6Ref.current?.submitForm;
      case 7:
        return step7Ref.current?.submitForm;
      case 8:
        return step8Ref.current?.submitForm;
      case 9:
        return step9Ref.current?.submitForm;
      default:
        return null;
    }
  };

  const getCurrentStepFormValidity = useCallback(() => {
    if (step < 1 || step > 9) return false;
    
    const stepRefs = [null, step1Ref, step2Ref, step3Ref, step4Ref, step5Ref, step6Ref, step7Ref, step8Ref, step9Ref];
    const ref = stepRefs[step];
    const isValid = ref?.current?.isValid ?? false;
    
    // Временное логирование для отладки шага 3
    if (step === 3) {
      console.log('[Main] Step 3 validation:', {
        isValid,
        refExists: !!ref?.current,
        ref: step3Ref.current
      });
    }
    
    return isValid;
  }, [step, step1Ref, step2Ref, step3Ref, step4Ref, step5Ref, step6Ref, step7Ref, step8Ref, step9Ref, formData]);

  // Step handlers with company association
  const handleStep1Submit = async (stepData: Step1Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const handleStep2Submit = async (stepData: Step2Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const handleStep3Submit = async (stepData: Step3Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const handleStep4Submit = async (stepData: Step4Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const handleStep5Submit = async (stepData: Step5Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const handleStep6Submit = async (stepData: Step6Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const handleStep7Submit = async (stepData: Step7Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const handleStep8Submit = async (stepData: Step8Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, step + 1, company.id, uploadedFiles);
      setStep(step + 1);
      if (isLocalStorageAvailable()) {
        setCurrentStep(step + 1);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробuйте еще раз.');
      return false;
    }
  };

  const handleStep9Submit = async (stepData: Step9Data) => {
    try {
      updateFormData(stepData);
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(agentId, updatedFormData, 10, company.id, uploadedFiles);
      
      // Get payment status from database only if feature is enabled
      if (isFeatureEnabled('CHECK_PAYMENT_STATUS')) {
        try {
          const { data: applicationData, error } = await supabase
            .from('visa_applications')
            .select('service_payment_status, is_paid')
            .eq('agent_id', agentId)
            .single();

          if (!error && applicationData) {
            const isPaid = applicationData.service_payment_status === 'оплачено' || applicationData.is_paid;
            setPaymentStatus({ isPaid, companyName: company.name });
          }
        } catch (error) {
          console.error('Error fetching payment status:', error);
        }
      } else {
        // When feature is disabled, always set payment status as not paid (default behavior)
        setPaymentStatus({ isPaid: false, companyName: company.name });
      }
      
      // Send WhatsApp message after form completion
      if (updatedFormData.phone) {
        try {
          const response = await fetch('/api/send-whatsapp', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              phone: updatedFormData.phone,
              agentId: agentId 
            })
          });

          const result = await response.json();
          if (!result.success) {
            console.error('Failed to send WhatsApp message:', result.error);
          }
        } catch (error) {
          console.error('Error sending WhatsApp message:', error);
        }
      }

      setIsFormCompleted(true);
      if (isLocalStorageAvailable()) {
        setCurrentStep(10);
      }
      return true;
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Ошибка при сохранении данных. Попробуйте еще раз.');
      return false;
    }
  };

  const getStepTitle = () => {
    const titles = [
      'Добро пожаловать',
      'Загрузка документов',
      'Личная информация',
      'Цель поездки',
      'История виз',
      'Контактная информация',
      'Информация о семье',
      'Образование и работа',
      'История поездок'
    ];
    return titles[step - 1] || '';
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <Step1_Welcome
            ref={step1Ref}
            initialValues={{ visaDestination: formData.visaDestination }}
            onSubmit={handleStep1Submit}
          />
        );
      case 2:
        return (
          <Step2_DocumentUpload
            ref={step2Ref}
            initialValues={{
              surname: formData.surname,
              name: formData.name,
              dateOfBirth: formData.dateOfBirth,
              citizenship: formData.citizenship,
              passportNumber: formData.passportNumber,
              passportIssueDate: formData.passportIssueDate,
              passportExpiryDate: formData.passportExpiryDate,
              iin: formData.iin,
              idNumber: formData.idNumber,
            }}
            onSubmit={handleStep2Submit}
            uploadedFiles={uploadedFiles}
            setUploadedFiles={setUploadedFiles}
            onFormDataUpdate={updateFormData}
          />
        );
      case 3:
        return (
          <Step3_PersonalInfo
            ref={step3Ref}
            initialValues={{
              fullNameCyrillic: formData.fullNameCyrillic || '',
              hasOtherNames: formData.hasOtherNames || false,
              otherNames: formData.otherNames || '',
              gender: formData.gender || '',
              maritalStatus: formData.maritalStatus || '',
              cityOfBirth: formData.cityOfBirth || '',
              countryOfBirth: formData.countryOfBirth || '',
              hasOtherCitizenship: formData.hasOtherCitizenship || false,
              otherCitizenship: formData.otherCitizenship || '',
              isPermanentResidentOtherCountry: formData.isPermanentResidentOtherCountry || false,
              permanentResidenceCountry: formData.permanentResidenceCountry || '',
              nationality: formData.nationality || '',
            }}
            onSubmit={handleStep3Submit}
          />
        );
      case 4:
        return (
          <Step4_TravelPurpose
            ref={step4Ref}
            initialValues={{
              hasOwnTravelPurpose: Boolean(formData.hasOwnTravelPurpose),
              travelPurposeDescription: formData.travelPurposeDescription,
              departureDate: formData.departureDate,
              returnDate: formData.returnDate,
              destination: formData.destination,
              hasInvitation: Boolean(formData.hasInvitation),
              invitationFile: formData.invitationFile,
              travelWithOthers: Boolean(formData.travelWithOthers),
              travelAsGroup: Boolean(formData.travelAsGroup),
              groupName: formData.groupName,
              companions: formData.companions,
            }}
            onSubmit={handleStep4Submit}
          />
        );
      case 5:
        return (
          <Step5_VisaHistory
            ref={step5Ref}
            initialValues={{
              hasBeenToUSA: Boolean(formData.hasBeenToUSA),
              hasUSVisa: Boolean(formData.hasUSVisa),
              lastVisaDate: formData.lastVisaDate,
              visaNumber: formData.visaNumber,
              isSameVisaType: Boolean(formData.isSameVisaType),
              isSameCountry: Boolean(formData.isSameCountry),
              hasVisaRejections: Boolean(formData.hasVisaRejections),
              rejectionVisaType: formData.rejectionVisaType,
              rejectionDate: formData.rejectionDate,
              visaRejections: formData.visaRejections,
              hasPreviousDS160: Boolean(formData.hasPreviousDS160),
              previousDS160File: formData.previousDS160File,
            }}
            onSubmit={handleStep5Submit}
          />
        );
      case 6:
        return (
          <Step6_ContactInfo
            ref={step6Ref}
            initialValues={{
              address: formData.address || '',
              city: formData.city || '',
              stateProvince: formData.stateProvince || '',
              zipCode: formData.zipCode || '',
              country: formData.country || formData.countryOfBirth || '',
              phone: formData.phone ? String(formData.phone) : '+7 ',
              email: formData.email || '',
              socialMediaLinks: formData.socialMediaLinks || [],
            }}
            onSubmit={handleStep6Submit}
          />
        );
      case 7:
        return (
          <Step7_FamilyInfo
            ref={step7Ref}
            maritalStatus={formData.maritalStatus}
            initialValues={{
              hasSpouse: Boolean(formData.hasSpouse),
              spouseLastName: formData.spouseLastName || '',
              spouseFirstName: formData.spouseFirstName || '',
              spouseMiddleName: formData.spouseMiddleName || '',
              spouseCityOfBirth: formData.spouseCityOfBirth || '',
              spouseCountryOfBirth: formData.spouseCountryOfBirth || '',
              spouseDateOfBirth: formData.spouseDateOfBirth || '',
              spouseCitizenship: formData.spouseCitizenship || '',
              wasSpouseInUSA: Boolean(formData.wasSpouseInUSA),
              spouseUSAEntryDate: formData.spouseUSAEntryDate || '',
              spouseUSAStayDuration: formData.spouseUSAStayDuration || '',
              spouseUSAStayDurationType: formData.spouseUSAStayDurationType || '',
              fatherSurname: formData.fatherSurname || '',
              fatherName: formData.fatherName || '',
              fatherDateOfBirth: formData.fatherDateOfBirth || '',
              isFatherDateOfBirthUnknown: Boolean(formData.isFatherDateOfBirthUnknown),
              isFatherInUSA: Boolean(formData.isFatherInUSA),
              fatherUSAReason: formData.fatherUSAReason || '',
              motherSurname: formData.motherSurname || '',
              motherName: formData.motherName || '',
              motherDateOfBirth: formData.motherDateOfBirth || '',
              isMotherDateOfBirthUnknown: Boolean(formData.isMotherDateOfBirthUnknown),
              isMotherInUSA: Boolean(formData.isMotherInUSA),
              motherUSAReason: formData.motherUSAReason || '',
              hasRelativesInUSA: Boolean(formData.hasRelativesInUSA),
              relatives: Array.isArray(formData.relatives) ? formData.relatives : [],
            }}
            onSubmit={handleStep7Submit}
          />
        );
      case 8:
        return (
          <Step8_EducationWork
            ref={step8Ref}
            updateForm={updateFormData}
            initialValues={{
              occupation: formData.occupation || '',
              educationStatus: formData.educationStatus || '',
              educationLocation: formData.educationLocation || '',
              lastCompletedEducation: formData.lastCompletedEducation || '',
              otherEducation: formData.otherEducation || '',
              isCurrentStudent: formData.isCurrentStudent,
              hasJob: formData.hasJob,
              hasBusiness: formData.hasBusiness,
              companyName: formData.companyName || '',
              position: formData.position || '',
              workAddress: formData.workAddress || '',
              workAddress2: formData.workAddress2 || '',
              workCity: formData.workCity || '',
              workState: formData.workState || '',
              workStateNA: formData.workStateNA,
              workZipCode: formData.workZipCode || '',
              workZipCodeNA: formData.workZipCodeNA,
              workCountry: formData.workCountry || '',
              workPhone: formData.workPhone || '',
              workExperience: formData.workExperience || '',
              income: formData.income || '',
              incomeNA: formData.incomeNA,
              duties: formData.duties || '',
              institutionName: formData.institutionName || '',
              institutionAddress1: formData.institutionAddress1 || '',
              institutionAddress2: formData.institutionAddress2 || '',
              institutionCity: formData.institutionCity || '',
              institutionState: formData.institutionState || '',
              institutionStateNA: formData.institutionStateNA,
              institutionZipCode: formData.institutionZipCode || '',
              institutionZipCodeNA: formData.institutionZipCodeNA,
              institutionCountry: formData.institutionCountry || '',
              courseOfStudy: formData.courseOfStudy || '',
              attendanceFrom: formData.attendanceFrom || '',
              attendanceTo: formData.attendanceTo || '',
              universityName: formData.universityName || '',
              universityAddress: formData.universityAddress || '',
              faculty: formData.faculty || '',
              startDate: formData.startDate || '',
              endDate: formData.endDate || '',
              hasStudentJob: formData.hasStudentJob,
              hasStateGrant: formData.hasStateGrant,
              additionalStudentInfo: formData.additionalStudentInfo || '',
              businessType: formData.businessType || '',
              businessName: formData.businessName || '',
              businessRegistrationType: formData.businessRegistrationType || '',
              businessRegistrationNumber: formData.businessRegistrationNumber || '',
              businessRegistrationDate: formData.businessRegistrationDate || '',
              businessActivity: formData.businessActivity || '',
              monthlyBusinessIncome: formData.monthlyBusinessIncome || '',
              hasEmployees: formData.hasEmployees,
              employeeCount: formData.employeeCount,
              businessStatus: formData.businessStatus || '',
              businessAddress: formData.businessAddress || '',
              businessWebsite: formData.businessWebsite || '',
            }}
            onSubmit={handleStep8Submit}
          />
        );
      case 9:
        return (
          <Step9_TravelHistory
            ref={step9Ref}
            initialValues={{
              visitedCountries: Array.isArray(formData.travelHistory) ? formData.travelHistory.filter((item): item is string => typeof item === 'string') : [],
              hasBeenToCountry: formData.hasBeenToCountry,
              lastVisitToCountry: formData.lastVisitToCountry || '',
              lastVisitDuration: formData.lastVisitDuration || '',
              wasPreviouslyRefused: formData.wasPreviouslyRefused,
              refusalDate: formData.refusalDate || '',
              refusalReason: formData.refusalReason || '',
            }}
            onSubmit={handleStep9Submit}
          />
        );
      default:
        return null;
    }
  };

  // Мемоизируем значения валидации (должно быть до всех условных return)
  const canGoNext = useMemo(() => {
    return getCurrentStepFormValidity() ?? false;
  }, [getCurrentStepFormValidity, validationTrigger]);

  const isFormValid = useMemo(() => {
    return getCurrentStepFormValidity() ?? false;
  }, [getCurrentStepFormValidity, validationTrigger]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        <p className="ml-3 text-gray-700">Загрузка данных...</p>
      </div>
    );
  }

  // Show Thank You page if form is completed
  if (isFormCompleted) {
    return <ThankYouPage 
      agentId={agentId} 
      userPhone={formData.phone ? String(formData.phone) : ''} 
      isPaid={paymentStatus.isPaid}
      companyName={paymentStatus.companyName}
    />;
  }

  return (
    <>
      <Head>
        <title>Анкета на визу - {getStepTitle()}</title>
        <meta name="description" content="Заполните анкету для получения визы" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/visai_logo.png" />
        <link rel="apple-touch-icon" href="/visai_logo.png" />
      </Head>

      <main className="container mx-auto px-4 py-8">
        {/* Company branding header */}
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 rounded mb-4 text-sm">
          <strong>Анкета для компании:</strong> {company.name}
        </div>

        {/* Debug info - remove in production */}
        {agentId && (
          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 rounded mb-4 text-sm">
            <strong>номер заявки:</strong> {agentId}
            {isAutoSaving && (
              <span className="ml-4 text-orange-600">
                <span className="animate-pulse">●</span> Сохранение...
              </span>
            )}
            {lastSaved && !isAutoSaving && (
              <span className="ml-4 text-green-600">
                ✓ Сохранено: {lastSaved.toLocaleTimeString()}
              </span>
            )}
          </div>
        )}

        {errorMessage && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {errorMessage}
          </div>
        )}

        <StepWrapper
          title={getStepTitle()}
          step={step}
          totalSteps={totalSteps}
          onNext={handleNext}
          onPrev={handlePrev}
          onSubmitForm={getCurrentStepSubmitFunction() || undefined}
          canGoNext={canGoNext}
          isFormValid={isFormValid}
        >
          {renderStepContent()}
        </StepWrapper>
      </main>
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { slug } = context.params!;

  try {
    console.log(`[Company Route] Fetching company for slug: ${slug}`);
    
    // Method 1: Try direct Supabase connection first (more reliable)
    try {
      console.log('[Company Route] Attempting direct database fetch...');
      const { data: companyData, error: dbError } = await supabase
        .from('companies')
        .select('*')
        .eq('slug', slug)
        .eq('is_blocked', false)
        .single();

      if (!dbError && companyData) {
        console.log(`[Company Route] Direct DB fetch successful: ${companyData.name} (ID: ${companyData.id})`);
        const company: Company = {
          id: companyData.id,
          name: companyData.name,
          phone_number: companyData.phone_number,
          email: companyData.email,
          is_blocked: companyData.is_blocked,
          slug: companyData.slug,
          created_at: companyData.created_at,
          updated_at: companyData.updated_at
        };

        return {
          props: {
            company,
          },
        };
      }

      console.log('[Company Route] Direct DB fetch failed:', dbError);
    } catch (directDbError) {
      console.log('[Company Route] Direct DB connection failed:', directDbError);
    }

    // Method 2: Fallback to HTTP API call
    console.log('[Company Route] Trying HTTP API fallback...');
    
    // Enhanced URL resolution for different deployment environments
    let baseUrl: string;
    
    // Check if we're in Vercel environment
    if (process.env.VERCEL_URL) {
      baseUrl = `https://${process.env.VERCEL_URL}`;
      console.log('[Deployment] Using VERCEL_URL:', baseUrl);
    }
    // Check for explicit NEXT_PUBLIC_BASE_URL
    else if (process.env.NEXT_PUBLIC_BASE_URL) {
      baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
      console.log('[Deployment] Using NEXT_PUBLIC_BASE_URL:', baseUrl);
    }
    // Fallback to request headers
    else {
      const protocol = context.req.headers['x-forwarded-proto'] || 'http';
      const host = context.req.headers.host;
      baseUrl = `${protocol}://${host}`;
      console.log('[Deployment] Using request headers:', baseUrl);
    }

    console.log(`[Company Route] Using base URL: ${baseUrl}`);
    
    // Fetch company by slug via HTTP
    const apiUrl = `${baseUrl}/api/company/${slug}`;
    console.log(`[Company Route] Full API URL: ${apiUrl}`);
    
    const response = await fetch(apiUrl);
    
    console.log(`[Company Route] API Response Status: ${response.status}`);
    
    if (!response.ok) {
      console.error(`[Company Route] Failed to fetch company ${slug}: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.error(`[Company Route] Error details:`, errorText);
      return {
        notFound: true,
      };
    }

    const responseData = await response.json();
    console.log(`[Company Route] API Response Data:`, responseData);
    
    const { company } = responseData;

    if (!company) {
      console.error(`[Company Route] No company found for slug: ${slug}`);
      return {
        notFound: true,
      };
    }

    console.log(`[Company Route] Successfully found company: ${company.name} (ID: ${company.id})`);

    return {
      props: {
        company,
      },
    };
  } catch (error) {
    console.error('[Company Route] Error fetching company:', error);
    return {
      notFound: true,
    };
  }
}; 