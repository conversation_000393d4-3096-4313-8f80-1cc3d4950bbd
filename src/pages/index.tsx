import Head from 'next/head';
import Image from 'next/image';
import { useState, useEffect } from 'react';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);




  if (!mounted) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Анкета на визу - Получите ссылку от компании</title>
        <meta name="description" content="Для заполнения анкеты на визу необходимо получить персональную ссылку от вашей визовой компании" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/visai_logo.png" />
        <link rel="apple-touch-icon" href="/visai_logo.png" />
      </Head>

      <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
        <div className="max-w-2xl w-full">
          {/* Logo */}
          <div className="text-center mb-8">
            <Image 
              src="/visai_logo.png" 
              alt="Visa AI Logo" 
              width={64}
              height={64}
              className="mx-auto mb-4"
            />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Анкета на визу
            </h1>
            <p className="text-gray-600">
              Система онлайн подачи документов
            </p>
          </div>

          {/* Main instruction card */}
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                Требуется персональная ссылка
              </h2>
              <p className="text-lg text-gray-700 mb-6">
                Вы должны получить ссылку на опросник от вашей визовой компании
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-medium text-blue-900 mb-3">
                Как получить доступ к анкете:
              </h3>
              <ol className="text-left text-blue-800 space-y-2">
                <li className="flex items-start">
                  <span className="flex-shrink-0 h-6 w-6 flex items-center justify-center bg-blue-200 rounded-full text-sm font-medium mr-3 mt-0.5">1</span>
                  <span>Обратитесь к вашей визовой компании</span>
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 h-6 w-6 flex items-center justify-center bg-blue-200 rounded-full text-sm font-medium mr-3 mt-0.5">2</span>
                  <span>Получите персональную ссылку для заполнения анкеты</span>
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 h-6 w-6 flex items-center justify-center bg-blue-200 rounded-full text-sm font-medium mr-3 mt-0.5">3</span>
                  <span>Перейдите по полученной ссылке для начала работы</span>
                </li>
              </ol>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">
                <strong>Примечание:</strong> Каждая визовая компания предоставляет уникальную ссылку для своих клиентов. 
                Это обеспечивает правильное ведение документооборота и связь заявки с конкретной компанией.
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              © 2024 Visa AI. Все права защищены.
            </p>
          </div>
        </div>
      </main>
    </>
  );
}