import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useTranslation } from '../../utils/localization';
import { getAdminSession } from '../../utils/adminAuth';

export default function AdminIndex() {
  const router = useRouter();
  const { t } = useTranslation();

  useEffect(() => {
    const session = getAdminSession();
    if (session) {
      router.push('/admin/dashboard');
    } else {
      router.push('/admin/login');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-xl text-gray-600">{t('common.loading')}</div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      // No server-side translations needed with custom localization
    },
  };
} 