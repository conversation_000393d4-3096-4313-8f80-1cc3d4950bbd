import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useTranslation } from '../../../src/utils/localization';
import { getAdminSession, logoutAdmin, requireSuperAdmin } from '../../utils/adminAuth';
import { Admin, AnalyticsData, VisaApplicationWithStatus, SearchFilters } from '../../types/admin';
import Sidebar from '../../components/admin/Sidebar';
import DashboardOverviewNew from '../../components/admin/DashboardOverviewNew';
import ClientManagement from '../../components/admin/ClientManagement';
import StatisticsDashboard from '../../components/admin/StatisticsDashboard';
import CompanyManagement from '../../components/admin/CompanyManagement';
import EmployeeManagement from '../../components/admin/EmployeeManagement';
import PriceListManagement from '../../components/admin/PriceListManagement';
import Settings from '../../components/admin/Settings';
import { ToastContainer } from '../../components/common/Toast';
import { useToast } from '../../hooks/useToast';
import LanguageSwitcher from '../../components/common/LanguageSwitcher';
import CompanyRouteDisplay from '../../components/common/CompanyRouteDisplay';
import { DateFilterProvider, useDateFilter } from '../../contexts/DateFilterContext';
import { CompanyFilterProvider, useCompanyFilter } from '../../contexts/CompanyFilterContext';
import GlobalDateFilter from '../../components/admin/GlobalDateFilter';
import { DashboardOverviewProps, StatisticsDashboardProps } from '../../types/admin';

export type DashboardTab = 'glavnoe' | 'clients' | 'statistics' | 'companies' | 'employees' | 'price-list' | 'settings';

function DashboardContent() {
  const router = useRouter();
  const { t } = useTranslation();
  const { toasts, removeToast } = useToast();
  const { dateRange, ensureIncludesNewClients } = useDateFilter();
  const { companyId, setCompanyId } = useCompanyFilter();
  const [admin, setAdmin] = useState<Omit<Admin, 'password'> | null>(null);
  const [activeTab, setActiveTab] = useState<DashboardTab>('glavnoe');
  const [hasInitialized, setHasInitialized] = useState(false);
  const [applications, setApplications] = useState<VisaApplicationWithStatus[]>([]);
  const [totalApplications, setTotalApplications] = useState(0);
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalApplications: 0,
    filteredTotalApplications: 0,
    acceptedApplications: 0,
    rejectedApplications: 0,
    pendingApplications: 0,
    regionBreakdown: {},
    professionBreakdown: {},
    salaryBreakdown: {},
    caseTypeBreakdown: {},
    ageBreakdown: {},
    countryBreakdown: {},
    genderBreakdown: {},
    professionSuccessRate: {},
    incomeApprovalCorrelation: {},
    rejectionReasons: {},
    monthlySubmissions: [],
    allCountriesBreakdown: {},
    pendingPayments: [],
    requiresFixes: [],
    metrics: {
      totalClients: 0,
      paidClients: 0,
      submittedApplications: 0,
      totalRevenue: 0
    }
  });
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [focusClientId, setFocusClientId] = useState<string | null>(null);
  const [companySlug, setCompanySlug] = useState<string | null>(null);
  const [companies, setCompanies] = useState<Array<{ id: string; name: string; is_blocked: boolean }>>([]);
  const [companyLoading, setCompanyLoading] = useState(false);
  
  // Tab titles mapping
  const tabTitles = {
    glavnoe: t('navigation.dashboard'),
    clients: t('clients.title'),
    statistics: t('navigation.statistics'),
    companies: t('companies.title'),
    employees: t('navigation.employees'),
    'price-list': t('navigation.priceList'),
    settings: t('navigation.settings'),
  };

  useEffect(() => {
    const session = getAdminSession();
    if (!session) {
      router.push('/admin/login');
      return;
    }
    setAdmin(session);
  }, [router]);

  // Initialize tab from localStorage after component mounts
  useEffect(() => {
    if (!hasInitialized && typeof window !== 'undefined') {
      const savedTab = localStorage.getItem('admin_active_tab');
      console.log('Loading saved tab from localStorage on mount:', savedTab);
      if (savedTab && ['glavnoe', 'clients', 'statistics', 'companies', 'employees', 'price-list', 'settings'].includes(savedTab)) {
        console.log('Setting initial tab from localStorage:', savedTab);
        setActiveTab(savedTab as DashboardTab);
      }
      setHasInitialized(true);
    }
  }, [hasInitialized]);

  // Clean up URL parameters on initial load if no explicit tab is set
  useEffect(() => {
    if (router.isReady && typeof window !== 'undefined') {
      const { tab, focus, country } = router.query;
      
      // If there are focus/country parameters but no explicit tab parameter, 
      // and this is likely a page reload (not an intentional navigation),
      // clean up the URL to prevent unwanted navigation
      if (!tab && (focus || country)) {
        const url = new URL(window.location.href);
        url.searchParams.delete('focus');
        url.searchParams.delete('country');
        router.replace(url.pathname, undefined, { shallow: true });
      }
    }
  }, [router.isReady]);

  // Handle URL parameters separately to prevent infinite redirects
  useEffect(() => {
    if (!hasInitialized) return; // Wait for localStorage initialization
    
    const { tab, focus, country } = router.query;
    console.log('URL params changed:', { tab, focus, country, currentActiveTab: activeTab, hasInitialized });
    
    // Only set tab from URL if explicitly provided
    if (tab && typeof tab === 'string' && tab !== activeTab) {
      console.log('Setting tab from URL:', tab);
      setActiveTab(tab as DashboardTab);
    }
    
    if (focus && typeof focus === 'string' && focus !== focusClientId) {
      setFocusClientId(focus);
    }
    
    if (country && typeof country === 'string' && country !== searchFilters.country) {
      setSearchFilters(prev => ({
        ...prev,
        country: country
      }));
    }
  }, [router.query, hasInitialized]);

  // Save active tab to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      console.log('Saving tab to localStorage:', activeTab);
      localStorage.setItem('admin_active_tab', activeTab);
    }
  }, [activeTab]);

  // Fetch company slug for visa admins and managers
  useEffect(() => {
    const fetchCompanySlug = async () => {
      if (admin && admin.companyId && admin.role !== 'super_admin') {
        try {
          const response = await fetch(`/api/admin/companies?companyId=${admin.companyId}`);
          const data = await response.json();
          
          if (response.ok && data.companies && data.companies.length > 0) {
            const slug = data.companies[0].slug || null;
            setCompanySlug(slug);
          }
        } catch (error) {
          console.error('Error fetching company slug:', error);
        }
      }
    };
    
    if (admin && admin.companyId) {
      fetchCompanySlug();
    }
  }, [admin]);

  // Fetch companies for superadmin dropdown
  useEffect(() => {
    const fetchCompanies = async () => {
      if (admin?.role === 'super_admin') {
        try {
          const response = await fetch('/api/admin/companies');
          const data = await response.json();
          if (response.ok) {
            // Filter out blocked companies for the dropdown
            const activeCompanies = (data.companies || []).filter(
              (company: { is_blocked: boolean }) => !company.is_blocked
            );
            setCompanies(activeCompanies);
          }
        } catch (error) {
          console.error('Error fetching companies:', error);
        }
      }
    };
    
    if (admin?.role === 'super_admin') {
      fetchCompanies();
    }
  }, [admin?.role]);

  // Reset selected company if it becomes blocked
  useEffect(() => {
    if (companyId !== 'all') {
      const selectedCompanyExists = companies.some(company => company.id === companyId);
      if (!selectedCompanyExists) {
        setCompanyId('all');
      }
    }
  }, [companies, companyId, setCompanyId]);

  // Focus is now handled directly in ClientManagement component

  const handleLogout = () => {
    logoutAdmin();
    router.push('/admin/login');
  };

  const fetchApplications = useCallback(async (overrideCompanyId?: string) => {
    try {
      const params = new URLSearchParams();
      const effectiveCompanyId = overrideCompanyId ?? (admin?.role === 'super_admin' ? companyId : admin?.companyId);
      // Apply company filtering for non-super admin users OR when superadmin selects a specific company
      if (admin?.companyId && admin?.role !== 'super_admin') {
        params.append('companyId', admin.companyId);
      } else if (admin?.role === 'super_admin') {
        params.append('companyId', effectiveCompanyId || 'all');
      }
      
      // Add global date filter for all admin roles
      if (dateRange) {
        params.append('dateFrom', dateRange.from);
        params.append('dateTo', dateRange.to);
      }
      
      // Add country filter from URL if present
      const countryFromUrl = router.query.country as string;
      if (countryFromUrl) {
        params.append('country', countryFromUrl);
      }
      
      Object.entries(searchFilters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const url = `/api/admin/applications?${params}&_t=${Date.now()}`;
      console.log('Fetching applications from:', url);
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (response.ok) {
        console.log('Applications fetched successfully:', {
          total: data.total,
          applicationsCount: data.applications.length,
          step2Count: data.applications.filter((app: any) => app.step_status === 2).length
        });
        setApplications(data.applications);
        setTotalApplications(data.total || data.applications.length);
      } else {
        console.error('Failed to fetch applications:', data);
      }
    } catch (error) {
      console.error('Ошибка при загрузке заявок:', error);
    }
  }, [admin, companyId, dateRange, router.query.country, searchFilters]);

  const fetchAnalytics = useCallback(async (overrideCompanyId?: string) => {
    try {
      const params = new URLSearchParams();
      const effectiveCompanyId = overrideCompanyId ?? (admin?.role === 'super_admin' ? companyId : admin?.companyId);
      // Apply company filtering for non-super admin users OR when superadmin selects a specific company
      if (admin?.companyId && admin?.role !== 'super_admin') {
        params.append('companyId', admin.companyId);
      } else if (admin?.role === 'super_admin') {
        params.append('companyId', effectiveCompanyId || 'all');
      }
      
      // Add global date filter for all admin roles
      if (dateRange) {
        params.append('dateFrom', dateRange.from);
        params.append('dateTo', dateRange.to);
      }
      
      // Add country filter from URL if present
      const countryFromUrl = router.query.country as string;
      if (countryFromUrl) {
        params.append('country', countryFromUrl);
      }

      const response = await fetch(`/api/admin/analytics?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setAnalytics(data);
      }
    } catch (error) {
      console.error('Ошибка при загрузке аналитики:', error);
    }
  }, [admin, companyId, dateRange, router.query.country]);

  // Memoize refetchAll to prevent unnecessary recreations
  const refetchAll = useCallback(async (overrideCompanyId?: string, ensureNewClientsVisible = false) => {
    if (!admin) return; // Guard against fetching without admin session
    
    console.log('refetchAll called with overrideCompanyId:', overrideCompanyId, 'ensureNewClientsVisible:', ensureNewClientsVisible);
    
    // If we need to ensure new clients are visible, expand date range first
    if (ensureNewClientsVisible) {
      ensureIncludesNewClients();
    }
    
    setCompanyLoading(true);
    try {
      await Promise.all([
        fetchApplications(overrideCompanyId),
        fetchAnalytics(overrideCompanyId)
      ]);
      console.log('refetchAll completed successfully');
    } catch (error) {
      console.error('refetchAll failed:', error);
    } finally {
      setCompanyLoading(false);
    }
  }, [admin, fetchApplications, fetchAnalytics, ensureIncludesNewClients]);

  // Single effect to handle all data fetching
  useEffect(() => {
    if (admin) {
      refetchAll();
    }
  }, [admin, companyId, dateRange, refetchAll]);

  // Initial data fetch
  useEffect(() => {
    if (admin) {
      // Ensure date filter includes all clients on initial load
      ensureIncludesNewClients();
      refetchAll();
    }
  }, [admin, refetchAll]);

  // Ensure date filter is properly set when component mounts
  useEffect(() => {
    // Call ensureIncludesNewClients on mount to expand restrictive date filters
    ensureIncludesNewClients();
  }, []);

  // Clear focus parameters when navigating away from clients tab
  useEffect(() => {
    if (activeTab !== 'clients' && typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (url.searchParams.has('focus') || url.searchParams.has('country')) {
        url.searchParams.delete('focus');
        url.searchParams.delete('country');
        window.history.replaceState({}, '', url.toString());
      }
    }
  }, [activeTab]);

  const updateApplicationStatus = async (applicationId: string, status: string) => {
    try {
      const response = await fetch('/api/admin/applications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ applicationId, status }),
      });

      if (response.ok) {
        await refetchAll();
      }
    } catch (error) {
      console.error('Ошибка при обновлении статуса заявки:', error);
    }
  };

  // Modified company dropdown handler to only update state
  const handleCompanyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newCompanyId = e.target.value;
    setCompanyId(newCompanyId);
  };

  if (!admin) {
    return null;
  }

  const isSuperAdmin = requireSuperAdmin(admin);
  const isCompanyUser = admin.role === 'visa_admin' || admin.role === 'manager';
  
  return (
    <>
      <Head>
        <title>
          {tabTitles[activeTab]} - Visa AI Admin
        </title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/visai_logo.png" />
        <link rel="apple-touch-icon" href="/visai_logo.png" />
      </Head>
      
      <div className="flex h-screen bg-gray-50">
        <Sidebar
          activeTab={activeTab}
          onTabChange={setActiveTab}
          admin={admin}
          onLogout={handleLogout}
          isSuperAdmin={isSuperAdmin}
        />
        
        <div className="flex-1 flex flex-col overflow-hidden ml-64">
          {/* Header */}
          <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-semibold text-gray-900">
                  {tabTitles[activeTab]}
                </h1>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* Global Company Filter - only for superadmin */}
                {isSuperAdmin && (
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700">
                      {t('statistics.company')}:
                    </label>
                    <div className="relative inline-block">
                      <select
                        value={companyId}
                        onChange={handleCompanyChange}
                        className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm min-w-[160px]"
                        disabled={companyLoading}
                      >
                        <option value="all">{t('statistics.allCompanies')}</option>
                        {companies.map((company) => (
                          <option key={company.id} value={company.id}>
                            {company.name}
                          </option>
                        ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Global Date Filter - for all admin roles */}
                <GlobalDateFilter />
                
                {/* Company Route Display - only for company users */}
                {isCompanyUser && companySlug && (
                  <CompanyRouteDisplay slug={companySlug} className="mr-2" />
                )}
                
                {/* Language Switcher */}
                <LanguageSwitcher 
                  className="" 
                  showFlags={true} 
                  showText={true}
                />
                
                {/* User Info */}
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>{admin.username}</span>
                  <span className="text-gray-400">•</span>
                  <span className="capitalize">{admin.role.replace('_', ' ')}</span>
                </div>
              </div>
            </div>
          </header>
          
          <main className="flex-1 overflow-y-auto">
            <div className="py-6">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {activeTab === 'glavnoe' && (
                  <DashboardOverviewNew 
                    admin={admin}
                    analytics={analytics}
                    onRefresh={refetchAll}
                    selectedGlobalCompany={companyId}
                    metricsLoading={companyLoading}
                  />
                )}
                
                {activeTab === 'clients' && (
                  <ClientManagement
                    applications={applications}
                    onUpdateStatus={updateApplicationStatus}
                    searchFilters={searchFilters}
                    onSearchFiltersChange={setSearchFilters}
                    isLoading={companyLoading}
                    isSuperAdmin={admin?.role === 'super_admin'}
                    companyId={companyId}
                    onRefresh={refetchAll}
                    onRefreshWithNewClients={() => refetchAll(undefined, true)}
                    focusClientId={focusClientId}
                    selectedCountryFromUrl={searchFilters.country}
                    userRole={admin?.role}
                    totalApplications={totalApplications}
                  />
                )}
                
                {activeTab === 'statistics' && analytics && (
                  <StatisticsDashboard 
                    analytics={analytics}
                    onRefresh={fetchAnalytics}
                    admin={admin}
                    selectedGlobalCompany={companyId}
                    metricsLoading={companyLoading}
                  />
                )}
                
                {activeTab === 'companies' && (
                  <CompanyManagement 
                    admin={admin}
                    isSuperAdmin={isSuperAdmin}
                  />
                )}
                
                {activeTab === 'employees' && admin.companyId && (
                  <EmployeeManagement 
                    companyId={admin.companyId}
                    userRole={admin.role}
                  />
                )}
                
                {activeTab === 'price-list' && (
                  <PriceListManagement 
                    companyId={admin.companyId}
                  />
                )}
                
                {activeTab === 'settings' && (
                  <Settings 
                    admin={admin}
                  />
                )}
              </div>
            </div>
          </main>
        </div>
      </div>
      
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </>
  );
}

export default function AdminDashboard() {
  return (
    <CompanyFilterProvider>
      <DateFilterProvider>
        <DashboardContent />
      </DateFilterProvider>
    </CompanyFilterProvider>
  );
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      // No server-side translations needed with custom localization
    },
  };
} 