import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { loginAdmin, getAdminSession } from '../../utils/adminAuth';

export default function AdminLogin() {
  const router = useRouter();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockType, setBlockType] = useState<'user' | 'company' | null>(null);

  useEffect(() => {
    // Redirect if already logged in
    const session = getAdminSession();
    if (session) {
      router.push('/admin/dashboard');
    }
  }, [router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsBlocked(false);
    setBlockType(null);
    setLoading(true);

    try {
      await loginAdmin(username, password);
      router.push('/admin/dashboard');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Ошибка входа';
      setError(errorMessage);
      
      // Check if it's a blocking error
      if (errorMessage.includes('заблокирован')) {
        setIsBlocked(true);
        if (errorMessage.includes('аккаунт был заблокирован')) {
          setBlockType('user');
        } else if (errorMessage.includes('Компания') && errorMessage.includes('заблокирована')) {
          setBlockType('company');
        }
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Вход администратора - Visa Form</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/visai_logo.png" />
        <link rel="apple-touch-icon" href="/visai_logo.png" />
      </Head>
      
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Вход администратора
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Доступ к панели администратора
            </p>
          </div>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                  Логин
                </label>
                <div className="mt-1">
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Введите логин"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Пароль
                </label>
                <div className="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Введите пароль"
                  />
                </div>
              </div>

              {error && (
                <div className={`rounded-md p-4 ${isBlocked ? (blockType === 'user' ? 'bg-red-50' : 'bg-orange-50') : 'bg-red-50'}`}>
                  <div className="flex">
                    <div className="flex-shrink-0">
                      {isBlocked ? (
                        blockType === 'user' ? (
                          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        )
                      ) : (
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <div className="ml-3">
                      <h3 className={`text-sm font-medium ${
                        isBlocked 
                          ? (blockType === 'user' ? 'text-red-800' : 'text-orange-800')
                          : 'text-red-800'
                      }`}>
                        {isBlocked 
                          ? (blockType === 'user' ? '🚫 Аккаунт заблокирован' : '⚠️ Компания заблокирована')
                          : 'Ошибка входа'
                        }
                      </h3>
                      <div className={`mt-2 text-sm ${
                        isBlocked 
                          ? (blockType === 'user' ? 'text-red-700' : 'text-orange-700')
                          : 'text-red-700'
                      }`}>
                        {error}
                      </div>
                      {isBlocked && (
                        <div className={`mt-3 text-sm ${blockType === 'user' ? 'text-red-700' : 'text-orange-700'}`}>
                          {blockType === 'user' ? (
                            <div>
                              <p className="font-medium">Ваш аккаунт заблокирован</p>
                              <p className="mt-1">Возможные причины:</p>
                              <ul className="mt-1 list-disc list-inside space-y-1">
                                <li>Нарушение правил использования системы</li>
                                <li>Неавторизованный доступ</li>
                                <li>Административное решение</li>
                              </ul>
                              <p className="mt-2 font-medium">Обратитесь к администратору компании или технической поддержке.</p>
                            </div>
                          ) : (
                            <div>
                              <p className="font-medium">Компания временно заблокирована</p>
                              <p className="mt-1">Для получения дополнительной информации свяжитесь с технической поддержкой:</p>
                              <ul className="mt-1 list-disc list-inside">
                                <li>Email: <EMAIL></li>
                                <li>Телефон: +7 (XXX) XXX-XX-XX</li>
                              </ul>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <div>
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {loading ? 'Вход...' : 'Войти'}
                </button>
              </div>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 