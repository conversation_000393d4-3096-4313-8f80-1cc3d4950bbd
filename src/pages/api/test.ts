import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('Test API called with method:', req.method);
  
  if (req.method === 'GET') {
    return res.status(200).json({ 
      success: true, 
      message: 'Test API is working',
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
  
  if (req.method === 'POST') {
    return res.status(200).json({ 
      success: true, 
      message: 'Test API POST is working',
      method: req.method,
      body: req.body,
      timestamp: new Date().toISOString()
    });
  }
  
  return res.status(405).json({ 
    success: false, 
    error: 'Method not allowed',
    allowedMethods: ['GET', 'POST']
  });
}
