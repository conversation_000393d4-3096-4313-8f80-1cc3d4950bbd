import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { CLIENT_PROGRESS_STATUS } from '../../../types/client-status';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    return await handleStatusCheck(req, res);
  } else if (req.method === 'POST') {
    return await handleStatusFix(req, res);
  } else {
    return res.status(405).json({ error: 'Method not allowed' });
  }
}

async function handleStatusCheck(req: NextApiRequest, res: NextApiResponse) {
  try {
    console.log('=== TESTING PAID CLIENT LOGIC ===');
    
    // 1. Check recent paid clients in database
    const { data: paidClients, error: paidError } = await supabase
      .from('visa_applications')
      .select(`
        id,
        agent_id,
        step_status,
        client_progress_status,
        service_payment_status,
        is_paid,
        added_by_manager,
        form_data,
        created_at,
        company_id
      `)
      .eq('client_progress_status', CLIENT_PROGRESS_STATUS.PAID_PACKAGE)
      .order('created_at', { ascending: false })
      .limit(10);

    if (paidError) {
      console.error('Error fetching paid clients:', paidError);
      return res.status(500).json({ error: 'Database error' });
    }

    console.log('Found paid package clients:', paidClients?.length || 0);
    
    // 2. Check analytics data
    const { data: analyticsData, error: analyticsError } = await supabase
      .from('visa_applications')
      .select(`
        id,
        step_status,
        service_payment_status,
        is_paid,
        visa_status,
        client_progress_status,
        created_at
      `)
      .order('created_at', { ascending: false })
      .limit(50);

    if (analyticsError) {
      console.error('Error fetching analytics data:', analyticsError);
      return res.status(500).json({ error: 'Analytics error' });
    }

    // 3. Calculate metrics like analytics-new.ts does
    const totalClients = analyticsData?.filter(app => 
      app.step_status >= 9 || 
      app.is_paid === true || 
      (app.client_progress_status && [
        'Оплатили пакет услуг',
        'Сбор информации ботом', 
        'Ожидает приглашения',
        'Согласование кейса',
        'Заполнение анкеты + обучение',
        'Подано'
      ].includes(app.client_progress_status))
    ).length || 0;
    
    const paidClientsCount = analyticsData?.filter(app => app.is_paid === true).length || 0;
    const submittedApplications = analyticsData?.filter(app => app.visa_status === 'подано').length || 0;

    // 4. Analyze step_status distribution
    const stepStatusDistribution = analyticsData?.reduce((acc, app) => {
      const step = app.step_status;
      acc[step] = (acc[step] || 0) + 1;
      return acc;
    }, {} as Record<number, number>) || {};

    // 5. Analyze client_progress_status distribution
    const progressStatusDistribution = analyticsData?.reduce((acc, app) => {
      const status = app.client_progress_status || 'null';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // 6. Check for problematic statuses that need fixing
    const problematicStatuses = analyticsData?.filter(app => 
      app.client_progress_status && 
      app.client_progress_status.toLowerCase().includes('оплатил') &&
      app.client_progress_status !== CLIENT_PROGRESS_STATUS.PAID_PACKAGE
    ) || [];

    const testResults = {
      database_check: {
        paid_package_clients: paidClients?.length || 0,
        recent_paid_clients: paidClients?.map(client => ({
          id: client.id,
          agent_id: client.agent_id,
          step_status: client.step_status,
          client_progress_status: client.client_progress_status,
          service_payment_status: client.service_payment_status,
          is_paid: client.is_paid,
          added_by_manager: client.added_by_manager,
          name: `${(client.form_data as any)?.surname || ''} ${(client.form_data as any)?.name || ''}`.trim() || 'Unknown',
          visaCountry: (client.form_data as any)?.visaCountry || 'not set',
          created_at: client.created_at,
          company_id: client.company_id
        })) || []
      },
      analytics_metrics: {
        total_clients: totalClients,
        paid_clients: paidClientsCount,
        submitted_applications: submittedApplications
      },
      distributions: {
        step_status: stepStatusDistribution,
        progress_status: progressStatusDistribution
      },
      problems: {
        inconsistent_paid_statuses: problematicStatuses.length,
        inconsistent_clients: problematicStatuses.map(client => ({
          id: client.id,
          current_status: client.client_progress_status,
          should_be: CLIENT_PROGRESS_STATUS.PAID_PACKAGE
        }))
      },
      constants_check: {
        expected_paid_package_status: CLIENT_PROGRESS_STATUS.PAID_PACKAGE,
        expected_paid_package_status_type: typeof CLIENT_PROGRESS_STATUS.PAID_PACKAGE,
        expected_paid_package_status_length: CLIENT_PROGRESS_STATUS.PAID_PACKAGE.length
      }
    };

    console.log('Test results:', JSON.stringify(testResults, null, 2));

    return res.status(200).json(testResults);

  } catch (error) {
    console.error('Test API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleStatusFix(req: NextApiRequest, res: NextApiResponse) {
  try {
    console.log('=== FIXING INCONSISTENT PAID STATUSES ===');
    
    // Find all clients with inconsistent paid statuses
    const { data: problematicClients, error: fetchError } = await supabase
      .from('visa_applications')
      .select('id, client_progress_status, service_payment_status, is_paid')
      .like('client_progress_status', '%оплатил%')
      .neq('client_progress_status', CLIENT_PROGRESS_STATUS.PAID_PACKAGE);

    if (fetchError) {
      console.error('Error fetching problematic clients:', fetchError);
      return res.status(500).json({ error: 'Fetch error' });
    }

    console.log(`Found ${problematicClients?.length || 0} clients with inconsistent statuses`);

    if (!problematicClients || problematicClients.length === 0) {
      return res.status(200).json({ 
        message: 'No clients need status fixing',
        fixed_count: 0
      });
    }

    // Update all problematic clients to use the correct status
    const updatePromises = problematicClients.map(client => 
      supabase
        .from('visa_applications')
        .update({ 
          client_progress_status: CLIENT_PROGRESS_STATUS.PAID_PACKAGE,
          updated_at: new Date().toISOString()
        })
        .eq('id', client.id)
    );

    const results = await Promise.allSettled(updatePromises);
    
    const successCount = results.filter(result => result.status === 'fulfilled').length;
    const failureCount = results.filter(result => result.status === 'rejected').length;

    console.log(`Status fix completed: ${successCount} successful, ${failureCount} failed`);

    return res.status(200).json({
      message: 'Status fix completed',
      fixed_count: successCount,
      failed_count: failureCount,
      total_processed: problematicClients.length
    });

  } catch (error) {
    console.error('Status fix error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}