import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      // Get price list items for a company
      const { companyId } = req.query;

      let query = supabase
        .from('price_list')
        .select('*')
        .order('country', { ascending: true })
        .order('price', { ascending: true });

      // Filter by company if provided (but not for 'all' companies)
      if (companyId && companyId !== 'undefined' && companyId !== 'all') {
        query = query.eq('company_id', companyId);
      }

      const { data: priceList, error } = await query;

      if (error) {
        console.error('Error fetching price list:', error);
        return res.status(500).json({ error: 'Failed to fetch price list' });
      }

      return res.status(200).json({ packages: priceList });
    }

    if (req.method === 'POST') {
      // Create new price list item
      const { company_id, country, title, price, duration, description } = req.body;

      if (!company_id || !country || !title || !price) {
        return res.status(400).json({ 
          error: 'Company ID, country, title, and price are required' 
        });
      }

      const { data: priceItem, error } = await supabase
        .from('price_list')
        .insert({
          company_id,
          country,
          title,
          price: parseFloat(price),
          duration,
          description,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating price list item:', error);
        return res.status(500).json({ error: 'Failed to create price list item' });
      }

      return res.status(201).json({ 
        success: true, 
        priceItem,
        message: 'Price list item created successfully'
      });
    }

    if (req.method === 'PUT') {
      // Update price list item
      const { id, country, title, price, duration, description } = req.body;

      if (!id) {
        return res.status(400).json({ error: 'Price list item ID is required' });
      }

      const updateData: any = { updated_at: new Date().toISOString() };
      
      if (country !== undefined) updateData.country = country;
      if (title !== undefined) updateData.title = title;
      if (price !== undefined) updateData.price = parseFloat(price);
      if (duration !== undefined) updateData.duration = duration;
      if (description !== undefined) updateData.description = description;

      const { data: priceItem, error } = await supabase
        .from('price_list')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating price list item:', error);
        return res.status(500).json({ error: 'Failed to update price list item' });
      }

      return res.status(200).json({ 
        success: true, 
        priceItem,
        message: 'Price list item updated successfully'
      });
    }

    if (req.method === 'DELETE') {
      // Delete price list item
      const { id } = req.body;

      if (!id) {
        return res.status(400).json({ error: 'Price list item ID is required' });
      }

      // Check if item is referenced by any applications
      const { data: applications } = await supabase
        .from('visa_applications')
        .select('id')
        .eq('service_package_id', id)
        .limit(1);

      if (applications && applications.length > 0) {
        return res.status(400).json({ 
          error: 'Cannot delete price list item that is referenced by applications' 
        });
      }

      const { error } = await supabase
        .from('price_list')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting price list item:', error);
        return res.status(500).json({ error: 'Failed to delete price list item' });
      }

      return res.status(200).json({ 
        success: true, 
        message: 'Price list item deleted successfully' 
      });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Price list API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 