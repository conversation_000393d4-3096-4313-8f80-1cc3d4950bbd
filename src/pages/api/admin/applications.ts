import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import { COUNTRY_MAPPINGS, getCountryDisplayName } from '../../../types/client-status';

// Country normalization mapping - add visaDestination values from Step1
const COUNTRY_NORMALIZATION: { [key: string]: string } = {
  'United States': 'US',
  'US': 'США',
  'USA': 'США',
  'США': 'США',
  'Соединенные Штаты': 'США',
  'usa': 'США', // From Step1 visaDestination
  
  'United Kingdom': 'Великобритания',
  'UK': 'Великобритания',
  'Britain': 'Великобритания',
  'Великобритания': 'Великобритания',
  'Британия': 'Великобритания',
  'uk': 'Великобритания', // From Step1 visaDestination
  
  'Canada': 'Канада',
  'Канада': 'Канада',
  'canada': 'Канада', // From Step1 visaDestination
  
  'Australia': 'Australia',
  'Австралия': 'Australia',
  'australia': 'Australia', // From Step1 visaDestination
  
  'Germany': 'Шенген',
  'Германия': 'Шенген',
  'Deutschland': 'Шенген',
  'France': 'Шенген',
  'Франция': 'Шенген',
  'Italy': 'Шенген',
  'Италия': 'Шенген',
  'Italia': 'Шенген',
  'Spain': 'Шенген',
  'Испания': 'Шенген',
  'España': 'Шенген',
  'Netherlands': 'Шенген',
  'Belgium': 'Шенген',
  'Austria': 'Шенген',
  'Switzerland': 'Шенген',
  'Portugal': 'Шенген',
  'Poland': 'Шенген',
  'Czech Republic': 'Шенген',
  'Hungary': 'Шенген',
  'Slovakia': 'Шенген',
  'Slovenia': 'Шенген',
  'Croatia': 'Шенген',
  'Estonia': 'Шенген',
  'Latvia': 'Шенген',
  'Lithuania': 'Шенген',
  'Finland': 'Шенген',
  'Sweden': 'Шенген',
  'Denmark': 'Шенген',
  'Norway': 'Шенген',
  'Iceland': 'Шенген',
  'Шенген': 'Шенген',
  'Schengen Area': 'Шенген',
  'Европа': 'Шенген',
  'schengen': 'Шенген', // From Step1 visaDestination
  
  'China': 'CN',
  'CN': 'CN',
  'Китай': 'CN'
};

// Function to normalize country names
const normalizeCountry = (country: string): string => {
  const normalized = COUNTRY_NORMALIZATION[country];
  return normalized || country;
};

interface VisaApplication {
  id: string;
  agent_id: string;
  form_data: Record<string, unknown>;
  step_status: number;
  phone_number?: string;
  whatsapp_redirected: boolean;
  company_id?: string;
  service_payment_status: string;
  service_package_id?: string;
  visa_status: string;
  client_progress_status: string;
  manual_fix_status?: string;
  consular_fee_paid: boolean;
  requires_fixes: boolean;
  fix_comment?: string;
  invitation_file_url?: string;
  added_by_manager?: boolean;
  created_at: string;
  updated_at: string;
  service_package?: {
    id: string;
    country: string;
    title: string;
    price: number;
    duration?: string;
    description?: string;
  };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      const { 
        companyId, 
        dateFrom, 
        dateTo, 
        service_payment_status,
        visa_status,
        requires_fixes,
        searchQuery,
        minIncome,
        maxIncome,
        maritalStatus,
        destinationCountry,
        country, // Add country parameter for URL-based filtering
        hasSponsor,
        hasInvitation,
        hasPreviousRejections,
        page = '1',
        limit = companyId === 'all' ? '2000' : '1000' // Massively increased limits: 2000 for all companies, 1000 for specific company
      } = req.query;

      let query = supabase
        .from('visa_applications')
        .select(`
          id,
          agent_id,
          form_data,
          step_status,
          phone_number,
          whatsapp_redirected,
          company_id,
          service_payment_status,
          service_package_id,
          visa_status,
          client_progress_status,
          manual_fix_status,
          consular_fee_paid,
          requires_fixes,
          fix_comment,
          invitation_file_url,
          added_by_manager,
          created_at,
          updated_at,
          service_package:price_list(id, country, title, price, duration, description)
        `);

      // Apply company filter - explicit handling for superadmin
      if (companyId && companyId !== 'all' && companyId !== 'undefined' && companyId !== '' && String(companyId).trim() !== '') {
        // Filter by specific company when a company is explicitly selected
        query = query.eq('company_id', companyId);
      }
      // When companyId is 'all'/null/undefined/empty, show ALL applications (no company filter)
      // This ensures superadmin sees all applications from all companies when "все компании" is selected

      if (dateFrom) {
        query = query.gte('created_at', dateFrom);
      }

      if (dateTo) {
        query = query.lte('created_at', dateTo);
      }

      if (service_payment_status) {
        query = query.eq('service_payment_status', service_payment_status);
      }

      if (visa_status) {
        query = query.eq('visa_status', visa_status);
      }

      if (requires_fixes === 'true') {
        query = query.eq('requires_fixes', true);
      } else if (requires_fixes === 'false') {
        query = query.eq('requires_fixes', false);
      }

      // Note: Country filtering moved to client-side for more flexible matching
      // Database-level filtering was too strict and missed clients with different country formats

      // Order by updated_at descending to get latest first, with created_at as secondary sort
      query = query.order('updated_at', { ascending: false, nullsFirst: false })
                   .order('created_at', { ascending: false });

      // Get total count without pagination first
      const countQuery = supabase
        .from('visa_applications')
        .select('id', { count: 'exact', head: true });

      // Apply the same company filter for count
      if (companyId && companyId !== 'all' && companyId !== 'undefined' && companyId !== '' && String(companyId).trim() !== '') {
        countQuery.eq('company_id', companyId);
      }

      // Apply the same date filters for count
      if (dateFrom) {
        countQuery.gte('created_at', dateFrom);
      }
      if (dateTo) {
        countQuery.lte('created_at', dateTo);
      }

      // Apply the same service_payment_status filter for count
      if (service_payment_status) {
        countQuery.eq('service_payment_status', service_payment_status);
      }

      // Apply the same visa_status filter for count
      if (visa_status) {
        countQuery.eq('visa_status', visa_status);
      }

      // Apply the same requires_fixes filter for count
      if (requires_fixes === 'true') {
        countQuery.eq('requires_fixes', true);
      } else if (requires_fixes === 'false') {
        countQuery.eq('requires_fixes', false);
      }

      // Note: Country filtering for count also moved to client-side

      const { count: totalCount, error: countError } = await countQuery;

      if (countError) {
        console.error('Error getting total count:', countError);
      }
      
      // Get TRUE total count without any filters for consistent display
      const trueTotalQuery = supabase
        .from('visa_applications')
        .select('id', { count: 'exact', head: true });
        
      // Apply only company filter for true total (superadmin vs company admin)
      if (companyId && companyId !== 'all' && companyId !== 'undefined' && companyId !== '' && String(companyId).trim() !== '') {
        trueTotalQuery.eq('company_id', companyId);
      }
      
      const { count: trueTotalCount, error: trueTotalError } = await trueTotalQuery;
      
      if (trueTotalError) {
        console.error('Error getting true total count:', trueTotalError);
      }

      // Pagination - now applied after database filtering
      const pageNumber = parseInt(page as string);
      const limitNumber = parseInt(limit as string);
      const from = (pageNumber - 1) * limitNumber;
      const to = from + limitNumber - 1;

      query = query.range(from, to);

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching applications:', error);
        return res.status(500).json({ error: 'Failed to fetch applications' });
      }

      let applications = data || [];

      // Debug: Check added_by_manager field in fetched applications
      const managerAddedCount = applications?.filter(app => app.added_by_manager === true).length || 0;
      console.log(`🔍 DEBUG added_by_manager: Found ${managerAddedCount} manager-added clients in raw API response`);
      if (managerAddedCount > 0) {
        const managerClients = applications?.filter(app => app.added_by_manager === true).slice(0, 2);
        console.log('Sample manager clients:', managerClients?.map(c => ({ 
          id: c.id, 
          added_by_manager: c.added_by_manager, 
          agent_id: c.agent_id,
          name: `${c.form_data?.name || ''} ${c.form_data?.surname || ''}`.trim()
        })));
      }

      // Apply remaining client-side filtering for complex form_data searches
      if (searchQuery) {
        const queryStr = (searchQuery as string).toLowerCase();
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          return (
            String(formData.firstName || formData.fullNameCyrillic || formData.name || '').toLowerCase().includes(queryStr) ||
            String(formData.lastName || formData.surname || '').toLowerCase().includes(queryStr) ||
            String(formData.profession || formData.occupation || '').toLowerCase().includes(queryStr) ||
            String(application.phone_number || '').toLowerCase().includes(queryStr) ||
            String(formData.email || '').toLowerCase().includes(queryStr)
          );
        });
      }

      // Apply income filtering
      if (minIncome || maxIncome) {
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          const income = Number(formData.income || formData.salary || 0);
          const min = minIncome ? Number(minIncome) : 0;
          const max = maxIncome ? Number(maxIncome) : Infinity;
          return income >= min && income <= max;
        });
      }

      // Apply marital status filtering
      if (maritalStatus) {
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          const status = String(formData.maritalStatus || '').toLowerCase();
          return status.includes((maritalStatus as string).toLowerCase());
        });
      }

      // Apply visa country filtering (legacy parameter) - check both visaCountry and visaDestination
      if (destinationCountry) {
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          
          // Check both visaCountry and visaDestination fields
          const visaCountry = String(formData.visaCountry || '').toLowerCase();
          const visaDestination = String(formData.visaDestination || '').toLowerCase();
          
          const searchTerm = (destinationCountry as string).toLowerCase();
          return visaCountry.includes(searchTerm) || visaDestination.includes(searchTerm);
        });
      }

      // Apply country filtering with flexible matching (supports both normalized and display names)
      if (country) {
        const countryParam = Array.isArray(country) ? country[0] : country;
        
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          
          // Check both visaCountry and visaDestination fields
          const visaCountry = String(formData.visaCountry || formData.country || '');
          const visaDestination = String(formData.visaDestination || '');
          
          // Normalize both values and compare with requested country
          const normalizedVisaCountry = normalizeCountry(visaCountry);
          const normalizedVisaDestination = normalizeCountry(visaDestination);
          
          return normalizedVisaCountry === countryParam || normalizedVisaDestination === countryParam;
        });
      }

      // Apply sponsor filtering
      if (hasSponsor) {
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          const hasSponsorData = formData.hasSponsor || formData.sponsor;
          return hasSponsor === 'yes' ? hasSponsorData : !hasSponsorData;
        });
      }

      // Apply invitation filtering
      if (hasInvitation) {
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          const hasInvitationData = formData.hasInvitation || formData.invitation;
          return hasInvitation === 'yes' ? hasInvitationData : !hasInvitationData;
        });
      }

      // Apply previous rejections filtering
      if (hasPreviousRejections) {
        applications = applications.filter((app: unknown) => {
          const application = app as Record<string, unknown>;
          const formData = (application.form_data as Record<string, unknown>) || {};
          const hasRejections = formData.hasPreviousRejections || formData.rejections;
          return hasPreviousRejections === 'yes' ? hasRejections : !hasRejections;
        });
      }

      // Calculate filtered total after client-side filtering
      const filteredTotalCount = applications.length;
      
      // Check added_by_manager field after all filtering
      const finalManagerAddedCount = applications?.filter(app => app.added_by_manager === true).length || 0;
      
      // Logging for debugging total count issues
      console.log('=== APPLICATIONS API DEBUG ===');
      console.log('Company ID:', companyId);
      console.log('Filters applied:', { 
        dateFrom, dateTo, service_payment_status, visa_status, requires_fixes, 
        country, searchQuery, minIncome, maxIncome, maritalStatus 
      });
      console.log('Database count (filtered):', totalCount);
      console.log('True total count (company only):', trueTotalCount);
      console.log('Applications after client-side filtering:', filteredTotalCount);
      console.log(`🔍 FINAL: ${finalManagerAddedCount} manager-added clients in final response`);
      console.log('==============================');

      return res.status(200).json({
        applications,
        total: trueTotalCount || 0, // TRUE total for consistent "Всего заявок" display
        filteredTotal: filteredTotalCount, // Actual filtered count
        dbFilteredTotal: totalCount || 0, // Database filtered count
        page: pageNumber,
        limit: limitNumber
      });
    }

    if (req.method === 'PUT') {
      const { 
        applicationId, 
        company_id,
        service_payment_status,
        service_package_id,
        visa_status,
        client_progress_status,
        manual_fix_status,
        consular_fee_paid,
        requires_fixes,
        fix_comment
      } = req.body;

      if (!applicationId) {
        return res.status(400).json({ error: 'Application ID is required' });
      }

      const updateData: Record<string, unknown> = {
        updated_at: new Date().toISOString()
      };

      if (company_id !== undefined) updateData.company_id = company_id;
      if (service_payment_status) updateData.service_payment_status = service_payment_status;
      if (service_package_id) updateData.service_package_id = service_package_id;
      if (visa_status) updateData.visa_status = visa_status;
      if (client_progress_status) updateData.client_progress_status = client_progress_status;
      if (manual_fix_status) updateData.manual_fix_status = manual_fix_status;
      if (consular_fee_paid !== undefined) updateData.consular_fee_paid = consular_fee_paid;
      if (requires_fixes !== undefined) updateData.requires_fixes = requires_fixes;
      if (fix_comment !== undefined) updateData.fix_comment = fix_comment;

      const { data, error } = await supabase
        .from('visa_applications')
        .update(updateData)
        .eq('id', applicationId)
        .select(`
          id,
          agent_id,
          form_data,
          step_status,
          phone_number,
          whatsapp_redirected,
          company_id,
          service_payment_status,
          service_package_id,
          visa_status,
          client_progress_status,
          manual_fix_status,
          consular_fee_paid,
          requires_fixes,
          fix_comment,
          added_by_manager,
          created_at,
          updated_at,
          service_package:price_list(id, country, title, price, duration, description)
        `)
        .single();

      if (error) {
        console.error('Error updating application status:', error);
        return res.status(500).json({ error: 'Failed to update application status' });
      }

      return res.status(200).json({ success: true, data });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Admin applications API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 