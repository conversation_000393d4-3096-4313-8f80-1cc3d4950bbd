import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'PUT') {
      // Update admin preferences
      const { language, timezone, emailNotifications, browserNotifications, darkMode } = req.body;

      // In a real app, you would store these preferences in Supabase
      // For now, we'll just simulate a successful update
      
      const preferences = {
        language,
        timezone,
        emailNotifications,
        browserNotifications,
        darkMode
      };

      return res.status(200).json({ 
        success: true, 
        message: 'Preferences updated successfully',
        preferences
      });
    }

    if (req.method === 'GET') {
      // Get admin preferences
      // In a real app, you would fetch from Supabase
      const defaultPreferences = {
        language: 'ru',
        timezone: 'Europe/Moscow',
        emailNotifications: true,
        browserNotifications: false,
        darkMode: false
      };

      return res.status(200).json({ preferences: defaultPreferences });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Preferences API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 