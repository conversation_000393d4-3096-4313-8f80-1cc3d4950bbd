import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import bcrypt from 'bcryptjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ error: 'Email and password are required' });
  }

  try {
    console.log('AUTH API CALLED - SIMPLIFIED VERSION');
    console.log('Login attempt:', { username, password });

    // First, get the employee data (including blocked users for proper error handling)
    const { data: employee, error } = await supabase
      .from('employees')
      .select(`
        id,
        email,
        password_hash,
        role,
        company_id,
        full_name,
        is_active,
        is_blocked
      `)
      .eq('email', username)
      .single();

    console.log('Database query result:', { employee, error });

    if (error || !employee) {
      return res.status(401).json({ error: 'Неверные учетные данные' });
    }

    console.log('Employee found:', employee);

    // Check if user is blocked/inactive
    if (!employee.is_active) {
      return res.status(403).json({ 
        error: 'Ваш аккаунт неактивен. Обратитесь к администратору для активации.',
        blocked: true,
        userBlocked: true
      });
    }

    if (employee.is_blocked) {
      return res.status(403).json({ 
        error: 'Ваш аккаунт был заблокирован администратором. Обратитесь к руководству для получения доступа.',
        blocked: true,
        userBlocked: true
      });
    }

    // For development, handle both hashed and plain text passwords
    let isValidPassword = false;
    
    if (employee.password_hash.startsWith('$2b$')) {
      // Hashed password
      isValidPassword = await bcrypt.compare(password, employee.password_hash);
    } else {
      // Plain text password (for development only)
      isValidPassword = password === employee.password_hash;
    }

    console.log('Password check:', { provided: password, stored: employee.password_hash, valid: isValidPassword });

    if (!isValidPassword) {
      return res.status(401).json({ error: 'Неверные учетные данные' });
    }

    // Get company data separately if employee has a company
    let companyName = null;
    if (employee.role !== 'super_admin' && employee.company_id) {
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id, name, is_blocked')
        .eq('id', employee.company_id)
        .single();

      if (!companyError && company) {
        companyName = company.name;
        
        // Check if company is blocked (only for non-super admins)
        if (company.is_blocked) {
          return res.status(403).json({ 
            error: 'Ваша компания была заблокирована администратором системы. Доступ к панели администратора временно ограничен.',
            blocked: true,
            companyName: company.name
          });
        }
      }
    }

    // Return admin session data
    const adminSession = {
      id: employee.id,
      username: employee.email,
      role: employee.role,
      companyId: employee.company_id,
      companyName: companyName || 'Test Company',
      fullName: employee.full_name
    };

    console.log('Login successful, returning:', adminSession);

    res.status(200).json({ admin: adminSession });
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ error: 'Ошибка сервера' });
  }
} 