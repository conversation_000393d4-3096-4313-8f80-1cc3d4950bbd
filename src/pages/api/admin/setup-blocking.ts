import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

// Create admin client for setup
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Setting up company blocking functionality...');

    // Create a simple blocking status table
    const companies = [
      '550e8400-e29b-41d4-a716-446655440001',
      '550e8400-e29b-41d4-a716-446655440002', 
      '550e8400-e29b-41d4-a716-446655440003'
    ];

    // Create company_blocking_status records
    const blockingRecords = companies.map(companyId => ({
      company_id: companyId,
      is_blocked: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    // Try to insert blocking status records
    const { data: insertedRecords, error: insertError } = await supabaseAdmin
      .from('company_blocking_status')
      .upsert(blockingRecords, { onConflict: 'company_id' })
      .select();

    if (insertError) {
      console.error('Insert error:', insertError);
      return res.status(500).json({ error: 'Failed to create blocking records: ' + insertError.message });
    }

    console.log('Setup completed successfully');

    return res.status(200).json({
      success: true,
      message: `Setup completed. ${insertedRecords?.length || 0} company blocking records created.`,
      records: insertedRecords
    });

  } catch (error) {
    console.error('Setup error:', error);
    return res.status(500).json({ error: 'Setup failed: ' + error });
  }
} 