import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      // Get all companies with their admin info
      const { data: companies, error } = await supabase
        .from('companies')
        .select(`
          *,
          employees!companies_employees_company_id_fkey(
            id,
            email,
            full_name,
            role,
            is_active
          )
        `);

      if (error) {
        console.error('Error fetching companies:', error);
        return res.status(500).json({ error: 'Failed to fetch companies' });
      }

      return res.status(200).json({ companies });
    }

    if (req.method === 'POST') {
      // Create new company with admin user
      const { name, phone_number, email, adminEmail, adminPassword, adminFullName } = req.body;

      if (!name || !adminEmail || !adminPassword) {
        return res.status(400).json({ error: 'Company name, admin email, and password are required' });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(adminEmail)) {
        return res.status(400).json({ error: 'Invalid admin email format' });
      }

      // Check if email already exists
      const { data: existingEmployee } = await supabase
        .from('employees')
        .select('id')
        .eq('email', adminEmail)
        .single();

      if (existingEmployee) {
        return res.status(400).json({ error: 'Admin email already exists' });
      }

      try {
        // Start transaction by creating company first
        const { data: company, error: companyError } = await supabase
          .from('companies')
          .insert([{
            name,
            phone_number,
            email
          }])
          .select()
          .single();

        if (companyError) {
          console.error('Error creating company:', companyError);
          return res.status(500).json({ error: 'Failed to create company' });
        }

        // Create admin user in Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: adminEmail,
          password: adminPassword,
          email_confirm: true,
          user_metadata: {
            role: 'visa_admin',
            company_id: company.id,
            company_name: name,
            full_name: adminFullName || `Admin ${name}`,
          }
        });

        if (authError) {
          // Rollback company creation
          await supabase.from('companies').delete().eq('id', company.id);
          console.error('Supabase auth error:', authError);
          return res.status(400).json({ 
            error: `Failed to create admin user: ${authError.message}` 
          });
        }

        // Create employee record
        const { error: employeeError } = await supabase
          .from('employees')
          .insert([{
            id: authData.user?.id,
            email: adminEmail,
            password_hash: 'managed_by_supabase_auth',
            role: 'visa_admin',
            company_id: company.id,
            full_name: adminFullName || `Admin ${name}`,
            is_active: true
          }]);

        if (employeeError) {
          // Rollback auth user and company
          await supabase.auth.admin.deleteUser(authData.user?.id!);
          await supabase.from('companies').delete().eq('id', company.id);
          console.error('Error creating employee record:', employeeError);
          return res.status(500).json({ error: 'Failed to create employee record' });
        }

        return res.status(201).json({ 
          success: true, 
          company,
          message: 'Company and admin user created successfully'
        });

      } catch (error) {
        console.error('Company creation failed:', error);
        return res.status(500).json({ 
          error: 'Failed to create company and admin user' 
        });
      }
    }

    if (req.method === 'PUT') {
      // Update company
      const { companyId, name, phone_number, email } = req.body;

      if (!companyId) {
        return res.status(400).json({ error: 'Company ID is required' });
      }

      const { data: company, error } = await supabase
        .from('companies')
        .update({
          name,
          phone_number,
          email,
          updated_at: new Date().toISOString()
        })
        .eq('id', companyId)
        .select()
        .single();

      if (error) {
        console.error('Error updating company:', error);
        return res.status(500).json({ error: 'Failed to update company' });
      }

      return res.status(200).json({ 
        success: true, 
        company,
        message: 'Company updated successfully'
      });
    }

    if (req.method === 'DELETE') {
      // Delete company and related data
      const { companyId } = req.body;

      if (!companyId) {
        return res.status(400).json({ error: 'Company ID is required' });
      }

      try {
        // Get all employees to delete from auth
        const { data: employees } = await supabase
          .from('employees')
          .select('id')
          .eq('company_id', companyId);

        // Delete auth users
        if (employees && employees.length > 0) {
          for (const employee of employees) {
            try {
              await supabase.auth.admin.deleteUser(employee.id);
            } catch (authError) {
              console.error('Failed to delete auth user:', employee.id, authError);
              // Continue with deletion process
            }
          }
        }

        // Delete company (cascades to employees and price_list due to foreign key constraints)
        const { error } = await supabase
          .from('companies')
          .delete()
          .eq('id', companyId);

        if (error) {
          console.error('Error deleting company:', error);
          return res.status(500).json({ error: 'Failed to delete company' });
        }

        return res.status(200).json({ 
          success: true, 
          message: 'Company and all related data deleted successfully' 
        });

      } catch (error) {
        console.error('Error during company deletion:', error);
        return res.status(500).json({ error: 'Failed to delete company completely' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Companies API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 