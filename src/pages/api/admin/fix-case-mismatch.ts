// API endpoint to fix case mismatch in client_progress_status

import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔧 Starting client_progress_status case fix...');

    // Fix lowercase "прошли опросник" to uppercase "Прошли опросник"
    const { data: fixedCompleted, error: errorCompleted } = await supabase
      .from('visa_applications')
      .update({ client_progress_status: 'Прошли опросник' })
      .eq('client_progress_status', 'прошли опросник')
      .select('id');

    if (errorCompleted) {
      console.error('Error fixing completed questionnaire status:', errorCompleted);
      throw errorCompleted;
    }

    // Fix other potential case mismatches
    const caseFixes = [
      { from: 'оплатили пакет услуг', to: 'Оплатили пакет услуг' },
      { from: 'оплатили пакет', to: 'Оплатили пакет услуг' },
      { from: 'сбор информации ботом', to: 'Сбор информации ботом' },
      { from: 'ожидает приглашения', to: 'Ожидает приглашения' },
      { from: 'согласование кейса', to: 'Согласование кейса' },
      { from: 'заполнение анкеты + обучение', to: 'Заполнение анкеты + обучение' },
      { from: 'подано', to: 'Подано' }
    ];

    let totalFixed = fixedCompleted?.length || 0;

    for (const fix of caseFixes) {
      const { data: fixedData, error } = await supabase
        .from('visa_applications')
        .update({ client_progress_status: fix.to })
        .eq('client_progress_status', fix.from)
        .select('id');

      if (error) {
        console.error(`Error fixing "${fix.from}" to "${fix.to}":`, error);
        continue;
      }

      if (fixedData && fixedData.length > 0) {
        console.log(`✅ Fixed ${fixedData.length} records: "${fix.from}" → "${fix.to}"`);
        totalFixed += fixedData.length;
      }
    }

    // Get count of applications by status after fix
    const { data: statusCounts, error: countError } = await supabase
      .from('visa_applications')
      .select('client_progress_status')
      .not('client_progress_status', 'is', null);

    if (!countError && statusCounts) {
      const counts = statusCounts.reduce((acc, app) => {
        const status = app.client_progress_status || 'null';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log('📊 Current client_progress_status distribution:', counts);
    }

    res.status(200).json({
      success: true,
      message: `Successfully fixed case mismatch in ${totalFixed} records`,
      fixedCompleted: fixedCompleted?.length || 0,
      totalFixed,
      statusCounts: statusCounts ? statusCounts.reduce((acc, app) => {
        const status = app.client_progress_status || 'null';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) : null
    });

  } catch (error) {
    console.error('❌ Error fixing case mismatch:', error);
    res.status(500).json({ 
      error: 'Failed to fix case mismatch',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}