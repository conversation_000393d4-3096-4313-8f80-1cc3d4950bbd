import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'POST') {
      // Create new client
      const clientData = req.body;
      
      const formData = {
        firstName: clientData.firstName,
        lastName: clientData.lastName,
        phone: clientData.phone,
        email: clientData.email,
        profession: clientData.profession,
        income: clientData.income,
        destinationCountry: clientData.destinationCountry,
        maritalStatus: clientData.maritalStatus,
        isPaid: clientData.isPaid,
        isPaidConsularFee: clientData.isPaidConsularFee,
        workflowStep: clientData.workflowStep,
        visa_status: clientData.status,
        hasSponsor: clientData.hasSponsor,
        hasInvitation: clientData.hasInvitation,
        hasPreviousRejections: clientData.hasPreviousRejections,
        companyId: 'company_1' // Default company ID
      };

      const { data, error } = await supabase
        .from('visa_applications')
        .insert({
          agent_id: 'admin_created',
          form_data: formData,
          step_status: 5, // Default step
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating client:', error);
        return res.status(500).json({ error: 'Failed to create client' });
      }

      return res.status(201).json({ success: true, client: data });
    }

    if (req.method === 'PUT') {
      // Update existing client
      const clientData = req.body;
      const { clientId } = req.query;

      if (!clientId) {
        return res.status(400).json({ error: 'Client ID is required' });
      }

      // First get the current form_data
      const { data: currentData, error: fetchError } = await supabase
        .from('visa_applications')
        .select('form_data')
        .eq('id', clientId)
        .single();

      if (fetchError) {
        console.error('Error fetching current client:', fetchError);
        return res.status(500).json({ error: 'Failed to fetch current client' });
      }

      // Update with merged form_data
      const updatedFormData = {
        ...currentData.form_data,
        firstName: clientData.firstName,
        lastName: clientData.lastName,
        phone: clientData.phone,
        email: clientData.email,
        profession: clientData.profession,
        income: clientData.income,
        destinationCountry: clientData.destinationCountry,
        maritalStatus: clientData.maritalStatus,
        isPaid: clientData.isPaid,
        isPaidConsularFee: clientData.isPaidConsularFee,
        workflowStep: clientData.workflowStep,
        visa_status: clientData.status,
        hasSponsor: clientData.hasSponsor,
        hasInvitation: clientData.hasInvitation,
        hasPreviousRejections: clientData.hasPreviousRejections
      };

      const { data, error } = await supabase
        .from('visa_applications')
        .update({
          form_data: updatedFormData,
          updated_at: new Date().toISOString()
        })
        .eq('id', clientId)
        .select()
        .single();

      if (error) {
        console.error('Error updating client:', error);
        return res.status(500).json({ error: 'Failed to update client' });
      }

      return res.status(200).json({ success: true, client: data });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Clients API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 