import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../../utils/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { employeeId } = req.query;

  if (!employeeId) {
    return res.status(400).json({ error: 'Employee ID is required' });
  }

  try {
    if (req.method === 'PUT') {
      // Update employee status
      const { is_active, role, full_name } = req.body;

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (is_active !== undefined) updateData.is_active = is_active;
      if (role) updateData.role = role;
      if (full_name) updateData.full_name = full_name;

      const { data: employee, error } = await supabase
        .from('employees')
        .update(updateData)
        .eq('id', employeeId)
        .select(`
          id,
          email,
          role,
          company_id,
          full_name,
          is_active,
          created_at,
          updated_at
        `)
        .single();

      if (error) {
        return res.status(400).json({ error: 'Failed to update employee: ' + error.message });
      }

      return res.status(200).json({ success: true, employee });
    }

    if (req.method === 'DELETE') {
      // Delete employee
      const { error } = await supabase
        .from('employees')
        .delete()
        .eq('id', employeeId);

      if (error) {
        return res.status(400).json({ error: 'Failed to delete employee: ' + error.message });
      }

      return res.status(200).json({ success: true, message: 'Employee deleted successfully' });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Employee API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 