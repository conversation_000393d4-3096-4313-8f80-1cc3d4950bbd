import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Mock data for employees - in a real app, this would be in Supabase
const mockEmployees = [
  {
    id: 'emp_1',
    username: 'john_doe',
    email: '<EMAIL>',
    role: 'employee',
    companyId: 'company_1',
    createdAt: '2024-01-20T10:00:00Z',
    isActive: true
  },
  {
    id: 'emp_2',
    username: 'jane_smith',
    email: '<EMAIL>',
    role: 'manager',
    companyId: 'company_1',
    createdAt: '2024-02-15T14:30:00Z',
    isActive: true
  },
  {
    id: 'emp_3',
    username: 'bob_wilson',
    email: '<EMAIL>',
    role: 'employee',
    companyId: 'company_1',
    createdAt: '2024-03-01T09:15:00Z',
    isActive: false
  }
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { companyId } = req.query;

  if (!companyId) {
    return res.status(400).json({ error: 'Company ID is required' });
  }

  try {
    if (req.method === 'GET') {
      // Get employees for a company
      const employees = mockEmployees.filter(emp => emp.companyId === companyId);
      return res.status(200).json({ employees });
    }

    if (req.method === 'POST') {
      // Add new employee
      const { username, email, password, role } = req.body;

      if (!username || !email || !password || !role) {
        return res.status(400).json({ error: 'Username, email, password, and role are required' });
      }

      const newEmployee = {
        id: `emp_${Date.now()}`,
        username,
        email,
        role,
        companyId: companyId as string,
        createdAt: new Date().toISOString(),
        isActive: true
      };

      // In a real app, you would hash the password and store it in Supabase
      mockEmployees.push(newEmployee);

      return res.status(201).json({ success: true, employee: newEmployee });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Employees API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 