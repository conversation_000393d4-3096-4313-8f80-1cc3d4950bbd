import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Create admin client for operations requiring elevated permissions
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      const { companyId } = req.query;
      
      // Build query based on whether companyId is provided
      let query = supabase
        .from('companies')
        .select(`
          id,
          name,
          phone_number,
          email,
          slug,
          wapi_token,
          wapi_profile_id,
          wapi_webhook_url,
          created_at,
          updated_at,
          is_blocked,
          employees(id, email, full_name, role, is_active)
        `);
      
      // Filter by companyId if provided
      if (companyId) {
        query = query.eq('id', companyId);
      }
      
      query = query.order('created_at', { ascending: false });
      
      const { data: companies, error } = await query;

      if (error) {
        console.error('Error fetching companies:', error);
        return res.status(500).json({ error: 'Failed to fetch companies' });
      }

      return res.status(200).json({ companies: companies || [] });
    }

    if (req.method === 'POST') {
      // Create new company with visa admin user
      const { name, phone_number, email, slug, wapi_token, wapi_profile_id, wapi_webhook_url, adminEmail, adminPassword, adminFullName } = req.body;

      if (!name || !adminEmail || !adminPassword || !slug) {
        return res.status(400).json({ error: 'Company name, slug, admin email, and password are required' });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(adminEmail)) {
        return res.status(400).json({ error: 'Invalid email format' });
      }

      // Validate slug format
      if (!/^[a-z0-9-]+$/.test(slug)) {
        return res.status(400).json({ error: 'Slug can only contain lowercase letters, numbers, and hyphens' });
      }

      // Check if slug already exists
      const { data: existingCompany, error: slugCheckError } = await supabase
        .from('companies')
        .select('id')
        .eq('slug', slug)
        .single();

      if (existingCompany) {
        return res.status(400).json({ error: 'Slug already exists. Please choose a different one.' });
      }

      // Check if admin email already exists
      const { data: existingEmployee, error: checkError } = await supabase
        .from('employees')
        .select('id')
        .eq('email', adminEmail)
        .single();

      if (existingEmployee) {
        return res.status(400).json({ error: 'Admin email already exists' });
      }

      try {
        // Create company first (is_blocked defaults to FALSE)
        const { data: newCompany, error: companyError } = await supabase
          .from('companies')
          .insert({
            name,
            phone_number,
            email,
            slug,
            wapi_token: wapi_token || null,
            wapi_profile_id: wapi_profile_id || null,
            wapi_webhook_url: wapi_webhook_url || null,
            is_blocked: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (companyError) {
          return res.status(400).json({ error: 'Failed to create company: ' + companyError.message });
        }

        // Hash password for employee
        const hashedPassword = await bcrypt.hash(adminPassword, 10);

        // Create admin employee
        const { data: adminEmployee, error: employeeError } = await supabaseAdmin
          .from('employees')
          .insert({
            email: adminEmail,
            password_hash: hashedPassword,
            role: 'visa_admin',
            company_id: newCompany.id,
            full_name: adminFullName || `Admin ${name}`,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (employeeError) {
          // Rollback company creation if employee creation fails
          await supabase.from('companies').delete().eq('id', newCompany.id);
          return res.status(400).json({ error: 'Failed to create admin employee: ' + employeeError.message });
        }

        // Create admin user in Supabase Auth
        try {
          const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
          email: adminEmail,
          password: adminPassword,
            email_confirm: true,
          user_metadata: {
            role: 'visa_admin',
              company_id: newCompany.id,
              employee_id: adminEmployee.id,
              full_name: adminFullName || `Admin ${name}`
          }
        });

        if (authError) {
            // Rollback database insertions if auth creation fails
            await supabase.from('employees').delete().eq('id', adminEmployee.id);
            await supabase.from('companies').delete().eq('id', newCompany.id);
            return res.status(400).json({ error: 'Failed to create auth user: ' + authError.message });
          }

        return res.status(201).json({ 
          success: true, 
            company: {
              ...newCompany,
              employees: [adminEmployee]
            },
          message: 'Company and admin user created successfully'
        });

        } catch (authError) {
          // Rollback database insertions if auth creation fails
          await supabase.from('employees').delete().eq('id', adminEmployee.id);
          await supabase.from('companies').delete().eq('id', newCompany.id);
          return res.status(400).json({ error: 'Failed to create auth user' });
        }

      } catch (error) {
        console.error('Error creating company:', error);
        return res.status(500).json({ error: 'Internal server error' });
      }
    }

    if (req.method === 'PUT') {
      // Update company
      const { companyId, name, phone_number, email, slug, wapi_token, wapi_profile_id, wapi_webhook_url, isBlocked } = req.body;

      if (!companyId) {
        return res.status(400).json({ error: 'Company ID is required' });
      }

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (name) updateData.name = name;
      if (phone_number !== undefined) updateData.phone_number = phone_number;
      if (email !== undefined) updateData.email = email;
      if (slug !== undefined) updateData.slug = slug;
      if (wapi_token !== undefined) updateData.wapi_token = wapi_token;
      if (wapi_profile_id !== undefined) updateData.wapi_profile_id = wapi_profile_id;
      if (wapi_webhook_url !== undefined) updateData.wapi_webhook_url = wapi_webhook_url;

      // Handle company blocking/unblocking in database
      if (isBlocked !== undefined) {
        updateData.is_blocked = isBlocked;
        console.log(`${isBlocked ? 'Blocking' : 'Unblocking'} company ${companyId} in database`);
      }

      const { data: updatedCompany, error } = await supabase
        .from('companies')
        .update(updateData)
        .eq('id', companyId)
        .select(`
          id,
          name,
          phone_number,
          email,
          slug,
          wapi_token,
          wapi_profile_id,
          wapi_webhook_url,
          created_at,
          updated_at,
          is_blocked,
          employees(id, email, full_name, role, is_active)
        `)
        .single();

      if (error) {
        return res.status(400).json({ error: 'Failed to update company: ' + error.message });
      }

      return res.status(200).json({ 
        success: true, 
        company: updatedCompany,
        message: 'Company updated successfully'
      });
    }

    if (req.method === 'DELETE') {
      // Delete company
      const { companyId } = req.body;

      if (!companyId) {
        return res.status(400).json({ error: 'Company ID is required' });
      }

      try {
        // Get all employees for this company to delete them from auth
        const { data: employees, error: employeesError } = await supabase
          .from('employees')
          .select('email')
          .eq('company_id', companyId);

        if (employeesError) {
          return res.status(500).json({ error: 'Failed to fetch company employees' });
        }

        // Delete employees from Supabase Auth
        if (employees && employees.length > 0) {
          try {
            const { data: users } = await supabaseAdmin.auth.admin.listUsers();
            for (const employee of employees) {
              const authUser = users.users.find(u => u.email === employee.email);
              if (authUser) {
                await supabaseAdmin.auth.admin.deleteUser(authUser.id);
              }
            }
          } catch (authError) {
            console.error('Failed to delete auth users:', authError);
            // Continue with database deletion even if auth deletion fails
          }
        }

        // Delete company (employees will be deleted automatically due to CASCADE)
        const { error: deleteError } = await supabase
          .from('companies')
          .delete()
          .eq('id', companyId);

        if (deleteError) {
          return res.status(400).json({ error: 'Failed to delete company: ' + deleteError.message });
        }

        return res.status(200).json({ 
          success: true, 
          message: 'Company and all associated data deleted successfully' 
        });

      } catch (error) {
        console.error('Error during company deletion:', error);
        return res.status(500).json({ error: 'Failed to delete company completely' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Companies API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 