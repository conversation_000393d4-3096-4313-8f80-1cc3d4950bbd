import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { phone } = req.query;

    if (!phone || typeof phone !== 'string') {
      return res.status(400).json({ error: 'Phone number is required' });
    }

    // Normalize phone number for comprehensive comparison
    const cleanPhone = phone.replace(/\D/g, '');
    const normalizePhone = (phoneNum: string) => {
      const digits = phoneNum.replace(/\D/g, '');
      if (digits.startsWith('8') && digits.length === 11) {
        return '7' + digits.substring(1);
      }
      if (digits.startsWith('7') && digits.length === 11) {
        return digits;
      }
      return digits;
    };

    const normalizedPhone = normalizePhone(phone);

    // Check if client exists in visa_applications table
    const { data, error } = await supabase
      .from('visa_applications')
      .select('id, phone_number, form_data')
      .or(`phone_number.eq.${phone},phone_number.eq.${cleanPhone},phone_number.eq.+${normalizedPhone}`)
      .limit(1);

    if (error) {
      console.error('Database error:', error);
      return res.status(500).json({ error: 'Database error' });
    }

    const exists = data && data.length > 0;
    let clientInfo = null;

    if (exists) {
      const clientData = data[0];
      const clientName = clientData.form_data?.name && clientData.form_data?.surname 
        ? `${clientData.form_data.name} ${clientData.form_data.surname}`
        : 'неизвестно';
      
      clientInfo = {
        id: clientData.id,
        name: clientName,
        phone: clientData.phone_number
      };
    }

    return res.status(200).json({
      exists,
      clientInfo,
      message: exists 
        ? `Клиент с номером ${phone} уже зарегистрирован в системе`
        : 'Номер телефона доступен для регистрации',
      translationKey: exists ? 'clients.modal.phoneAlreadyExists' : 'clients.phoneAvailable',
      translationParams: exists ? { phone, clientName: clientInfo?.name } : { phone }
    });

  } catch (error) {
    console.error('Error checking client:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 