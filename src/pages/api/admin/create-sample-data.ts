import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

const sampleApplications = [
  {
    agent_id: 'agent_001',
    company_id: 'company_1',
    form_data: {
      visa_status: 'pending',
      fullNameCyrillic: 'Алексей Петров',
      name: '<PERSON><PERSON>',
      surname: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '******-123-4567',
      citizenship: 'Kazakhstan',
      countryOfBirth: 'Kazakhstan',
      occupation: 'Software Engineer',
      visaDestination: 'USA',
      passportNumber: 'N12345678',
      dateOfBirth: '1990-05-15',
      income: '75000',
      travelPurposeDescription: 'Business conference'
    },
    step_status: 9
  },
  {
    agent_id: 'agent_002',
    company_id: 'company_1',
    form_data: {
      visa_status: 'accepted',
      fullNameCyrillic: 'Мария Иванова',
      name: 'Maria',
      surname: 'Ivanova',
      email: '<EMAIL>',
      phone: '******-234-5678',
      citizenship: 'Russia',
      countryOfBirth: 'Russia',
      occupation: 'Marketing Manager',
      visaDestination: 'Canada',
      passportNumber: 'R98765432',
      dateOfBirth: '1985-08-22',
      income: '60000',
      travelPurposeDescription: 'Tourism'
    },
    step_status: 9
  },
  {
    agent_id: 'agent_003',
    company_id: 'company_1',
    form_data: {
      visa_status: 'rejected',
      fullNameCyrillic: 'Дмитрий Сидоров',
      name: 'Dmitry',
      surname: 'Sidorov',
      email: '<EMAIL>',
      phone: '******-345-6789',
      citizenship: 'Belarus',
      countryOfBirth: 'Belarus',
      occupation: 'Teacher',
      visaDestination: 'UK',
      passportNumber: 'B11223344',
      dateOfBirth: '1978-12-03',
      income: '35000',
      travelPurposeDescription: 'Educational purposes'
    },
    step_status: 9
  },
  {
    agent_id: 'agent_004',
    company_id: 'company_1',
    form_data: {
      visa_status: 'pending',
      fullNameCyrillic: 'Анна Козлова',
      name: 'Anna',
      surname: 'Kozlova',
      email: '<EMAIL>',
      phone: '******-456-7890',
      citizenship: 'Ukraine',
      countryOfBirth: 'Ukraine',
      occupation: 'Doctor',
      visaDestination: 'Germany',
      passportNumber: 'U55667788',
      dateOfBirth: '1992-03-18',
      income: '85000',
      travelPurposeDescription: 'Medical conference'
    },
    step_status: 9
  },
  {
    agent_id: 'agent_005',
    company_id: 'company_1',
    form_data: {
      visa_status: 'accepted',
      fullNameCyrillic: 'Сергей Николаев',
      name: 'Sergey',
      surname: 'Nikolaev',
      email: '<EMAIL>',
      phone: '******-567-8901',
      citizenship: 'Kazakhstan',
      countryOfBirth: 'Kazakhstan',
      occupation: 'Business Owner',
      visaDestination: 'Australia',
      passportNumber: '*********',
      dateOfBirth: '1980-11-07',
      income: '120000',
      travelPurposeDescription: 'Business expansion'
    },
    step_status: 9
  }
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Add random dates for the past 6 months
    const applicationsWithDates = sampleApplications.map(app => ({
      ...app,
      created_at: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { data, error } = await supabase
      .from('visa_applications')
      .insert(applicationsWithDates)
      .select();

    if (error) {
      console.error('Error creating sample data:', error);
      return res.status(500).json({ error: 'Failed to create sample data' });
    }

    return res.status(200).json({
      success: true,
      message: `Created ${data?.length || 0} sample applications`,
      data
    });
  } catch (error) {
    console.error('Sample data creation error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 