import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../../utils/supabase';
import { createClient } from '@supabase/supabase-js';
import { enforceClientStatusBusinessRules } from '../../../../utils/clientStatusValidation';
import { CLIENT_PROGRESS_STATUS, SERVICE_PAYMENT_STATUS } from '../../../../types/client-status';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Application ID is required' });
  }

  try {
    if (req.method === 'GET') {
      // Get single application
      const { data, error } = await supabase
        .from('visa_applications')
        .select(`
          id,
          agent_id,
          form_data,
          step_status,
          phone_number,
          whatsapp_redirected,
          service_payment_status,
          service_package_id,
          visa_status,
          client_progress_status,
          manual_fix_status,
          consular_fee_paid,
          requires_fixes,
          fix_comment,
          invitation_file_url,
          created_at,
          updated_at,
          last_updated,
          service_package:price_list(id, country, title, price, duration, description)
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching application:', error);
        return res.status(500).json({ error: 'Failed to fetch application' });
      }

      if (!data) {
        return res.status(404).json({ error: 'Application not found' });
      }

      return res.status(200).json({ application: data });
    }

    if (req.method === 'PUT') {
      // Update application
      const {
        form_data,
        step_status,
        visa_status,
        service_payment_status,
        client_progress_status,
        consular_fee_paid,
        requires_fixes,
        whatsapp_redirected,
        phone_number,
        agent_id,
        service_package_id,
        manual_fix_status,
        fix_comment,
        invitation_file_url
      } = req.body;

      // Prepare update data
      const updateData: Record<string, unknown> = {
        updated_at: new Date().toISOString()
      };

      // Update form_data if provided
      if (form_data) {
        updateData.form_data = form_data;
      }

      // Update direct fields if provided
      if (step_status !== undefined) updateData.step_status = step_status;
      if (visa_status) updateData.visa_status = visa_status;
      if (service_payment_status) updateData.service_payment_status = service_payment_status;
      if (client_progress_status) updateData.client_progress_status = client_progress_status;
      if (consular_fee_paid !== undefined) updateData.consular_fee_paid = consular_fee_paid;
      if (requires_fixes !== undefined) updateData.requires_fixes = requires_fixes;
      if (whatsapp_redirected !== undefined) updateData.whatsapp_redirected = whatsapp_redirected;
      if (phone_number) updateData.phone_number = phone_number;
      if (agent_id) updateData.agent_id = agent_id;
      if (service_package_id) {
        updateData.service_package_id = service_package_id;
        updateData.is_paid = true; // Required by check_payment_consistency constraint
        
        // Ensure payment status is consistent when package is assigned
        if (!service_payment_status) {
          updateData.service_payment_status = SERVICE_PAYMENT_STATUS.PAID; // Use correct constant
        }
        
        // Ensure client progress status is consistent when package is assigned
        if (!client_progress_status) {
          updateData.client_progress_status = CLIENT_PROGRESS_STATUS.PAID_PACKAGE; // Move from "Прошли опросник" to "Оплатили пакет"
        }
        
        // Apply business rules validation to ensure consistency
        const statusToValidate = {
          client_progress_status: updateData.client_progress_status || client_progress_status,
          service_payment_status: updateData.service_payment_status || service_payment_status,
          service_package_id: service_package_id
        };
        
        const validatedStatus = enforceClientStatusBusinessRules(statusToValidate);
        updateData.client_progress_status = validatedStatus.client_progress_status;
        updateData.service_payment_status = validatedStatus.service_payment_status;
        
        console.log('Package assignment - validated status:', {
          service_package_id: service_package_id,
          is_paid: updateData.is_paid,
          client_progress_status: updateData.client_progress_status,
          service_payment_status: updateData.service_payment_status
        });
      }
      if (manual_fix_status) updateData.manual_fix_status = manual_fix_status;
      if (fix_comment !== undefined) updateData.fix_comment = fix_comment;
      if (invitation_file_url !== undefined) updateData.invitation_file_url = invitation_file_url;

      // Check if we need to send WhatsApp notification for package assignment
      let shouldSendWhatsAppNotification = false;
      let currentClientData = null;
      let assignedPackageData = null;

      if (service_package_id) {
        try {
          // Get current client data to check if they were unpaid
          const { data: currentData, error: fetchError } = await supabaseAdmin
            .from('visa_applications')
            .select('service_payment_status, service_package_id, phone_number, company_id, form_data')
            .eq('id', id)
            .single();

          if (!fetchError && currentData) {
            currentClientData = currentData;
            // Check if client was unpaid and now getting a paid package
            const wasUnpaid = currentData.service_payment_status === 'unpaid' || currentData.service_payment_status === 'не оплачено' || !currentData.service_package_id;
            
            if (wasUnpaid) {
              // Get the package details
              const { data: packageData, error: packageError } = await supabaseAdmin
                .from('price_list')
                .select('id, country, title, price, description')
                .eq('id', service_package_id)
                .single();

              if (!packageError && packageData) {
                assignedPackageData = packageData;
                shouldSendWhatsAppNotification = true;
              } else {
                console.log('Package not found or error fetching package:', packageError);
              }
            } else {
              console.log('Client was already paid, no WhatsApp notification needed');
            }
          } else {
            console.log('Error fetching current client data:', fetchError);
          }
        } catch (preCheckError) {
          console.error('Error in WhatsApp pre-check:', preCheckError);
          // Don't fail the main update if this fails
        }
      }

      const { data, error } = await supabase
        .from('visa_applications')
        .update(updateData)
        .eq('id', id)
        .select(`
          id,
          agent_id,
          form_data,
          step_status,
          phone_number,
          whatsapp_redirected,
          service_payment_status,
          service_package_id,
          visa_status,
          client_progress_status,
          manual_fix_status,
          consular_fee_paid,
          requires_fixes,
          fix_comment,
          invitation_file_url,
          created_at,
          updated_at,
          last_updated,
          service_package:price_list(id, country, title, price, duration, description)
        `)
        .single();

      if (error) {
        console.error('Error updating application:', error);
        return res.status(500).json({ error: 'Failed to update application' });
      }

      // Send WhatsApp notification if package was assigned to unpaid client
      if (shouldSendWhatsAppNotification && currentClientData && assignedPackageData && currentClientData.company_id) {
        try {
          console.log('Attempting to send WhatsApp notification for package assignment...');
          
          // Get WhatsApp credentials and company details from companies table
          const { data: companyData, error: companyError } = await supabaseAdmin
            .from('companies')
            .select('id, name, slug, wapi_token, wapi_profile_id')
            .eq('id', currentClientData.company_id)
            .single();

          if (companyError) {
            console.log('Error fetching company data:', companyError);
          } else if (!companyData) {
            console.log('No company data found for ID:', currentClientData.company_id);
          } else if (!companyData.wapi_token || !companyData.wapi_profile_id) {
            console.log('Missing WhatsApp credentials for company:', currentClientData.company_id);
          } else if (!currentClientData.phone_number) {
            console.log('No phone number found for client');
          } else {
            // Clean phone number for WhatsApp (remove all non-digits including +)
            const whatsappPhone = currentClientData.phone_number.replace(/[^\d]/g, '');
            
            // Check if cleaned phone number is valid
            if (whatsappPhone && whatsappPhone.length >= 10) {
              // Format message with dynamic company details
              const companyName = companyData.name || 'Visa Pro';
              const message = `Добрый день, спасибо что вы доверяете нам и поздравляем вам с покупкой!\nМеня зовут Алишер, я визовик компании ${companyName}\n\nДавайте начнем подачу, для начала пожалуйста ответьте на следующие вопросы для генерации кейса, эти данные нужны чтобы составить вам тур маршрут, отвечайте максимально честно и детально`;
              
              console.log('Sending WhatsApp message to:', whatsappPhone);
              
              const whatsappResponse = await fetch(`https://wappi.pro/api/sync/message/send?profile_id=${companyData.wapi_profile_id}`, {
                method: 'POST',
                headers: {
                  'accept': 'application/json',
                  'Authorization': companyData.wapi_token,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  body: message,
                  recipient: whatsappPhone
                })
              });

              const whatsappResult = await whatsappResponse.json();
              
              if (whatsappResponse.ok) {
                console.log('✅ WhatsApp notification sent successfully for package assignment:', whatsappResult);
              } else {
                console.error('❌ WhatsApp API error for package assignment:', whatsappResult);
                console.error('Response status:', whatsappResponse.status);
              }
            } else {
              console.log('Invalid phone number format, skipping WhatsApp notification');
            }
          }
        } catch (whatsappError) {
          console.error('Error sending WhatsApp notification for package assignment:', whatsappError);
          // Don't fail the whole request if WhatsApp fails
        }
      } else {
        console.log('WhatsApp notification not triggered:', {
          shouldSend: shouldSendWhatsAppNotification,
          hasClientData: !!currentClientData,
          hasPackageData: !!assignedPackageData,
          hasCompanyId: !!currentClientData?.company_id
        });
      }

      return res.status(200).json({ 
        success: true, 
        application: data,
        message: 'Application updated successfully'
      });
    }

    if (req.method === 'DELETE') {
      // Delete application
      const { error } = await supabase
        .from('visa_applications')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting application:', error);
        return res.status(500).json({ error: 'Failed to delete application' });
      }

      return res.status(200).json({ 
        success: true, 
        message: 'Application deleted successfully' 
      });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Application API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 