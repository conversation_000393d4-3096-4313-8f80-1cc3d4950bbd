import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import bcrypt from 'bcryptjs';
import { createClient } from '@supabase/supabase-js';

// Create admin client for user management
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        return await handleGet(req, res);
      case 'POST':
        return await handlePost(req, res);
      case 'PUT':
        return await handlePut(req, res);
      case 'DELETE':
        return await handleDelete(req, res);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Employee API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  const { companyId } = req.query;

  let query = supabase
    .from('employees')
    .select(`
      id,
      email,
      role,
      company_id,
      full_name,
      is_active,
      password_plain,
      created_at,
      updated_at,
      company:companies(id, name)
    `)
    .order('created_at', { ascending: false });

  if (companyId && companyId !== 'undefined') {
    query = query.eq('company_id', companyId);
  }

  const { data: employees, error } = await query;

  if (error) {
    return res.status(500).json({ error: 'Failed to fetch employees' });
  }

  return res.status(200).json({ employees });
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  const { email, password, role, company_id, full_name } = req.body;

  if (!email || !password || !role) {
    return res.status(400).json({ error: 'Email, password, and role are required' });
  }

  // Validate role
  if (!['super_admin', 'visa_admin', 'manager'].includes(role)) {
    return res.status(400).json({ error: 'Invalid role. Allowed roles: super_admin, visa_admin, manager' });
  }

  // Check if employee with this email already exists in database
  const { data: existingEmployee } = await supabase
    .from('employees')
    .select('email')
    .eq('email', email)
    .single();

  if (existingEmployee) {
    return res.status(400).json({ error: 'Employee with this email already exists' });
  }

  // Check if user with this email exists in Supabase Auth
  try {
    const { data: users } = await supabaseAdmin.auth.admin.listUsers();
    const existingAuthUser = users.users.find(u => u.email === email);
    
    if (existingAuthUser) {
      // If auth user exists but no database record, clean up auth user first
      try {
        await supabaseAdmin.auth.admin.deleteUser(existingAuthUser.id);
        console.log(`Cleaned up orphaned auth user for email: ${email}`);
      } catch (cleanupError) {
        console.error('Failed to cleanup orphaned auth user:', cleanupError);
        return res.status(400).json({ error: 'Email already exists in authentication system. Please contact administrator.' });
      }
    }
  } catch (authListError) {
    console.error('Failed to check existing auth users:', authListError);
    // Continue with creation as this is not critical
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 10);

  try {
    // Create user in Supabase Auth first
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        role,
        company_id: company_id || null,
        full_name
      }
    });

    if (authError) {
      return res.status(400).json({ error: 'Failed to create auth user: ' + authError.message });
    }

    // Then create employee in database
    const { data: employee, error: dbError } = await supabase
      .from('employees')
      .insert({
        email,
        password_hash: hashedPassword,
        password_plain: password, // Store plain text password for visa admin visibility
        role,
        company_id: company_id || null,
        full_name,
        is_active: true
      })
      .select(`
        id,
        email,
        role,
        company_id,
        full_name,
        is_active,
        password_plain,
        created_at,
        updated_at,
        company:companies(id, name)
      `)
      .single();

    if (dbError) {
      // Rollback auth user creation if database insertion fails
      try {
        await supabaseAdmin.auth.admin.deleteUser(authUser.user.id);
      } catch (rollbackError) {
        console.error('Failed to rollback auth user creation:', rollbackError);
      }
      return res.status(400).json({ error: 'Failed to create employee: ' + dbError.message });
    }

    // Update auth user metadata with employee_id
    try {
      await supabaseAdmin.auth.admin.updateUserById(authUser.user.id, {
        user_metadata: {
          role,
          company_id: company_id || null,
          employee_id: employee.id,
          full_name
        }
      });
    } catch (metadataError) {
      console.error('Failed to update auth user metadata:', metadataError);
      // Don't fail the request as the user is created successfully
    }

    return res.status(201).json({ 
      employee,
      message: 'Employee created successfully' 
    });
  } catch (error) {
    console.error('Error creating employee:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;
  const { email, role, company_id, full_name, is_active, password } = req.body;

  if (!id) {
    return res.status(400).json({ error: 'Employee ID is required' });
  }

  const updateData: any = {
    updated_at: new Date()
  };

  if (email) updateData.email = email;
  if (role) updateData.role = role;
  if (company_id !== undefined) updateData.company_id = company_id;
  if (full_name) updateData.full_name = full_name;
  if (is_active !== undefined) updateData.is_active = is_active;
  if (password) {
    updateData.password_hash = await bcrypt.hash(password, 10);
    updateData.password_plain = password; // Store plain text password for visa admin visibility
  }

  const { data: employee, error } = await supabase
    .from('employees')
    .update(updateData)
    .eq('id', id)
    .select(`
      id,
      email,
      role,
      company_id,
      full_name,
      is_active,
      password_plain,
      created_at,
      updated_at,
      company:companies(id, name)
    `)
    .single();

  if (error) {
    return res.status(400).json({ error: 'Failed to update employee: ' + error.message });
  }

  // Update user metadata in auth if role or company changed
  if (role || company_id !== undefined) {
    try {
      const { data: users } = await supabaseAdmin.auth.admin.listUsers();
      const authUser = users.users.find(u => u.email === employee.email);
      
      if (authUser) {
        await supabaseAdmin.auth.admin.updateUserById(authUser.id, {
          user_metadata: {
            ...authUser.user_metadata,
            role: role || authUser.user_metadata?.role,
            company_id: company_id !== undefined ? company_id : authUser.user_metadata?.company_id,
            employee_id: employee.id,
            full_name: full_name || authUser.user_metadata?.full_name
          }
        });
      }
    } catch (authError) {
      console.error('Failed to update auth user metadata:', authError);
      // Don't fail the request if auth update fails
    }
  }

  return res.status(200).json({ 
    employee,
    message: 'Employee updated successfully' 
  });
}

async function handleDelete(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'Employee ID is required' });
  }

  // Get employee data first to find auth user
  const { data: employee, error: getError } = await supabase
    .from('employees')
    .select('email, id')
    .eq('id', id)
    .single();

  if (getError) {
    return res.status(404).json({ error: 'Employee not found' });
  }

  console.log(`Deleting employee: ${employee.email} (ID: ${employee.id})`);

  // First, try to find and delete from Supabase Auth
  let authUserDeleted = false;
  try {
    const { data: users } = await supabaseAdmin.auth.admin.listUsers();
    const authUser = users.users.find(u => u.email === employee.email);
    
    if (authUser) {
      const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(authUser.id);
      if (authDeleteError) {
        console.error('Failed to delete auth user:', authDeleteError);
        // Don't fail the request, continue with database deletion
      } else {
        console.log(`Successfully deleted auth user for: ${employee.email}`);
        authUserDeleted = true;
      }
    } else {
      console.log(`No auth user found for email: ${employee.email}`);
      authUserDeleted = true; // Consider it successful if no auth user exists
    }
  } catch (authError) {
    console.error('Error during auth user deletion:', authError);
    // Continue with database deletion even if auth deletion fails
  }

  // Delete from database
  const { error: deleteError } = await supabase
    .from('employees')
    .delete()
    .eq('id', id);

  if (deleteError) {
    return res.status(400).json({ error: 'Failed to delete employee from database: ' + deleteError.message });
  }

  console.log(`Successfully deleted employee from database: ${employee.email}`);

  // Additional cleanup: try to delete any remaining auth users with the same email
  try {
    const { data: remainingUsers } = await supabaseAdmin.auth.admin.listUsers();
    const remainingAuthUsers = remainingUsers.users.filter(u => u.email === employee.email);
    
    for (const remainingUser of remainingAuthUsers) {
      try {
        await supabaseAdmin.auth.admin.deleteUser(remainingUser.id);
        console.log(`Cleaned up remaining auth user: ${remainingUser.id} for email: ${employee.email}`);
      } catch (cleanupError) {
        console.error(`Failed to cleanup remaining auth user ${remainingUser.id}:`, cleanupError);
      }
    }
  } catch (cleanupListError) {
    console.error('Failed to list users for cleanup:', cleanupListError);
  }

  return res.status(200).json({ 
    message: 'Employee deleted successfully',
    details: {
      email: employee.email,
      authUserDeleted,
      databaseRecordDeleted: true
    }
  });
} 