import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';

// Country normalization mapping - add visaDestination values from Step1
const COUNTRY_NORMALIZATION: { [key: string]: string } = {
  'United States': 'US',
  'US': 'US',
  'USA': 'US',
  'США': 'US',
  'Соединенные Штаты': 'US',
  'usa': 'US', // From Step1 visaDestination
  
  'United Kingdom': 'UK',
  'UK': 'UK',
  'Britain': 'UK',
  'Великобритания': 'UK',
  'Британия': 'UK',
  'uk': 'UK', // From Step1 visaDestination
  
  'Canada': 'Canada',
  'Канада': 'Canada',
  'canada': 'Canada', // From Step1 visaDestination
  
  'Australia': 'Australia',
  'Австралия': 'Australia',
  'australia': 'Australia', // From Step1 visaDestination
  
  'Germany': 'EU',
  'Германия': 'EU',
  'Deutschland': 'EU',
  'France': 'EU',
  'Франция': 'EU',
  'Italy': 'EU',
  'Италия': 'EU',
  'Italia': 'EU',
  'Spain': 'EU',
  'Испания': 'EU',
  'España': 'EU',
  'Netherlands': 'EU',
  'Belgium': 'EU',
  'Austria': 'EU',
  'Switzerland': 'EU',
  'Portugal': 'EU',
  'Poland': 'EU',
  'Czech Republic': 'EU',
  'Hungary': 'EU',
  'Slovakia': 'EU',
  'Slovenia': 'EU',
  'Croatia': 'EU',
  'Estonia': 'EU',
  'Latvia': 'EU',
  'Lithuania': 'EU',
  'Finland': 'EU',
  'Sweden': 'EU',
  'Denmark': 'EU',
  'Norway': 'EU',
  'Iceland': 'EU',
  'Шенген': 'EU',
  'Schengen Area': 'EU',
  'Европа': 'EU',
  'schengen': 'EU', // From Step1 visaDestination
  
  'China': 'CN',
  'CN': 'CN',
  'Китай': 'CN'
};

// Function to normalize country names
const normalizeCountry = (country: string): string => {
  const normalized = COUNTRY_NORMALIZATION[country];
  return normalized || country;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { dateFrom, dateTo, country, companyId } = req.query;

    // Build base query for visa applications
    let query = supabase.from('visa_applications').select(`
      id,
      step_status,
      service_payment_status,
      visa_status,
      consular_fee_paid,
      requires_fixes,
      fix_comment,
      form_data,
      company_id,
      created_at,
      service_package:price_list(price, country, title, package_type)
    `);

    // Apply company filter
    if (companyId && companyId !== 'all') {
      query = query.eq('company_id', companyId);
    }

    // Apply date filters
    if (dateFrom) {
      query = query.gte('created_at', dateFrom);
    }
    if (dateTo) {
      query = query.lte('created_at', dateTo);
    }

    // Order by updated_at descending to get latest first, with created_at as secondary sort
    query = query.order('updated_at', { ascending: false, nullsFirst: false })
                 .order('created_at', { ascending: false });

    const { data: applications, error } = await query;

    if (error) {
      console.error('Error fetching applications for analytics:', error);
      return res.status(500).json({ error: 'Failed to fetch analytics data' });
    }

    // Filter by country if specified (after fetching, since we need to normalize)
    let filteredApplications = applications || [];
    if (country && country !== 'undefined' && country !== '') {
      filteredApplications = filteredApplications.filter(app => {
        const formData = (app.form_data as Record<string, unknown>) || {};
        
        // Check both visaCountry and visaDestination fields for compatibility
        const visaCountry = String(formData.visaCountry || formData.country || 'USA');
        const visaDestination = String(formData.visaDestination || '');
        
        const normalizedVisaCountry = normalizeCountry(visaCountry);
        const normalizedVisaDestination = normalizeCountry(visaDestination);
        
        return normalizedVisaCountry === country || normalizedVisaDestination === country;
      });
    }

    // Helper function to get estimated price when service package is not available
    const getEstimatedPrice = (formData: Record<string, unknown>) => {
      const visaCountry = String(formData?.visaCountry || formData?.country || 'USA');
      const visaDestination = String(formData?.visaDestination || '');
      
      // Use visaCountry first, fallback to normalized visaDestination
      let normalizedCountry = normalizeCountry(visaCountry);
      if (normalizedCountry === visaCountry && visaDestination) {
        // If no normalization happened for visaCountry, try visaDestination
        normalizedCountry = normalizeCountry(visaDestination);
      }
      
      const basePrice: { [key: string]: number } = {
        'US': 450,
        'UK': 380,
        'EU': 320,
        'CN': 280,
        'Canada': 400,
        'Australia': 350
      };
      return basePrice[normalizedCountry] || 350;
    };

    // Calculate TRUE total applications (company filtered only, no other filters)
    let trueTotalApplications = applications.length;
    if (companyId && companyId !== 'all' && companyId !== 'undefined' && companyId !== '' && String(companyId).trim() !== '') {
      // Filter by specific company when a company is explicitly selected
      trueTotalApplications = applications.filter(app => app.company_id === companyId).length;
    }
    
    // filteredTotalApplications - с учётом всех фильтров (дата, страна и т.д.)
    const filteredTotalApplications = filteredApplications.length;

    // Logging for debugging
    console.log('=== ANALYTICS API DEBUG ===');
    console.log('Company ID:', companyId);
    console.log('All applications:', applications.length);
    console.log('Filtered applications:', filteredTotalApplications);
    console.log('TRUE total applications (company only):', trueTotalApplications);
    console.log('===========================');
    
    const totalApplications = trueTotalApplications; // Use TRUE total for consistency
    const totalClients = filteredApplications.length;
    const paidClients = filteredApplications.filter(app => app.service_payment_status === 'оплачено').length;
    const submittedApplications = filteredApplications.filter(app => app.visa_status === 'подано').length;
    const acceptedApplications = filteredApplications.filter(app => app.visa_status === 'одобрено').length;
    const rejectedApplications = filteredApplications.filter(app => app.visa_status === 'отклонено').length;
    const pendingApplications = filteredApplications.filter(app => app.visa_status === 'ожидает').length;
    
    // Calculate total revenue from paid packages
    const totalRevenue = filteredApplications
      .filter(app => app.service_payment_status === 'оплачено')
      .reduce((sum, app) => {
        const packageData = Array.isArray(app.service_package) ? app.service_package[0] : app.service_package;
        const price = packageData?.price || getEstimatedPrice(app.form_data);
        return sum + parseFloat(price.toString());
      }, 0);

    // Get applications that need attention
    const pendingPayments = filteredApplications.filter(app => 
      app.step_status >= 9 && // Completed questionnaire
      app.service_payment_status === 'не оплачено'
    );

    const requiresFixes = filteredApplications.filter(app => 
      app.requires_fixes === true
    );

    // Calculate breakdowns for detailed analytics using filtered data
    const regionBreakdown: { [key: string]: number } = {};
    const professionBreakdown: { [key: string]: number } = {};
    const professionSuccessRate: { [key: string]: { total: number; approved: number; rejected: number } } = {};
    const salaryBreakdown: { [key: string]: number } = {};
    const caseTypeBreakdown: { [key: string]: number } = {};
    const ageBreakdown: { [key: string]: number } = {};

    const incomeApprovalCorrelation: { [key: string]: { total: number; approved: number; rejected: number } } = {};

    // Calculate country breakdown for overview (use company-filtered applications)
    const allCountriesBreakdown: { [key: string]: { total: number; approved: number; rejected: number; pending: number; revenue: number } } = {};
    
    // Apply company filter to applications for country breakdown - explicit handling for superadmin
    let companyFilteredApplications = applications;
    if (companyId && companyId !== 'all' && companyId !== 'undefined' && companyId !== '' && String(companyId).trim() !== '') {
      // Filter by specific company when a company is explicitly selected
      companyFilteredApplications = applications.filter(app => app.company_id === companyId);
    }
    // When companyId is null/undefined/empty, use ALL applications (no company filter)
    // This ensures superadmin sees all applications from all companies when "все компании" is selected
    
    companyFilteredApplications.forEach(app => {
      const formData = (app.form_data as Record<string, unknown>) || {};
      
      // Country breakdown (visa destination) - normalize country names - USE COMPANY-FILTERED APPLICATIONS
      const visaCountry = String(formData.visaCountry || formData.country || 'USA');
      const visaDestination = String(formData.visaDestination || '');
      
      // Use visaCountry first, fallback to normalized visaDestination
      let normalizedCountry = normalizeCountry(visaCountry);
      if (normalizedCountry === visaCountry && visaDestination) {
        // If no normalization happened for visaCountry, try visaDestination
        normalizedCountry = normalizeCountry(visaDestination);
      }
      
      if (!allCountriesBreakdown[normalizedCountry]) {
        allCountriesBreakdown[normalizedCountry] = { total: 0, approved: 0, rejected: 0, pending: 0, revenue: 0 };
      }
      allCountriesBreakdown[normalizedCountry].total += 1;
      
      if (app.visa_status === 'одобрено') allCountriesBreakdown[normalizedCountry].approved += 1;
      else if (app.visa_status === 'отклонено') allCountriesBreakdown[normalizedCountry].rejected += 1;
      else if (app.visa_status === 'ожидает') allCountriesBreakdown[normalizedCountry].pending += 1;
      
      if (app.service_payment_status === 'оплачено') {
        const packageData = Array.isArray(app.service_package) ? app.service_package[0] : app.service_package;
        const price = packageData?.price || getEstimatedPrice(formData);
        allCountriesBreakdown[normalizedCountry].revenue += parseFloat(price.toString());
      }
    });

    // Calculate filtered country breakdown for statistics (based on selected country filter)
    const countryBreakdown: { [key: string]: { total: number; approved: number; rejected: number; pending: number; revenue: number } } = {};
    
    filteredApplications.forEach(app => {
      const formData = (app.form_data as Record<string, unknown>) || {};
      
      // Country breakdown (visa destination) - normalize country names - USE FILTERED APPLICATIONS
      const visaCountry = String(formData.visaCountry || formData.country || 'USA');
      const visaDestination = String(formData.visaDestination || '');
      
      // Use visaCountry first, fallback to normalized visaDestination
      let normalizedCountry = normalizeCountry(visaCountry);
      if (normalizedCountry === visaCountry && visaDestination) {
        // If no normalization happened for visaCountry, try visaDestination
        normalizedCountry = normalizeCountry(visaDestination);
      }
      
      if (!countryBreakdown[normalizedCountry]) {
        countryBreakdown[normalizedCountry] = { total: 0, approved: 0, rejected: 0, pending: 0, revenue: 0 };
      }
      countryBreakdown[normalizedCountry].total += 1;
      
      if (app.visa_status === 'одобрено') countryBreakdown[normalizedCountry].approved += 1;
      else if (app.visa_status === 'отклонено') countryBreakdown[normalizedCountry].rejected += 1;
      else if (app.visa_status === 'ожидает') countryBreakdown[normalizedCountry].pending += 1;
      
      if (app.service_payment_status === 'оплачено') {
        const packageData = Array.isArray(app.service_package) ? app.service_package[0] : app.service_package;
        const price = packageData?.price || getEstimatedPrice(formData);
        countryBreakdown[normalizedCountry].revenue += parseFloat(price.toString());
      }
    });

    // Calculate other breakdowns using filtered applications
    filteredApplications.forEach(app => {
      const formData = (app.form_data as Record<string, unknown>) || {};
      
      // Region breakdown (country of residence)
      const region = String(formData.countryResidence || formData.region || 'Не указано');
      regionBreakdown[region] = (regionBreakdown[region] || 0) + 1;

      // Profession breakdown with success rates
      const profession = String(formData.profession || formData.occupation || 'Не указана');
      professionBreakdown[profession] = (professionBreakdown[profession] || 0) + 1;
      
      if (!professionSuccessRate[profession]) {
        professionSuccessRate[profession] = { total: 0, approved: 0, rejected: 0 };
      }
      professionSuccessRate[profession].total += 1;
      if (app.visa_status === 'одобрено') {
        professionSuccessRate[profession].approved += 1;
      } else if (app.visa_status === 'отклонено') {
        professionSuccessRate[profession].rejected += 1;
      }

      // Age breakdown
      const birthDate = formData.birthDate || formData.dateOfBirth;
      if (birthDate) {
        const age = new Date().getFullYear() - new Date(String(birthDate)).getFullYear();
        let ageGroup = 'Не указан';
        if (age >= 18 && age <= 25) ageGroup = '18-25';
        else if (age >= 26 && age <= 35) ageGroup = '26-35';
        else if (age >= 36 && age <= 45) ageGroup = '36-45';
        else if (age >= 46 && age <= 55) ageGroup = '46-55';
        else if (age >= 56) ageGroup = '56+';
        
        ageBreakdown[ageGroup] = (ageBreakdown[ageGroup] || 0) + 1;
      }

      // Salary breakdown (categorized)
      const salary = parseFloat(String(formData.income || formData.salary || '0'));
      let salaryCategory = 'Не указана';
      if (salary > 0) {
        if (salary < 1000) salaryCategory = 'До 450,000 теңге';
        else if (salary < 3000) salaryCategory = '450,000 - 1,350,000 теңге';
        else if (salary < 5000) salaryCategory = '1,350,000 - 2,250,000 теңге';
        else if (salary < 10000) salaryCategory = '2,250,000 - 4,500,000 теңге';
        else salaryCategory = 'Свыше 4,500,000 теңге';
      }
      salaryBreakdown[salaryCategory] = (salaryBreakdown[salaryCategory] || 0) + 1;

      // Income vs approval correlation
      if (!incomeApprovalCorrelation[salaryCategory]) {
        incomeApprovalCorrelation[salaryCategory] = { total: 0, approved: 0, rejected: 0 };
      }
      incomeApprovalCorrelation[salaryCategory].total += 1;
      if (app.visa_status === 'одобрено') {
        incomeApprovalCorrelation[salaryCategory].approved += 1;
      } else if (app.visa_status === 'отклонено') {
        incomeApprovalCorrelation[salaryCategory].rejected += 1;
      }

      // Case type breakdown
      const caseType = String(formData.visaType || formData.purpose || 'Туризм');
      caseTypeBreakdown[caseType] = (caseTypeBreakdown[caseType] || 0) + 1;
    });

    // Calculate monthly submissions for the last 12 months
    const monthlySubmissions = [];
    const now = new Date();
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      
      const monthApplications = filteredApplications.filter(app => {
        const appDate = new Date(app.created_at);
        return appDate >= date && appDate < nextDate;
      });
      
      monthlySubmissions.push({
        month: date.toLocaleDateString('ru-RU', { year: 'numeric', month: 'short' }),
        count: monthApplications.length
      });
    }

    // Calculate rejection reasons
    const rejectionReasons: { [key: string]: number } = {};
    filteredApplications.filter(app => app.visa_status === 'отклонено').forEach(app => {
      const formData = (app.form_data as Record<string, unknown>) || {};
      const reason = String(formData.rejectionReason || app.fix_comment || 'Причина не указана');
      rejectionReasons[reason] = (rejectionReasons[reason] || 0) + 1;
    });

    // Prepare response data
    const analyticsData = {
      metrics: {
        totalClients,
        paidClients,
        submittedApplications,
        totalRevenue: Math.round(totalRevenue * 100) / 100
      },
      pendingPayments: pendingPayments.map(app => ({
        id: app.id,
        step_status: app.step_status,
        service_payment_status: app.service_payment_status,
        visa_status: app.visa_status,
        client_progress_status: 'Прошли опросник',
        consular_fee_paid: app.consular_fee_paid,
        requires_fixes: app.requires_fixes,
        fix_comment: app.fix_comment,
        form_data: app.form_data,
        created_at: app.created_at,
        service_package: app.service_package
      })),
      requiresFixes: requiresFixes.map(app => ({
        id: app.id,
        step_status: app.step_status,
        service_payment_status: app.service_payment_status,
        visa_status: app.visa_status,
        client_progress_status: app.step_status >= 9 ? 'Прошли опросник' : `шаг ${app.step_status}`,
        consular_fee_paid: app.consular_fee_paid,
        requires_fixes: app.requires_fixes,
        fix_comment: app.fix_comment,
        form_data: app.form_data,
        created_at: app.created_at,
        service_package: app.service_package
      })),
      regionBreakdown,
      professionBreakdown,
      professionSuccessRate,
      salaryBreakdown,
      caseTypeBreakdown,
      countryBreakdown, // This contains filtered countries based on selection
      ageBreakdown,
      incomeApprovalCorrelation,
      rejectionReasons,
      monthlySubmissions,
      totalApplications,
      acceptedApplications,
      rejectedApplications,
      pendingApplications,
      // Add available countries for dropdown (always all countries)
      availableCountries: Object.keys(allCountriesBreakdown).sort(),
      // Add all countries breakdown for overview section
      allCountriesBreakdown,
      filteredTotalApplications
    };

    return res.status(200).json(analyticsData);

  } catch (error) {
    console.error('Analytics API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 