import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';

interface DuplicateApplication {
  id: string;
  phone_number: string;
  name: string;
  surname: string;
  created_at: string;
  action: 'KEEP' | 'DELETE';
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    // Identify duplicates without removing them
    try {
      // Get all applications
      const { data: applications, error } = await supabase
        .from('visa_applications')
        .select('id, phone_number, form_data, created_at')
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching applications:', error);
        return res.status(500).json({ error: 'Failed to fetch applications' });
      }

      // Group by phone number and name to find duplicates
      const phoneNameGroups: { [key: string]: any[] } = {};
      
      applications?.forEach(app => {
        const formData = app.form_data as any;
        const name = formData?.name;
        const surname = formData?.surname;
        const phone = app.phone_number;
        
        if (phone && name && surname) {
          const key = `${phone}_${name}_${surname}`;
          if (!phoneNameGroups[key]) {
            phoneNameGroups[key] = [];
          }
          phoneNameGroups[key].push(app);
        }
      });

      // Find groups with more than one entry (duplicates)
      const duplicates: DuplicateApplication[] = [];
      
      Object.values(phoneNameGroups).forEach(group => {
        if (group.length > 1) {
          group.forEach((app, index) => {
            const formData = app.form_data as any;
            duplicates.push({
              id: app.id,
              phone_number: app.phone_number,
              name: formData.name,
              surname: formData.surname,
              created_at: app.created_at,
              action: index === 0 ? 'KEEP' : 'DELETE' // Keep first (oldest), delete others
            });
          });
        }
      });

      return res.status(200).json({ duplicates });
    } catch (error) {
      console.error('Error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }

  if (req.method === 'POST') {
    // Remove duplicates
    try {
      // Get all applications
      const { data: applications, error } = await supabase
        .from('visa_applications')
        .select('id, phone_number, form_data, created_at')
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching applications:', error);
        return res.status(500).json({ error: 'Failed to fetch applications' });
      }

      // Group by phone number and name to find duplicates
      const phoneNameGroups: { [key: string]: any[] } = {};
      
      applications?.forEach(app => {
        const formData = app.form_data as any;
        const name = formData?.name;
        const surname = formData?.surname;
        const phone = app.phone_number;
        
        if (phone && name && surname) {
          const key = `${phone}_${name}_${surname}`;
          if (!phoneNameGroups[key]) {
            phoneNameGroups[key] = [];
          }
          phoneNameGroups[key].push(app);
        }
      });

      // Collect IDs of duplicates to delete (keep first, delete rest)
      const idsToDelete: string[] = [];
      
      Object.values(phoneNameGroups).forEach(group => {
        if (group.length > 1) {
          // Skip first (index 0), delete the rest
          for (let i = 1; i < group.length; i++) {
            idsToDelete.push(group[i].id);
          }
        }
      });

      if (idsToDelete.length === 0) {
        return res.status(200).json({ 
          message: 'No duplicates found',
          removed: 0 
        });
      }

      // Delete the duplicate records
      const { error: deleteError } = await supabase
        .from('visa_applications')
        .delete()
        .in('id', idsToDelete);

      if (deleteError) {
        console.error('Error deleting duplicates:', deleteError);
        return res.status(500).json({ error: 'Failed to delete duplicates' });
      }

      return res.status(200).json({
        message: `Successfully removed ${idsToDelete.length} duplicate records`,
        removed: idsToDelete.length,
        deletedIds: idsToDelete
      });

    } catch (error) {
      console.error('Error removing duplicates:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }

  return res.status(405).json({ error: 'Method not allowed' });
} 