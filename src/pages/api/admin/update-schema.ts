import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

// Create admin client for schema changes
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Add is_blocked column to companies table using raw SQL
    const { data, error } = await supabaseAdmin
      .from('companies')
      .select('id, name, is_blocked')
      .limit(1);

    if (error && error.message.includes('is_blocked')) {
      // Column doesn't exist, create it
      const { error: createError } = await supabaseAdmin.rpc('exec_sql', {
        sql: 'ALTER TABLE public.companies ADD COLUMN IF NOT EXISTS is_blocked BOOLEAN DEFAULT false;'
      });

      if (createError) {
        console.error('Error creating column:', createError);
        return res.status(500).json({ error: 'Failed to create column: ' + createError.message });
      }
    }

    // Update all existing companies to have is_blocked = false
    const { error: updateError } = await supabaseAdmin
      .from('companies')
      .update({ is_blocked: false })
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Update all

    if (updateError) {
      console.error('Error updating companies:', updateError);
    }

    return res.status(200).json({ 
      success: true, 
      message: 'Schema updated successfully',
      data: data 
    });

  } catch (error) {
    console.error('Schema update error:', error);
    return res.status(500).json({ error: 'Failed to update schema: ' + error });
  }
} 