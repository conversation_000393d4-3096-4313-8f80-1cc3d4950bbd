import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import { createClient } from '@supabase/supabase-js';

// Create admin client for user management
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

interface CleanupResults {
  email: string;
  orphanedAuthUsersRemoved: number;
  orphanedDatabaseRecordsRemoved: number;
  errors: string[];
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { email } = req.body;

  if (!email) {
    return res.status(400).json({ error: 'Email is required' });
  }

  try {
    const cleanupResults: CleanupResults = {
      email,
      orphanedAuthUsersRemoved: 0,
      orphanedDatabaseRecordsRemoved: 0,
      errors: []
    };

    // Check if employee with this email exists in database
    const { data: existingEmployee } = await supabase
      .from('employees')
      .select('id, email, is_active')
      .eq('email', email)
      .single();

    // Get all auth users with this email
    let authUsers: any[] = [];
    try {
      const { data: users } = await supabaseAdmin.auth.admin.listUsers();
      authUsers = users.users.filter(u => u.email === email);
    } catch (authListError: any) {
      cleanupResults.errors.push(`Failed to list auth users: ${authListError?.message || 'Unknown error'}`);
    }

    // If no database record but auth users exist, clean up auth users
    if (!existingEmployee && authUsers.length > 0) {
      for (const authUser of authUsers) {
        try {
          await supabaseAdmin.auth.admin.deleteUser(authUser.id);
          cleanupResults.orphanedAuthUsersRemoved++;
          console.log(`Cleaned up orphaned auth user: ${authUser.id} for email: ${email}`);
        } catch (deleteError: any) {
          cleanupResults.errors.push(`Failed to delete auth user ${authUser.id}: ${deleteError?.message || 'Unknown error'}`);
        }
      }
    }

    // If database record exists but no auth users, this is also an issue
    // but we won't automatically delete database records as they might contain important data
    if (existingEmployee && authUsers.length === 0) {
      cleanupResults.errors.push('Database record exists without corresponding auth user. Manual review required.');
    }

    return res.status(200).json({
      success: true,
      cleanup: cleanupResults,
      message: `Cleanup completed for ${email}. Removed ${cleanupResults.orphanedAuthUsersRemoved} orphaned auth users.`
    });
  } catch (error) {
    console.error('Error during cleanup:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 