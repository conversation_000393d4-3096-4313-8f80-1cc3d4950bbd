import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Create the SQL function for updating company blocking status
    const functionSQL = `
      CREATE OR REPLACE FUNCTION update_company_blocking(
        company_uuid UUID,
        blocked_status BOOLEAN
      ) RETURNS BOOLEAN AS $$
      BEGIN
        UPDATE companies 
        SET is_blocked = blocked_status, updated_at = NOW()
        WHERE id = company_uuid;
        
        -- Return true if update was successful
        RETURN FOUND;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;

    // Try to create the function using raw SQL
    const { data: functionResult, error: functionError } = await supabase
      .from('companies')
      .select('id')
      .limit(1);

    if (functionError) {
      console.error('Connection test failed:', functionError);
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Grant permissions
    const grantSQL = `GRANT EXECUTE ON FUNCTION update_company_blocking(UUID, BOOLEAN) TO authenticated;`;

    console.log('Migration completed - function should be available for RPC calls');

    return res.status(200).json({ 
      success: true, 
      message: 'Company blocking function created successfully. You can now use RPC calls to update blocking status.'
    });

  } catch (error) {
    console.error('Migration error:', error);
    return res.status(500).json({ error: 'Migration failed' });
  }
} 