import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { companyId, dateFrom, dateTo } = req.query;

    // Build date filter
    let dateFilter = '';
    const params: any[] = [];
    
    if (dateFrom && dateTo) {
      dateFilter = 'AND created_at >= $1 AND created_at <= $2';
      params.push(dateFrom, dateTo);
    } else if (dateFrom) {
      dateFilter = 'AND created_at >= $1';
      params.push(dateFrom);
    } else if (dateTo) {
      dateFilter = 'AND created_at <= $1';
      params.push(dateTo);
    }

    // Note: Temporarily ignoring companyId filter as requested in the task
    // Build company filter would be: 
    // let companyFilter = '';
    // if (companyId) {
    //   companyFilter = companyId ? `AND company_id = '${companyId}'` : '';
    // }

    // Get main metrics
    const { data: applications, error: appsError } = await supabase
      .from('visa_applications')
      .select(`
        id,
        step_status,
        service_payment_status,
        is_paid,
        visa_status,
        service_package_id,
        requires_fixes,
        fix_comment,
        form_data,
        created_at,
        service_package:price_list(price),
        client_progress_status
      `)
      .gte('created_at', dateFrom || '1970-01-01')
      .lte('created_at', dateTo || '2099-12-31');

    if (appsError) {
      console.error('Error fetching applications:', appsError);
      return res.status(500).json({ error: 'Failed to fetch applications data' });
    }

    // Calculate metrics
    const totalClients = applications?.filter(app => 
      // Count clients who either:
      // 1. Completed questionnaire (step_status >= 9) OR
      // 2. Are paid clients (added by manager) OR  
      // 3. Have meaningful progress status indicating they're active clients
      app.step_status >= 9 || 
      app.is_paid === true || 
      (app.client_progress_status && [
        'Оплатили пакет услуг',
        'Сбор информации ботом', 
        'Ожидает приглашения',
        'Согласование кейса',
        'Заполнение анкеты + обучение',
        'Подано'
      ].includes(app.client_progress_status))
    ).length || 0;
    
    const paidClients = applications?.filter(app => app.is_paid === true).length || 0;
    const submittedApplications = applications?.filter(app => app.visa_status === 'подано').length || 0;

    // Calculate total revenue from paid packages
    const totalRevenue = applications
      ?.filter(app => app.is_paid === true && app.service_package)
      .reduce((sum, app) => {
        const packageData = Array.isArray(app.service_package) ? app.service_package[0] : app.service_package;
        return sum + (packageData?.price || 0);
      }, 0) || 0;

    // Get pending payments (clients who haven't paid)
    const pendingPayments = applications?.filter(app => 
      app.is_paid === false
    ).map(app => ({
      id: app.id,
      name: app.form_data?.name || 'Не указано',
      surname: app.form_data?.surname || 'Не указано',
      phone: app.form_data?.phone || 'Не указано',
      created_at: app.created_at
    })) || [];

    // Get applications requiring fixes
    const requiresFixes = applications?.filter(app => app.requires_fixes).map(app => ({
      id: app.id,
      name: app.form_data?.name || 'Не указано',
      surname: app.form_data?.surname || 'Не указано',
      comment: app.fix_comment || 'Комментарий не указан',
      created_at: app.created_at
    })) || [];

    // Build analytics breakdowns
    const regionBreakdown: { [key: string]: number } = {};
    const professionBreakdown: { [key: string]: number } = {};
    const salaryBreakdown: { [key: string]: number } = {};
    const caseTypeBreakdown: { [key: string]: number } = {};

    applications?.forEach(app => {
      // Region breakdown
      const region = app.form_data?.destinationCountry || 'Не указано';
      regionBreakdown[region] = (regionBreakdown[region] || 0) + 1;

      // Profession breakdown
      const profession = app.form_data?.profession || 'Не указано';
      professionBreakdown[profession] = (professionBreakdown[profession] || 0) + 1;

      // Salary breakdown
      const income = app.form_data?.income;
      let salaryRange = 'Не указано';
      if (income) {
        const incomeNum = parseInt(income);
        if (incomeNum < 50000) salaryRange = 'До 22,500,000 теңге';
        else if (incomeNum < 100000) salaryRange = '22,500,000 - 45,000,000 теңге';
        else if (incomeNum < 200000) salaryRange = '45,000,000 - 90,000,000 теңге';
        else salaryRange = 'Свыше 90,000,000 теңге';
      }
      salaryBreakdown[salaryRange] = (salaryBreakdown[salaryRange] || 0) + 1;

      // Case type breakdown (based on visa status)
      const caseType = app.visa_status || 'ожидает';
      caseTypeBreakdown[caseType] = (caseTypeBreakdown[caseType] || 0) + 1;
    });

    // Monthly submissions (last 12 months)
    const monthlySubmissions = [];
    const currentDate = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      
      const count = applications?.filter(app => {
        const appDate = new Date(app.created_at);
        return appDate >= monthStart && appDate <= monthEnd;
      }).length || 0;

      monthlySubmissions.push({
        month: date.toLocaleDateString('ru-RU', { month: 'short', year: 'numeric' }),
        count
      });
    }

    const analyticsData = {
      metrics: {
        totalClients,
        paidClients,
        submittedApplications,
        totalRevenue
      },
      pendingPayments,
      requiresFixes,
      regionBreakdown,
      professionBreakdown,
      salaryBreakdown,
      caseTypeBreakdown,
      monthlySubmissions
    };

    return res.status(200).json(analyticsData);

  } catch (error) {
    console.error('Analytics API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 