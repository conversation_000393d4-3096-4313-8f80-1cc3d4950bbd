import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { AddClientFormData } from '../../../types/admin';
import { enforceClientStatusBusinessRules } from '../../../utils/clientStatusValidation';
import { 
  PaymentStatus, 
  ClientProgressStatus, 
  UserRole, 
  VisaStatus,
  StepStatus,
  parseUserRole 
} from '../../../types/universal-status';
import { 
  CLIENT_PROGRESS_STATUS,
  SERVICE_PAYMENT_STATUS,
  VISA_STATUS,
  getCountryDisplayName 
} from '../../../types/client-status';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const clientData: AddClientFormData & { company_id?: string, userRole?: string } = req.body;

    // Restrict superadmin access to adding paid clients - using type-safe comparison
    const userRole = parseUserRole(clientData.userRole || '');
    if (userRole === UserRole.SUPER_ADMIN) {
      return res.status(403).json({ 
        error: 'Доступ запрещен', 
        message: 'Суперадминистраторы не могут добавлять оплаченных клиентов. Эта функция доступна только визовым компаниям.' 
      });
    }

    // Validate required fields
    if (!clientData.name || !clientData.surname || !clientData.phone || !clientData.service_package_id) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Check if client with this phone number already exists
    const cleanPhone = clientData.phone.replace(/\D/g, '');
    const normalizePhone = (phone: string) => {
      // Remove all non-digits
      const digits = phone.replace(/\D/g, '');
      // If starts with 8, replace with 7
      if (digits.startsWith('8') && digits.length === 11) {
        return '7' + digits.substring(1);
      }
      // If starts with +7, remove +
      if (digits.startsWith('7') && digits.length === 11) {
        return digits;
      }
      return digits;
    };

    const normalizedPhone = normalizePhone(clientData.phone);
    const { data: existingClient, error: checkError } = await supabase
      .from('visa_applications')
      .select('id, phone_number, form_data')
      .or(`phone_number.eq.${clientData.phone},phone_number.eq.${cleanPhone},phone_number.eq.+${normalizedPhone}`)
      .limit(1);

    if (checkError) {
      console.error('Error checking existing client:', checkError);
      return res.status(500).json({ error: 'Database error while checking existing client' });
    }

    if (existingClient && existingClient.length > 0) {
      const existingClientData = existingClient[0];
      const clientName = existingClientData.form_data?.name && existingClientData.form_data?.surname 
        ? `${existingClientData.form_data.name} ${existingClientData.form_data.surname}`
        : 'неизвестно';
      
      return res.status(409).json({ 
        error: 'phoneAlreadyExists',
        message: 'Клиент с таким номером телефона уже существует',
        details: `Номер ${clientData.phone} уже зарегистрирован в системе (Клиент: ${clientName})`,
        translationKey: 'clients.modal.phoneAlreadyExists',
        translationParams: {
          phone: clientData.phone,
          clientName: clientName
        }
      });
    }

    // Get package details
    const { data: packageData, error: packageError } = await supabase
      .from('price_list')
      .select('*')
      .eq('id', clientData.service_package_id)
      .single();

    if (packageError || !packageData) {
      return res.status(400).json({ error: 'Invalid service package' });
    }

    // Generate unique agent_id for manager-added clients
    const generateAgentId = () => {
      const timestamp = Date.now().toString(36);
      const randomString = Math.random().toString(36).substring(2, 8);
      return `manager_${timestamp}_${randomString}`;
    };

    // Apply business rule validation for new client - using type-safe status values
    const statusUpdate = enforceClientStatusBusinessRules({
      client_progress_status: ClientProgressStatus.PAID_PACKAGE,
      service_payment_status: PaymentStatus.PAID,
      service_package_id: clientData.service_package_id
    });

    // Create new visa application record
    const visaDestination = clientData.country || packageData.country;
    const newApplication = {
      agent_id: generateAgentId(), // Unique ID for manager-added clients
      form_data: {
        name: clientData.name,
        surname: clientData.surname,
        visaCountry: visaDestination,
        // Basic form structure - will be filled by client later
        step: 1
      },
      step_status: 0, // Technical questionnaire progress: 0 = not started (0-9 scale)
      uploaded_files: {},
      phone_number: clientData.phone,
      whatsapp_redirected: false,
      company_id: (clientData.company_id && clientData.company_id !== 'all') ? clientData.company_id : packageData.company_id,
      service_payment_status: statusUpdate.service_payment_status,
      service_package_id: clientData.service_package_id,
      is_paid: true, // Required by check_payment_consistency constraint - clients with packages are paid
      visa_status: VisaStatus.PENDING,
      client_progress_status: statusUpdate.client_progress_status,
      consular_fee_paid: clientData.consular_fee_paid,
      requires_fixes: false,
      added_by_manager: true, // Special flag to identify manager-added clients
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: insertedApplication, error: insertError } = await supabase
      .from('visa_applications')
      .insert([newApplication])
      .select()
      .single();

    if (insertError) {
      console.error('Database insert error:', insertError);
      return res.status(500).json({ error: 'Failed to create client record' });
    }

    // Log the successful creation with the generated agent_id
    console.log(`New client added: ${clientData.name} ${clientData.surname} (${clientData.phone})`);
    console.log(`Agent ID: ${insertedApplication.agent_id}`);
    console.log(`Package: ${packageData.title} for ${packageData.country}`);
    console.log(`Consular fee paid: ${clientData.consular_fee_paid}`);

    // Get WhatsApp credentials and company details from companies table
    let wapiToken = null;
    let wapiProfileId = null;
    let companyName = 'Visa Pro'; // Default fallback
    let companySlug = 'visa-pro-kz'; // Default fallback
    
    try {
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select('id, name, slug, wapi_token, wapi_profile_id')
        .eq('id', newApplication.company_id)
        .single();
      
      if (!companyError && companyData) {
        wapiToken = companyData.wapi_token;
        wapiProfileId = companyData.wapi_profile_id;
        companyName = companyData.name || 'Visa Pro';
        companySlug = companyData.slug || 'visa-pro-kz';
        console.log('Found company details:', { name: companyName, slug: companySlug });
      } else {
        console.log('No company data found for company:', newApplication.company_id);
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
    }

    // Send WhatsApp notification if credentials are available
    if (wapiToken && wapiProfileId) {
      try {
        // Clean phone number for WhatsApp (remove all non-digits including +)
        const whatsappPhone = clientData.phone.replace(/[^\d]/g, '');
        
        // Format message with dynamic company details
        const message = `Добрый день, спасибо что вы доверяете нам и поздравляем вам с покупкой!\nМеня зовут Алишер, я визовик компании ${companyName}\n\nДавайте начнем подачу, для начала пожалуйста заполните анкету для генерации кейса, эти данные нужны чтобы составить вам тур маршрут, отвечайте максимально честно и детально\n\nhttps://visaai.vercel.app/${companySlug}`;
        
        const whatsappResponse = await fetch(`https://wappi.pro/api/sync/message/send?profile_id=${wapiProfileId}`, {
          method: 'POST',
          headers: {
            'accept': 'application/json',
            'Authorization': wapiToken,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            body: message,
            recipient: whatsappPhone
          })
        });

        const whatsappResult = await whatsappResponse.json();
        
        if (whatsappResponse.ok) {
          console.log('WhatsApp notification sent successfully:', whatsappResult);
        } else {
          console.error('WhatsApp API error:', whatsappResult);
          console.error('Response status:', whatsappResponse.status);
          // Log but don't fail the request
          if (whatsappResult.detail === 'Authorization token incorrect') {
            console.error('Invalid WAPI token. Please update the token in the companies table.');
          }
        }
      } catch (whatsappError) {
        console.error('Error sending WhatsApp notification:', whatsappError);
        // Don't fail the whole request if WhatsApp fails
      }
    }

    return res.status(201).json({
      success: true,
      client: insertedApplication,
      message: 'Client successfully added. Bot will send questionnaire link.'
    });

  } catch (error) {
    console.error('Error adding client:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 