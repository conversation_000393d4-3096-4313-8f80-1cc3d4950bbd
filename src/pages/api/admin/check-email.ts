import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import { createClient } from '@supabase/supabase-js';

// Create admin client for user management
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { email } = req.body;

  if (!email) {
    return res.status(400).json({ error: 'Email is required' });
  }

  try {
    // Check if employee with this email exists in database
    const { data: existingEmployee } = await supabase
      .from('employees')
      .select('id, email, is_active')
      .eq('email', email)
      .single();

    // Check if user with this email exists in Supabase Auth
    let authUserExists = false;
    let authUserId = null;
    try {
      const { data: users } = await supabaseAdmin.auth.admin.listUsers();
      const existingAuthUser = users.users.find(u => u.email === email);
      if (existingAuthUser) {
        authUserExists = true;
        authUserId = existingAuthUser.id;
      }
    } catch (authListError) {
      console.error('Failed to check existing auth users:', authListError);
    }

    return res.status(200).json({
      email,
      databaseRecord: existingEmployee ? {
        id: existingEmployee.id,
        is_active: existingEmployee.is_active
      } : null,
      authUser: authUserExists ? {
        id: authUserId
      } : null,
      available: !existingEmployee && !authUserExists,
      issues: {
        orphanedAuthUser: !existingEmployee && authUserExists,
        orphanedDatabaseRecord: existingEmployee && !authUserExists
      }
    });
  } catch (error) {
    console.error('Error checking email:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 