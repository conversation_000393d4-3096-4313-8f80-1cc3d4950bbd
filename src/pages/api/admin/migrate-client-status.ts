import type { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';

interface MigrationResult {
  success: boolean;
  message: string;
  rowsUpdated?: number;
  beforeCount?: number;
  afterCount?: number;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<MigrationResult>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed. Use POST.'
    });
  }

  try {
    console.log('🚀 Starting client status migration...');

    // Step 1: Count clients with incorrect status before migration
    const { data: beforeData, error: beforeError } = await supabase
      .from('visa_applications')
      .select('id', { count: 'exact' })
      .eq('service_payment_status', 'оплачено')
      .eq('client_progress_status', 'Прошли опросник');

    if (beforeError) {
      console.error('❌ Error counting before migration:', beforeError);
      return res.status(500).json({
        success: false,
        message: 'Failed to count records before migration',
        error: beforeError.message
      });
    }

    const beforeCount = beforeData?.length || 0;
    console.log(`📊 Found ${beforeCount} clients with incorrect status (paid but showing 'Прошли опросник')`);

    if (beforeCount === 0) {
      return res.status(200).json({
        success: true,
        message: 'No migration needed. All paid clients already have correct status.',
        rowsUpdated: 0,
        beforeCount: 0,
        afterCount: 0
      });
    }

    // Step 2: Update client_progress_status for paid clients
    const { data: updateData, error: updateError } = await supabase
      .from('visa_applications')
      .update({
        client_progress_status: 'оплатили пакет',
        updated_at: new Date().toISOString()
      })
      .eq('service_payment_status', 'оплачено')
      .eq('client_progress_status', 'Прошли опросник')
      .select('id');

    if (updateError) {
      console.error('❌ Error updating client statuses:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to update client statuses',
        error: updateError.message,
        beforeCount
      });
    }

    const rowsUpdated = updateData?.length || 0;
    console.log(`✅ Updated ${rowsUpdated} client records`);

    // Step 3: Verify the migration by counting remaining incorrect records
    const { data: afterData, error: afterError } = await supabase
      .from('visa_applications')
      .select('id', { count: 'exact' })
      .eq('service_payment_status', 'оплачено')
      .eq('client_progress_status', 'Прошли опросник');

    if (afterError) {
      console.error('❌ Error counting after migration:', afterError);
      return res.status(500).json({
        success: false,
        message: 'Migration completed but verification failed',
        error: afterError.message,
        rowsUpdated,
        beforeCount
      });
    }

    const afterCount = afterData?.length || 0;
    console.log(`📊 After migration: ${afterCount} clients still have incorrect status`);

    // Step 4: Get sample of updated records for verification
    const { data: sampleData, error: sampleError } = await supabase
      .from('visa_applications')
      .select('id, agent_id, service_payment_status, client_progress_status, step_status, updated_at')
      .eq('service_payment_status', 'оплачено')
      .eq('client_progress_status', 'оплатили пакет')
      .order('updated_at', { ascending: false })
      .limit(5);

    if (!sampleError && sampleData) {
      console.log('📋 Sample updated records:', sampleData);
    }

    const migrationSuccess = rowsUpdated > 0 && afterCount === 0;

    return res.status(200).json({
      success: migrationSuccess,
      message: migrationSuccess 
        ? `Migration completed successfully! Updated ${rowsUpdated} client records.`
        : `Migration partially completed. Updated ${rowsUpdated} records, but ${afterCount} still need attention.`,
      rowsUpdated,
      beforeCount,
      afterCount
    });

  } catch (error) {
    console.error('❌ Unexpected error during migration:', error);
    return res.status(500).json({
      success: false,
      message: 'Unexpected error during migration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
} 