import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { clientId } = req.query;

  if (!clientId) {
    return res.status(400).json({ error: 'Client ID is required' });
  }

  try {
    if (req.method === 'PUT') {
      const { workflowStep } = req.body;

      if (!workflowStep) {
        return res.status(400).json({ error: 'Workflow step is required' });
      }

      // First get the current form_data
      const { data: currentData, error: fetchError } = await supabase
        .from('visa_applications')
        .select('form_data')
        .eq('id', clientId)
        .single();

      if (fetchError) {
        console.error('Error fetching current client:', fetchError);
        return res.status(500).json({ error: 'Failed to fetch current client' });
      }

      // Update with merged form_data
      const updatedFormData = {
        ...currentData.form_data,
        workflowStep: workflowStep
      };

      const { data, error } = await supabase
        .from('visa_applications')
        .update({
          form_data: updatedFormData,
          updated_at: new Date().toISOString()
        })
        .eq('id', clientId)
        .select()
        .single();

      if (error) {
        console.error('Error updating workflow:', error);
        return res.status(500).json({ error: 'Failed to update workflow' });
      }

      return res.status(200).json({ success: true, client: data });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Workflow API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 