import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { enforceClientStatusBusinessRules } from '../../../../utils/clientStatusValidation';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { clientId } = req.query;

  if (!clientId) {
    return res.status(400).json({ error: 'Client ID is required' });
  }

  try {
    if (req.method === 'DELETE') {
      // Delete client
      const { error } = await supabase
        .from('visa_applications')
        .delete()
        .eq('id', clientId);

      if (error) {
        console.error('Error deleting client:', error);
        return res.status(500).json({ error: 'Failed to delete client' });
      }

      return res.status(200).json({ success: true, message: 'Client deleted successfully' });
    }

    if (req.method === 'PUT') {
      // Update client (same as in clients.ts but for consistency)
      const clientData = req.body;

      // First get the current client data
      const { data: currentData, error: fetchError } = await supabase
        .from('visa_applications')
        .select('form_data, client_progress_status, service_payment_status, service_package_id')
        .eq('id', clientId)
        .single();

      if (fetchError) {
        console.error('Error fetching current client:', fetchError);
        return res.status(500).json({ error: 'Failed to fetch current client' });
      }

      // Update with merged form_data
      const updatedFormData = {
        ...currentData.form_data,
        firstName: clientData.firstName,
        lastName: clientData.lastName,
        phone: clientData.phone,
        email: clientData.email,
        profession: clientData.profession,
        income: clientData.income,
        destinationCountry: clientData.destinationCountry,
        maritalStatus: clientData.maritalStatus,
        isPaid: clientData.isPaid,
        isPaidConsularFee: clientData.isPaidConsularFee,
        workflowStep: clientData.workflowStep,
        visa_status: clientData.status,
        hasSponsor: clientData.hasSponsor,
        hasInvitation: clientData.hasInvitation,
        hasPreviousRejections: clientData.hasPreviousRejections
      };

      // Apply business rule validation before updating
      const statusUpdate = {
        client_progress_status: clientData.client_progress_status || currentData.client_progress_status,
        service_payment_status: clientData.service_payment_status || currentData.service_payment_status,
        service_package_id: clientData.service_package_id || currentData.service_package_id
      };
      
      const validatedUpdate = enforceClientStatusBusinessRules(statusUpdate);

      const { data, error } = await supabase
        .from('visa_applications')
        .update({
          form_data: updatedFormData,
          client_progress_status: validatedUpdate.client_progress_status,
          service_payment_status: validatedUpdate.service_payment_status,
          updated_at: new Date().toISOString()
        })
        .eq('id', clientId)
        .select()
        .single();

      if (error) {
        console.error('Error updating client:', error);
        return res.status(500).json({ error: 'Failed to update client' });
      }

      return res.status(200).json({ success: true, client: data });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Client API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 