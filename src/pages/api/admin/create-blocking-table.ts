import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

// Create admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Creating company blocking table and records...');

    // List of company IDs
    const companies = [
      '550e8400-e29b-41d4-a716-446655440001',
      '550e8400-e29b-41d4-a716-446655440002', 
      '550e8400-e29b-41d4-a716-446655440003'
    ];

    // Create blocking records for each company
    const blockingRecords = companies.map(companyId => ({
      company_id: companyId,
      is_blocked: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    // Try to insert records (this will create the table structure if it doesn't exist)
    const { data: insertedRecords, error: insertError } = await supabaseAdmin
      .from('company_blocking_status')
      .insert(blockingRecords)
      .select();

    if (insertError) {
      console.error('Insert error:', insertError);
      
      // If table doesn't exist, we need to create it manually
      if (insertError.message?.includes('does not exist') || insertError.code === '42P01') {
        return res.status(500).json({ 
          error: 'Table company_blocking_status does not exist. Please create it in Supabase SQL editor first.',
          sql: `
CREATE TABLE company_blocking_status (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  is_blocked BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id)
);

ALTER TABLE company_blocking_status ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated users to read and write company blocking status" 
ON company_blocking_status FOR ALL 
TO authenticated 
USING (true) 
WITH CHECK (true);
          `
        });
      }
      
      return res.status(500).json({ error: 'Failed to create blocking records: ' + insertError.message });
    }

    console.log('Successfully created blocking records');

    return res.status(200).json({
      success: true,
      message: `Successfully created ${insertedRecords?.length || 0} company blocking records.`,
      records: insertedRecords
    });

  } catch (error) {
    console.error('Setup error:', error);
    return res.status(500).json({ error: 'Setup failed: ' + error });
  }
} 