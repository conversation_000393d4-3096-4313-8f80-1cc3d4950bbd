import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'PUT') {
      // Update admin profile
      const { username, email, currentPassword, newPassword } = req.body;

      // In a real app, you would:
      // 1. Verify the current password
      // 2. Hash the new password if provided
      // 3. Update the user in Supabase Auth
      // 4. Update profile information

      // For now, we'll just simulate a successful update
      if (newPassword && newPassword !== currentPassword) {
        // Simulate password validation
        if (!currentPassword) {
          return res.status(400).json({ error: 'Current password is required to change password' });
        }
      }

      // Simulate updating profile
      return res.status(200).json({ 
        success: true, 
        message: 'Profile updated successfully',
        profile: {
          username,
          email
        }
      });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Profile API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 