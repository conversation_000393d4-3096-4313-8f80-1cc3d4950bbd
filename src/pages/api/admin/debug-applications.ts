// Debug API to check application status distribution

import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Debugging application status distribution...');

    // Get all applications with key fields
    const { data: allApps, error } = await supabase
      .from('visa_applications')
      .select(`
        id,
        step_status,
        client_progress_status,
        service_payment_status,
        created_at,
        form_data,
        added_by_manager
      `)
      .order('created_at', { ascending: false })
      .limit(500);

    if (error) {
      throw error;
    }

    // Analyze step_status distribution
    const stepStatusDistribution = allApps?.reduce((acc, app) => {
      const status = app.step_status || 0;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<number, number>) || {};

    // Analyze client_progress_status distribution
    const progressStatusDistribution = allApps?.reduce((acc, app) => {
      const status = app.client_progress_status || 'null';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Find completed questionnaires (step_status = 9)
    const completedQuestionnaires = allApps?.filter(app => app.step_status >= 9) || [];

    // Check their client_progress_status values
    const completedStatusDistribution = completedQuestionnaires.reduce((acc, app) => {
      const status = app.client_progress_status || 'null';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Find applications with form_data
    const appsWithFormData = allApps?.filter(app => 
      app.form_data && 
      typeof app.form_data === 'object' && 
      Object.keys(app.form_data).length > 0
    ) || [];

    // Check recent applications (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentApps = allApps?.filter(app => 
      new Date(app.created_at) >= thirtyDaysAgo
    ) || [];

    // Find potential "lost" completed applications
    const potentialLostApps = allApps?.filter(app => {
      const hasFormData = app.form_data && Object.keys(app.form_data).length > 5;
      const hasHighStepStatus = app.step_status >= 9;
      const hasNoProgressStatus = !app.client_progress_status;
      
      return hasFormData && hasHighStepStatus && hasNoProgressStatus;
    }) || [];

    // Case sensitivity check
    const caseSensitivityIssues = allApps?.filter(app => 
      app.client_progress_status && 
      app.client_progress_status.includes('прошли') && 
      app.client_progress_status !== 'Прошли опросник'
    ) || [];

    const debugInfo = {
      totalApplications: allApps?.length || 0,
      stepStatusDistribution,
      progressStatusDistribution,
      completedQuestionnaires: {
        count: completedQuestionnaires.length,
        statusDistribution: completedStatusDistribution,
        sampleIds: completedQuestionnaires.slice(0, 5).map(app => ({
          id: app.id,
          step_status: app.step_status,
          client_progress_status: app.client_progress_status,
          created_at: app.created_at
        }))
      },
      appsWithFormData: {
        count: appsWithFormData.length,
        percentage: allApps?.length ? Math.round((appsWithFormData.length / allApps.length) * 100) : 0
      },
      recentApplications: {
        count: recentApps.length,
        completedInRecent: recentApps.filter(app => app.step_status >= 9).length
      },
      potentialLostApplications: {
        count: potentialLostApps.length,
        sampleIds: potentialLostApps.slice(0, 3).map(app => ({
          id: app.id,
          step_status: app.step_status,
          client_progress_status: app.client_progress_status,
          form_data_keys: app.form_data ? Object.keys(app.form_data).length : 0
        }))
      },
      caseSensitivityIssues: {
        count: caseSensitivityIssues.length,
        issues: caseSensitivityIssues.map(app => ({
          id: app.id,
          incorrect_status: app.client_progress_status
        }))
      }
    };

    console.log('📊 Debug Results:', debugInfo);

    res.status(200).json({
      success: true,
      debug: debugInfo
    });

  } catch (error) {
    console.error('❌ Error debugging applications:', error);
    res.status(500).json({ 
      error: 'Failed to debug applications',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}