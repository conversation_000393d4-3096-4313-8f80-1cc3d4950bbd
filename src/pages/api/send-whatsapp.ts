import { NextApiRequest, NextApiResponse } from 'next';
import { normalizePhoneNumber } from '../../utils/validations';
import { createClient } from '@supabase/supabase-js';
import { isFeatureEnabled } from '../../config/featureFlags';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface WhatsAppRequest {
  phone: string;
  agentId?: string;
}

interface WappiApiResponse {
  status: string;
  timestamp: number;
  time: string;
  message_id: string;
  task_id: string;
  uuid?: string;
  message?: string;
}

interface WhatsAppResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: WappiApiResponse;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<WhatsAppResponse>
) {
  console.log('WhatsApp API called with method:', req.method);
  console.log('Request body:', req.body);

  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { phone, agentId }: WhatsAppRequest = req.body;

    if (!phone) {
      console.log('No phone number provided');
      return res.status(400).json({ success: false, error: 'Phone number is required' });
    }

    console.log('Original phone number:', phone);
    console.log('Agent ID:', agentId);

    // Use the same normalization function as in the rest of the app
    const normalizedPhone = normalizePhoneNumber(phone);
    // Remove the + for WhatsApp API (it expects just digits)
    const formattedPhone = normalizedPhone.replace(/^\+/, '');

    console.log('Normalized phone number:', normalizedPhone);
    console.log('Formatted phone number for WhatsApp:', formattedPhone);

    // Get company information and payment status from the application
    let companyName = 'Visa Pro'; // Default fallback
    let wapiToken = 'ff033765135d4167dc8c341d7b10629f31e1a69d'; // Default fallback
    let wapiProfileId = '3f5631a4-b23f'; // Default fallback
    let isPaid = false;
    const checkPaymentFeatureEnabled = isFeatureEnabled('CHECK_PAYMENT_STATUS');

    if (agentId) {
      try {
        // Get application data to find company_id and payment status (if feature is enabled)
        const selectFields = checkPaymentFeatureEnabled 
          ? 'company_id, service_payment_status, is_paid'
          : 'company_id';
          
        const { data: applicationData, error: appError } = await supabase
          .from('visa_applications')
          .select(selectFields)
          .eq('agent_id', agentId)
          .single();

        if (!appError && applicationData) {
          // Check payment status only if feature is enabled
          if (checkPaymentFeatureEnabled) {
            isPaid = applicationData.service_payment_status === 'оплачено' || applicationData.is_paid;
            console.log('Payment status check enabled:', { service_payment_status: applicationData.service_payment_status, is_paid: applicationData.is_paid, isPaid });
          } else {
            console.log('Payment status check disabled by feature flag');
          }

          if (applicationData.company_id) {
            // Get company details and WhatsApp credentials
            const { data: companyData, error: companyError } = await supabase
              .from('companies')
              .select('id, name, wapi_token, wapi_profile_id')
              .eq('id', applicationData.company_id)
              .single();

            if (!companyError && companyData) {
              companyName = companyData.name || companyName;
              wapiToken = companyData.wapi_token || wapiToken;
              wapiProfileId = companyData.wapi_profile_id || wapiProfileId;
              console.log('Found company details:', { name: companyName, id: applicationData.company_id });
            }
          }
        }
      } catch (error) {
        console.error('Error fetching application data:', error);
      }
    }

    // Different messages based on payment status (if feature is enabled)
    let message: string;
    if (checkPaymentFeatureEnabled && isPaid) {
      // If already paid, ask to answer additional questions
      message = `Вы успешно заполнили анкету! ✅\n\nПожалуйста, ответьте на следующие вопросы для завершения процесса оформления визы.\n\nНаш специалист свяжется с вами в ближайшее время.`;
    } else if (checkPaymentFeatureEnabled && !isPaid) {
      // If not paid, ask to pay the manager
      message = `Вы успешно заполнили анкету! ✅\n\nПожалуйста, оплатите услуги у менеджера компании ${companyName}.\n\nПосле оплаты наш специалист свяжется с вами для дальнейшего оформления визы.`;
    } else {
      // Default message when feature is disabled
      message = `Спасибо за заполнение анкеты! 🎉\n\nВаша заявка успешно отправлена в компанию ${companyName}.\n\nНаш специалист свяжется с вами в ближайшее время для обсуждения дальнейших шагов по получению визы.\n\nОжидайте нашего звонка!`;
    }

    const apiUrl = `https://wappi.pro/api/sync/message/send?profile_id=${wapiProfileId}`;
    
    console.log('Sending request to Wappi API...');
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Authorization': wapiToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        body: message,
        recipient: formattedPhone
      })
    });

    console.log('Wappi API response status:', response.status);
    
    const responseData = await response.json();
    console.log('Wappi API response data:', responseData);

    if (!response.ok) {
      console.error('WhatsApp API error:', responseData);
      return res.status(response.status).json({ 
        success: false, 
        error: `WhatsApp API error: ${responseData.message || 'Unknown error'}`,
        data: responseData
      });
    }

    console.log('WhatsApp message sent successfully:', responseData);
    
    return res.status(200).json({ 
      success: true, 
      message: 'WhatsApp message sent successfully',
      data: responseData
    });

  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to send WhatsApp message' 
    });
  }
} 