import { NextApiRequest, NextApiResponse } from 'next';
import { IncomingForm, Fields } from 'formidable';
import fs from 'fs';
import path from 'path';

export const config = {
  api: {
    bodyParser: false,
  },
};

type ProcessedFiles = {
  [key: string]: {
    filepath: string;
    originalFilename?: string;
    mimetype?: string;
    size?: number;
  };
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  console.log('=== OCR SIMPLE API HANDLER CALLED ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Headers:', {
    contentType: req.headers['content-type'],
    contentLength: req.headers['content-length']
  });

  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed',
      message: `Method ${req.method} not allowed. Only POST is supported.`
    });
  }

  try {
    console.log('Setting up form parser...');
    
    const form = new IncomingForm({
      keepExtensions: true,
      multiples: false,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowEmptyFiles: false,
      uploadDir: process.env.VERCEL ? '/tmp' : path.join(process.cwd(), 'tmp'),
    });

    // Create tmp directory if not in Vercel
    if (!process.env.VERCEL) {
      const tmpDir = path.join(process.cwd(), 'tmp');
      if (!fs.existsSync(tmpDir)) {
        fs.mkdirSync(tmpDir, { recursive: true });
      }
    }

    console.log('Parsing form data...');
    const formData: { fields: Fields; files: ProcessedFiles } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) {
          console.error('Form parsing error:', err);
          return reject(err);
        }

        console.log('Form parsed successfully');
        console.log('File keys:', Object.keys(files));
        resolve({ fields, files: files as unknown as ProcessedFiles });
      });
    });

    const fileKey = Object.keys(formData.files)[0];
    if (!fileKey) {
      console.error('No file found in upload');
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    let file = formData.files[fileKey];
    if (Array.isArray(file)) {
      file = file[0];
    }

    console.log('File received:', {
      filename: file.originalFilename || 'unnamed',
      mimetype: file.mimetype || 'unknown',
      size: file.size ? `${(file.size / (1024 * 1024)).toFixed(2)} MB` : 'unknown'
    });

    // Validate file exists
    if (!file || !file.filepath || !fs.existsSync(file.filepath)) {
      console.error('Invalid file or filepath');
      return res.status(400).json({
        success: false,
        error: 'Invalid file upload'
      });
    }

    // Get file stats
    const stats = fs.statSync(file.filepath);
    console.log('File stats:', {
      size: stats.size,
      isFile: stats.isFile()
    });

    // Clean up temp file
    try {
      fs.unlinkSync(file.filepath);
      console.log('Temp file cleaned up');
    } catch (cleanupError) {
      console.warn('Failed to clean up temp file:', cleanupError);
    }

    // Return success without OCR processing for now
    return res.status(200).json({
      success: true,
      message: 'File uploaded and processed successfully (simplified version)',
      fileInfo: {
        filename: file.originalFilename,
        mimetype: file.mimetype,
        size: stats.size
      }
    });

  } catch (error) {
    console.error('OCR Simple API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to process file',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export default handler;
