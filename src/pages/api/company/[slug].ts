import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import { Company } from '../../../types/admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log(`[Company API] ${req.method} request for slug: ${req.query.slug}`);
  console.log(`[Company API] Request headers:`, {
    host: req.headers.host,
    'user-agent': req.headers['user-agent'],
    'x-forwarded-for': req.headers['x-forwarded-for'],
    'x-forwarded-proto': req.headers['x-forwarded-proto']
  });

  try {
    if (req.method !== 'GET') {
      console.log(`[Company API] Method not allowed: ${req.method}`);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { slug } = req.query;

    if (!slug || typeof slug !== 'string') {
      console.log(`[Company API] Invalid slug parameter:`, slug);
      return res.status(400).json({ error: 'Slug is required' });
    }

    console.log(`[Company API] Fetching company with slug: ${slug}`);

    // Fetch company by slug
    const { data: company, error } = await supabase
      .from('companies')
      .select('*')
      .eq('slug', slug)
      .eq('is_blocked', false) // Only return active companies
      .single();

    if (error) {
      console.error(`[Company API] Supabase error:`, error);
      if (error.code === 'PGRST116') {
        // No rows returned
        console.log(`[Company API] No company found with slug: ${slug}`);
        return res.status(404).json({ error: 'Company not found' });
      }
      return res.status(500).json({ error: 'Database error', details: error.message });
    }

    if (!company) {
      console.log(`[Company API] Company data is null for slug: ${slug}`);
      return res.status(404).json({ error: 'Company not found' });
    }

    console.log(`[Company API] Found company: ${company.name} (ID: ${company.id})`);

    const companyData: Company = {
      id: company.id,
      name: company.name,
      phone_number: company.phone_number,
      email: company.email,
      is_blocked: company.is_blocked,
      slug: company.slug,
      created_at: company.created_at,
      updated_at: company.updated_at
    };

    console.log(`[Company API] Returning company data for: ${companyData.name}`);

    return res.status(200).json({ company: companyData });
  } catch (error) {
    console.error('[Company API] Unexpected error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
} 