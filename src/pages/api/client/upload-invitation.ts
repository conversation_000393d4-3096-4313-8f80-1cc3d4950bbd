import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../utils/supabase';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

// Disable default body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

// Allowed file types for invitations
const ALLOWED_MIME_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
  'application/msword', // DOC
  'image/png',
  'image/jpeg',
  'image/jpg'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the form data
    const form = formidable({
      maxFileSize: MAX_FILE_SIZE,
      keepExtensions: true,
    });

    const [fields, files] = await form.parse(req);
    
    const agentId = Array.isArray(fields.agentId) ? fields.agentId[0] : fields.agentId;
    const applicationId = Array.isArray(fields.applicationId) ? fields.applicationId[0] : fields.applicationId;
    const file = Array.isArray(files.invitation) ? files.invitation[0] : files.invitation;

    if (!agentId) {
      return res.status(400).json({ error: 'Agent ID is required' });
    }

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Validate file type
    if (!ALLOWED_MIME_TYPES.includes(file.mimetype || '')) {
      return res.status(400).json({ 
        error: 'Invalid file type. Allowed types: PDF, DOCX, DOC, PNG, JPG' 
      });
    }

    // Find application by agent ID or application ID
    let applicationQuery = supabase
      .from('visa_applications')
      .select('id, agent_id');

    if (applicationId) {
      applicationQuery = applicationQuery.eq('id', applicationId);
    } else {
      applicationQuery = applicationQuery.eq('agent_id', agentId);
    }

    const { data: application, error: fetchError } = await applicationQuery.single();

    if (fetchError || !application) {
      return res.status(404).json({ error: 'Application not found' });
    }

    // Read file content
    const fileContent = fs.readFileSync(file.filepath);
    
    // Generate unique filename
    const fileExtension = path.extname(file.originalFilename || '');
    const fileName = `invitation_${application.id}_${Date.now()}${fileExtension}`;
    const filePath = `${application.agent_id}/${fileName}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('invitations')
      .upload(filePath, fileContent, {
        contentType: file.mimetype || 'application/octet-stream',
        upsert: false
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      return res.status(500).json({ error: 'Failed to upload file' });
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('invitations')
      .getPublicUrl(filePath);

    // Update application with invitation file URL
    const { error: updateError } = await supabase
      .from('visa_applications')
      .update({ 
        invitation_file_url: publicUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', application.id);

    if (updateError) {
      console.error('Database update error:', updateError);
      return res.status(500).json({ error: 'Failed to update application' });
    }

    // Clean up temporary file
    try {
      fs.unlinkSync(file.filepath);
    } catch (cleanupError) {
      console.warn('Failed to clean up temporary file:', cleanupError);
    }

    return res.status(200).json({
      success: true,
      message: 'Invitation uploaded successfully',
      fileUrl: publicUrl,
      fileName: file.originalFilename
    });

  } catch (error) {
    console.error('Upload invitation error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 