import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface CompanyFilterContextType {
  companyId: string;
  setCompanyId: (id: string) => void;
}

const CompanyFilterContext = createContext<CompanyFilterContextType | undefined>(undefined);

export const useCompanyFilter = () => {
  const context = useContext(CompanyFilterContext);
  if (!context) {
    throw new Error('useCompanyFilter must be used within a CompanyFilterProvider');
  }
  return context;
};

interface CompanyFilterProviderProps {
  children: ReactNode;
}

export const CompanyFilterProvider: React.FC<CompanyFilterProviderProps> = ({ children }) => {
  const [companyId, setCompanyId] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('selectedCompanyId') || 'all';
    }
    return 'all';
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedCompanyId', companyId);
    }
  }, [companyId]);

  // Optionally, sync with URL or localStorage if needed

  return (
    <CompanyFilterContext.Provider value={{ companyId, setCompanyId }}>
      {children}
    </CompanyFilterContext.Provider>
  );
}; 