import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/router';

interface DateRange {
  from: string;
  to: string;
}

interface DateFilterContextType {
  dateRange: DateRange | null;
  setDateRange: (range: DateRange | null) => void;
  resetDateRange: () => void;
  isDefaultRange: boolean;
  ensureIncludesNewClients: () => void;
  disableDateFilter: () => void;
  isDateFilterDisabled: boolean;
}

const DateFilterContext = createContext<DateFilterContextType | undefined>(undefined);

export const useDateFilter = () => {
  const context = useContext(DateFilterContext);
  if (!context) {
    throw new Error('useDateFilter must be used within a DateFilterProvider');
  }
  return context;
};

const getDefaultDateRange = (): DateRange => {
  const to = new Date();
  const from = new Date();
  // Show all applications from the beginning of time (2020) to ensure nothing is missed
  from.setFullYear(2020, 0, 1); // January 1, 2020
  // Add 2 days to 'to' date to ensure newly created clients are included
  to.setDate(to.getDate() + 2);
  return {
    from: from.toISOString().split('T')[0],
    to: to.toISOString().split('T')[0]
  };
};

interface DateFilterProviderProps {
  children: ReactNode;
}

export const DateFilterProvider: React.FC<DateFilterProviderProps> = ({ children }) => {
  const router = useRouter();
  const [dateRange, setDateRangeState] = useState<DateRange | null>(() => {
    // Try to get initial date range from URL
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const fromDate = params.get('dateFrom');
      const toDate = params.get('dateTo');
      const noDateFilter = params.get('noDateFilter');
      
      if (noDateFilter === 'true') {
        return null;
      }
      
      if (fromDate && toDate) {
        return { from: fromDate, to: toDate };
      }
    }
    return getDefaultDateRange();
  });

  // Update URL when date range changes
  const setDateRange = (newRange: DateRange | null) => {
    setDateRangeState(newRange);
    
    // Update URL params
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      
      if (newRange === null) {
        url.searchParams.set('noDateFilter', 'true');
        url.searchParams.delete('dateFrom');
        url.searchParams.delete('dateTo');
      } else {
        url.searchParams.delete('noDateFilter');
        url.searchParams.set('dateFrom', newRange.from);
        url.searchParams.set('dateTo', newRange.to);
      }
      
      // Use router.replace to update URL without adding to history
      router.replace(url, undefined, { shallow: true });
    }
  };

  const resetDateRange = () => {
    const defaultRange = getDefaultDateRange();
    setDateRange(defaultRange);
  };

  const isDefaultRange = () => {
    if (!dateRange) return false;
    const defaultRange = getDefaultDateRange();
    return dateRange.from === defaultRange.from && dateRange.to === defaultRange.to;
  };

  const ensureIncludesNewClients = () => {
    if (!dateRange) return; // No date filter active
    
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 2); // Add 2 days buffer
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    // Ensure the date range starts from 2020 and goes to tomorrow
    const defaultRange = getDefaultDateRange();
    
    // If current range is more restrictive than default, reset to default
    if (dateRange.from > defaultRange.from || dateRange.to < defaultRange.to) {
      console.log('Expanding date range to ensure all clients are visible');
      setDateRange(defaultRange);
    }
  };

  const disableDateFilter = () => {
    setDateRange(null);
  };

  // Listen for URL changes
  useEffect(() => {
    const handleRouteChange = () => {
      if (typeof window !== 'undefined') {
        const params = new URLSearchParams(window.location.search);
        const fromDate = params.get('dateFrom');
        const toDate = params.get('dateTo');
        const noDateFilter = params.get('noDateFilter');
        
        if (noDateFilter === 'true') {
          setDateRangeState(null);
        } else if (fromDate && toDate) {
          setDateRangeState({ from: fromDate, to: toDate });
        }
      }
    };

    router.events.on('routeChangeComplete', handleRouteChange);
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router]);

  return (
    <DateFilterContext.Provider 
      value={{
        dateRange,
        setDateRange,
        resetDateRange,
        isDefaultRange: isDefaultRange(),
        ensureIncludesNewClients,
        disableDateFilter,
        isDateFilterDisabled: dateRange === null
      }}
    >
      {children}
    </DateFilterContext.Provider>
  );
}; 