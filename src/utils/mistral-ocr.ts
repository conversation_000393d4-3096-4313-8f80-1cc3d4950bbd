import {
  MistralOcrRequest,
  MistralOcrResponse,
  MistralOcrError,
  MistralOcrProcessResult,
  MistralOcrConfig
} from '../types/mistral-ocr';
import { uploadTempFile } from './file-storage';
import { ParsedNameData, TempFileInfo } from '../types/ocr-common';
import { processDocumentWithClaude, isClaudeOcrAvailable } from './claude-vision-ocr';
import { processDocumentWithOpenRouter, isOpenRouterOcrAvailable } from './openrouter-vision-ocr';
import { COUNTRIES } from '../constants/countries';

// Default configuration
const DEFAULT_CONFIG: Partial<MistralOcrConfig> = {
  baseUrl: 'https://api.mistral.ai/v1/ocr',
  timeout: 60000, // 60 seconds
  includeImages: true,
};

/**
 * Get Mistral OCR configuration from environment
 */
const getMistralConfig = (): MistralOcrConfig => {
  const apiKey = process.env.MISTRAL_API_KEY;
  
  if (!apiKey) {
    throw new Error('MISTRAL_API_KEY environment variable is required');
  }

  return {
    apiKey,
    ...DEFAULT_CONFIG
  } as MistralOcrConfig;
};

/**
 * Make a request to Mistral OCR API
 */
const callMistralOcrApi = async (
  request: MistralOcrRequest,
  config: MistralOcrConfig
): Promise<MistralOcrResponse> => {
  try {
    console.log('Making request to Mistral OCR API...');
    console.log('Request:', JSON.stringify(request, null, 2));

    const response = await fetch(config.baseUrl!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(config.timeout!)
    });

    const responseData = await response.json();

    if (!response.ok) {
      const errorData = responseData as MistralOcrError;
      console.error('Mistral OCR API error:', errorData);
      throw new Error(`Mistral OCR API error: ${errorData.error.message}`);
    }

    // console.log('Mistral OCR API response received successfully');
    // console.log('Response structure:', JSON.stringify(responseData, null, 2));
    
    // Check if response has expected structure
    if (!responseData.pages || !Array.isArray(responseData.pages)) {
      console.error('Unexpected response structure - missing pages:', responseData);
      throw new Error('Mistral OCR API returned unexpected response structure');
    }
    
    return responseData as MistralOcrResponse;

  } catch (error) {
    console.error('Error calling Mistral OCR API:', error);
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('Mistral OCR API request timed out');
      }
      throw error;
    }
    throw new Error('Unknown error calling Mistral OCR API');
  }
};

/**
 * Extract text from response
 */
const extractTextFromResponse = (response: MistralOcrResponse): string => {
  try {
    console.log('Extracting text from response...');
    console.log('Response has pages:', !!response.pages);
    console.log('Pages count:', response.pages?.length);
    
    if (!response.pages || response.pages.length === 0) {
      console.error('Full response for debugging:', JSON.stringify(response, null, 2));
      throw new Error('No pages in Mistral OCR response');
    }

    // Combine text from all pages
    const allText = response.pages
      .map((page) => {
        console.log(`Page ${page.index} markdown length:`, page.markdown?.length || 0);
        return page.markdown || '';
      })
      .join('\n\n');

    if (!allText.trim()) {
      console.error('All pages are empty or have no markdown content');
      throw new Error('No text content found in Mistral OCR response');
    }

    console.log('Successfully extracted text from', response.pages.length, 'pages, total length:', allText.length);
    return allText.trim();
  } catch (error) {
    console.error('Error extracting text from Mistral OCR response:', error);
    throw error;
  }
};

/**
 * Process an image file with Mistral OCR
 */
export const processImageWithMistral = async (
  filePath: string,
  originalFileName?: string
): Promise<MistralOcrProcessResult> => {
  let tempFile: TempFileInfo | null = null;

  try {
    console.log('=== PROCESSING IMAGE WITH MISTRAL OCR ===');
    console.log('File path:', filePath);

    // Get configuration
    const config = getMistralConfig();

    // Upload file to temporary storage
    tempFile = await uploadTempFile(filePath, originalFileName);

    // Create request
    const request: MistralOcrRequest = {
      model: 'mistral-ocr-latest',
      document: {
        type: 'image_url',
        image_url: tempFile.url
      },
      include_image_base64: config.includeImages
    };

    // Call Mistral OCR API
    const response = await callMistralOcrApi(request, config);

    // Extract text
    const text = extractTextFromResponse(response);

    console.log('Mistral OCR processing completed successfully');
    console.log('Extracted text length:', text.length);

    // Collect images from all pages
    const allImages = response.pages.flatMap(page => page.images || []);
    console.log(`Found ${allImages.length} images total`);
    allImages.forEach((img, index) => {
      console.log(`Image ${index + 1}: ${img.id}, has base64: ${!!img.image_base64}`);
    });

    return {
      success: true,
      text,
      images: allImages
    };

  } catch (error) {
    console.error('Error processing image with Mistral OCR:', error);
    return {
      success: false,
      text: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    // Cleanup temp file
    if (tempFile) {
      try {
        await tempFile.cleanup();
      } catch (cleanupError) {
        console.warn('Error cleaning up temp file:', cleanupError);
      }
    }
  }
};

/**
 * Process a PDF file with Mistral OCR
 */
export const processPdfWithMistral = async (
  filePath: string,
  originalFileName?: string
): Promise<MistralOcrProcessResult> => {
  let tempFile: TempFileInfo | null = null;

  try {
    console.log('=== PROCESSING PDF WITH MISTRAL OCR ===');
    console.log('File path:', filePath);

    // Get configuration
    const config = getMistralConfig();

    // Upload file to temporary storage
    tempFile = await uploadTempFile(filePath, originalFileName);

    // Create request
    const request: MistralOcrRequest = {
      model: 'mistral-ocr-latest',
      document: {
        type: 'document_url',
        document_url: tempFile.url
      },
      include_image_base64: config.includeImages
    };

    // Call Mistral OCR API
    const response = await callMistralOcrApi(request, config);

    // Extract text
    const text = extractTextFromResponse(response);

    console.log('Mistral OCR PDF processing completed successfully');
    console.log('Extracted text length:', text.length);

    // Collect images from all pages
    const allImages = response.pages.flatMap(page => page.images || []);
    console.log(`Found ${allImages.length} images total`);
    allImages.forEach((img, index) => {
      console.log(`Image ${index + 1}: ${img.id}, has base64: ${!!img.image_base64}`);
    });

    return {
      success: true,
      text,
      images: allImages
    };

  } catch (error) {
    console.error('Error processing PDF with Mistral OCR:', error);
    return {
      success: false,
      text: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    // Cleanup temp file
    if (tempFile) {
      try {
        await tempFile.cleanup();
      } catch (cleanupError) {
        console.warn('Error cleaning up temp file:', cleanupError);
      }
    }
  }
};

/**
 * Universal document processing with Mistral OCR
 */
export const processDocumentWithMistral = async (
  filePath: string,
  fileType: 'image' | 'pdf',
  originalFileName?: string
): Promise<MistralOcrProcessResult> => {
  try {
    console.log('=== PROCESSING DOCUMENT WITH MISTRAL OCR ===');
    console.log('File path:', filePath);
    console.log('File type:', fileType);

    if (fileType === 'pdf') {
      return await processPdfWithMistral(filePath, originalFileName);
    } else {
      return await processImageWithMistral(filePath, originalFileName);
    }

  } catch (error) {
    console.error('Error processing document with Mistral OCR:', error);
    return {
      success: false,
      text: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Parse name, surname and dates from Mistral OCR response
 */

/**
 * Convert date from DD.MM.YYYY to YYYY-MM-DD format
 */
const convertDateFormat = (dateStr: string): string => {
  if (!dateStr) return '';
  
  // Check if it's already in YYYY-MM-DD format
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr;
  }
  
  // Convert DD.MM.YYYY to YYYY-MM-DD
  if (/^\d{2}\.\d{2}\.\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('.');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Handle DD/MM/YYYY format
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('/');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Handle DD-MM-YYYY format
  if (/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('-');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  console.warn('Unknown date format:', dateStr);
  return dateStr; // Return as-is if we can't parse it
};

/**
 * Transliterate Cyrillic names to Latin according to international standards
 */
const transliterateName = (name: string): string => {
  if (!name) return '';
  
  // If already in Latin, return as is
  if (/^[A-Za-z\s\-']+$/.test(name)) {
    return name.toUpperCase();
  }
  
  // Transliteration map for Russian and Kazakh
  const transliterationMap: { [key: string]: string } = {
    // Russian alphabet
    'А': 'A', 'а': 'A',
    'Б': 'B', 'б': 'B',
    'В': 'V', 'в': 'V',
    'Г': 'G', 'г': 'G',
    'Д': 'D', 'д': 'D',
    'Е': 'E', 'е': 'E',
    'Ё': 'YO', 'ё': 'YO',
    'Ж': 'ZH', 'ж': 'ZH',
    'З': 'Z', 'з': 'Z',
    'И': 'I', 'и': 'I',
    'Й': 'Y', 'й': 'Y',
    'К': 'K', 'к': 'K',
    'Л': 'L', 'л': 'L',
    'М': 'M', 'м': 'M',
    'Н': 'N', 'н': 'N',
    'О': 'O', 'о': 'O',
    'П': 'P', 'п': 'P',
    'Р': 'R', 'р': 'R',
    'С': 'S', 'с': 'S',
    'Т': 'T', 'т': 'T',
    'У': 'U', 'у': 'U',
    'Ф': 'F', 'ф': 'F',
    'Х': 'KH', 'х': 'KH',
    'Ц': 'TS', 'ц': 'TS',
    'Ч': 'CH', 'ч': 'CH',
    'Ш': 'SH', 'ш': 'SH',
    'Щ': 'SHCH', 'щ': 'SHCH',
    'Ъ': 'IE', 'ъ': 'IE',
    'Ы': 'Y', 'ы': 'Y',
    'Ь': '', 'ь': '',
    'Э': 'E', 'э': 'E',
    'Ю': 'YU', 'ю': 'YU',
    'Я': 'YA', 'я': 'YA',
    
    // Kazakh specific letters
    'Ә': 'AE', 'ә': 'AE',
    'Қ': 'Q', 'қ': 'Q',
    'Ң': 'NG', 'ң': 'NG',
    'Ғ': 'GH', 'ғ': 'GH',
    'Ү': 'UE', 'ү': 'UE',
    'Ұ': 'U', 'ұ': 'U',
    'Һ': 'H', 'һ': 'H',
    'Ө': 'OE', 'ө': 'OE',
    'І': 'I', 'і': 'I',
    
    // Common punctuation and spaces
    ' ': ' ',
    '-': '-',
    "'": "'",
    '"': '"'
  };
  
  let result = '';
  for (let i = 0; i < name.length; i++) {
    const char = name[i];
    if (transliterationMap[char] !== undefined) {
      result += transliterationMap[char];
    } else if (/[A-Za-z]/.test(char)) {
      // Already Latin
      result += char.toUpperCase();
    } else {
      // Unknown character, keep as is
      result += char;
    }
  }
  
  // Clean up multiple spaces and trim
  result = result.replace(/\s+/g, ' ').trim();
  
  console.log(`Transliterated name: "${name}" → "${result}"`);
  return result;
};

/**
 * Map nationality/citizenship from Cyrillic to country list
 */
const mapNationalityToCitizenship = (nationality: string): string => {
  if (!nationality) return '';
  
  // Create mapping from common nationality terms to countries
  const nationalityMap: { [key: string]: string } = {
    'казахстанская': 'Казахстан',
    'казахстанец': 'Казахстан',
    'казахстанка': 'Казахстан',
    'казахская': 'Казахстан',
    'казах': 'Казахстан',
    'kaz': 'Казахстан',
    'казахстан': 'Казахстан',
    
    'российская': 'Россия',
    'русская': 'Россия',
    'русский': 'Россия',
    'российский': 'Россия',
    'россиянин': 'Россия',
    'россиянка': 'Россия',
    'rus': 'Россия',
    'россия': 'Россия',
    
    'американская': 'США',
    'американец': 'США',
    'американка': 'США',
    'usa': 'США',
    'united states': 'США',
    'сша': 'США',
    
    'немецкая': 'Германия',
    'немец': 'Германия',
    'немка': 'Германия',
    'german': 'Германия',
    'ger': 'Германия',
    'германия': 'Германия',
    
    'французская': 'Франция',
    'француз': 'Франция',
    'французженка': 'Франция',
    'french': 'Франция',
    'fra': 'Франция',
    'франция': 'Франция',
    
    'британская': 'Великобритания',
    'английская': 'Великобритания',
    'англичанин': 'Великобритания',
    'англичанка': 'Великобритания',
    'british': 'Великобритания',
    'uk': 'Великобритания',
    'gbr': 'Великобритания',
    'великобритания': 'Великобритания',
    
    'китайская': 'Китай',
    'китаец': 'Китай',
    'китаянка': 'Китай',
    'chinese': 'Китай',
    'chn': 'Китай',
    'китай': 'Китай',
    
    'узбекская': 'Узбекистан',
    'узбек': 'Узбекистан',
    'узбечка': 'Узбекистан',
    'uzb': 'Узбекистан',
    'узбекистан': 'Узбекистан',
    
    'кыргызская': 'Кыргызстан',
    'кыргыз': 'Кыргызстан',
    'кыргызка': 'Кыргызстан',
    'kgz': 'Кыргызстан',
    'кыргызстан': 'Кыргызстан',
    
    'таджикская': 'Таджикистан',
    'таджик': 'Таджикистан',
    'таджичка': 'Таджикистан',
    'tjk': 'Таджикистан',
    'таджикистан': 'Таджикистан',
    
    'турецкая': 'Турция',
    'турок': 'Турция',
    'турчанка': 'Турция',
    'turkish': 'Турция',
    'tur': 'Турция',
    'турция': 'Турция'
  };
  
  const normalized = nationality.toLowerCase().trim();
  
  // Try direct mapping first
  if (nationalityMap[normalized]) {
    return nationalityMap[normalized];
  }
  
  // Try partial matching
  for (const [key, value] of Object.entries(nationalityMap)) {
    if (normalized.includes(key) || key.includes(normalized)) {
      return value;
    }
  }
  
  // If no mapping found, check if it's already in the countries list
  const foundCountry = COUNTRIES.find(country => 
    country.toLowerCase() === normalized || 
    normalized.includes(country.toLowerCase())
  );
  
  if (foundCountry) {
    return foundCountry;
  }
  
  return nationality; // Return as-is if we can't map it
};

/**
 * Apply data conversions to parsed data
 */
const applyDataConversions = (data: Partial<ParsedNameData>): Partial<ParsedNameData> => {
  const converted = { ...data };
  
  // Transliterate names to Latin
  if (converted.name) {
    converted.name = transliterateName(converted.name);
  }
  if (converted.surname) {
    converted.surname = transliterateName(converted.surname);
  }
  
  // Convert dates
  if (converted.dateOfBirth) {
    converted.dateOfBirth = convertDateFormat(converted.dateOfBirth);
  }
  if (converted.passportIssueDate) {
    converted.passportIssueDate = convertDateFormat(converted.passportIssueDate);
  }
  if (converted.passportExpiryDate) {
    converted.passportExpiryDate = convertDateFormat(converted.passportExpiryDate);
  }
  
  // Convert nationality to citizenship
  if (converted.nationality) {
    converted.citizenship = mapNationalityToCitizenship(converted.nationality);
  }
  
  return converted;
};

/**
 * Log extracted data in a structured format
 */
const logExtractedData = (data: Partial<ParsedNameData>, source: string, stage: string) => {
  console.log(`\n📊 === ${stage.toUpperCase()} EXTRACTION RESULTS (${source}) ===`);
  
  const fields = [
    { key: 'name', label: '👤 Имя', value: data.name },
    { key: 'surname', label: '👤 Фамилия', value: data.surname },
    { key: 'dateOfBirth', label: '🎂 Дата рождения', value: data.dateOfBirth },
    { key: 'passportIssueDate', label: '📋 Дата выдачи', value: data.passportIssueDate },
    { key: 'passportExpiryDate', label: '⏰ Действует до', value: data.passportExpiryDate },
    { key: 'passportNumber', label: '📄 Номер паспорта', value: data.passportNumber },
    { key: 'nationality', label: '🌍 Национальность', value: data.nationality },
    { key: 'citizenship', label: '🏛️ Гражданство', value: data.citizenship }
  ];
  
  let foundCount = 0;
  fields.forEach(field => {
    const status = field.value ? '✅' : '❌';
    const value = field.value || 'не найдено';
    console.log(`${status} ${field.label}: ${value}`);
    if (field.value) foundCount++;
  });
  
  console.log(`\n📈 Извлечено полей: ${foundCount}/${fields.length} (${Math.round(foundCount/fields.length*100)}%)`);
  console.log('=' .repeat(60));
};

/**
 * Log data combination from multiple sources
 */
const logDataCombination = (mrzData: Partial<ParsedNameData>, textData: Partial<ParsedNameData>, finalData: Partial<ParsedNameData>) => {
  console.log('\n🔀 === КОМБИНИРОВАНИЕ ДАННЫХ ИЗ ИСТОЧНИКОВ ===');
  
  const fields = ['name', 'surname', 'dateOfBirth', 'passportIssueDate', 'passportExpiryDate', 'passportNumber', 'nationality', 'citizenship'];
  
  fields.forEach(field => {
    const mrzValue = (mrzData as any)[field];
    const textValue = (textData as any)[field];
    const finalValue = (finalData as any)[field];
    
    if (finalValue) {
      const source = mrzValue ? 'MRZ' : (textValue ? 'TEXT' : 'NONE');
      console.log(`✅ ${field}: "${finalValue}" (источник: ${source})`);
    }
  });
  
  console.log('=' .repeat(60));
};

/**
 * Extract name, surname and dates from MRZ (Machine Readable Zone) line
 */
const extractFromMRZ = (text: string): Partial<ParsedNameData> => {
  const result: Partial<ParsedNameData> = {};
  
  // MRZ pattern: P<SURNAME<<FIRSTNAME<<<<<<<<<<<<<<<<
  const mrzMatch = text.match(/P<([A-Z]+)<<([A-Z]+)<+/);
  
  if (mrzMatch) {
    result.surname = mrzMatch[1].replace(/<+/g, '').trim();
    result.name = mrzMatch[2].replace(/<+/g, '').trim();
    
    console.log('Extracted names from MRZ:', { surname: result.surname, name: result.name });
  }
  
  // Extract data from the second line of MRZ
  // Pattern: PASSPORTNUMBER<COUNTRY<BIRTHDATE<SEX<EXPIRYDATE<PERSONALNUM<CHECK
  const mrzDataMatch = text.match(/([A-Z0-9]+)([A-Z]{3})(\d{6})[A-Z]?([MF])(\d{6})(\d+)<+(\d+)/);
  
  if (mrzDataMatch) {
    const [, passportNum, country, birthDate, gender, expiryDate, personalNum] = mrzDataMatch;
    
    result.passportNumber = passportNum;
    
    // Convert YYMMDD to readable format
    const formatMrzDate = (yymmdd: string): string => {
      const yy = parseInt(yymmdd.substring(0, 2));
      const mm = yymmdd.substring(2, 4);
      const dd = yymmdd.substring(4, 6);
      
      // Assume years 00-30 are 2000-2030, 31-99 are 1931-1999
      const fullYear = yy <= 30 ? 2000 + yy : 1900 + yy;
      
      return `${dd}.${mm}.${fullYear}`;
    };
    
    result.dateOfBirth = formatMrzDate(birthDate);
    result.passportExpiryDate = formatMrzDate(expiryDate);
    
    console.log('Extracted MRZ data:', {
      passportNumber: result.passportNumber,
      dateOfBirth: result.dateOfBirth,
      passportExpiryDate: result.passportExpiryDate,
      country,
      personalNum
    });
  } else {
    // Try alternative MRZ patterns for non-standard formats
    console.log('Standard MRZ pattern not found, trying alternative patterns...');
    
    // Look for passport number pattern
    const passportMatch = text.match(/[A-Z](\d{8,9})/);
    if (passportMatch) {
      result.passportNumber = passportMatch[0];
      console.log('Found passport number:', result.passportNumber);
    }
    
    // Look for date patterns in YYMMDD format
    const dateMatches = text.match(/\d{6}/g);
    if (dateMatches && dateMatches.length >= 2) {
      // Typically first date is birth date, second is expiry
      try {
        const formatMrzDate = (yymmdd: string): string => {
          const yy = parseInt(yymmdd.substring(0, 2));
          const mm = yymmdd.substring(2, 4);
          const dd = yymmdd.substring(4, 6);
          
          const fullYear = yy <= 30 ? 2000 + yy : 1900 + yy;
          return `${dd}.${mm}.${fullYear}`;
        };
        
        result.dateOfBirth = formatMrzDate(dateMatches[0]);
        if (dateMatches[1]) {
          result.passportExpiryDate = formatMrzDate(dateMatches[1]);
        }
        
        console.log('Extracted dates from alternative pattern:', {
          dateOfBirth: result.dateOfBirth,
          passportExpiryDate: result.passportExpiryDate
        });
      } catch (error) {
        console.warn('Error parsing MRZ dates:', error);
      }
    }
  }
  
  // Log extracted MRZ data
  logExtractedData(result, 'Mistral OCR', 'MRZ');
  
  // Apply data conversions
  const convertedResult = applyDataConversions(result);
  
  if (convertedResult !== result) {
    console.log('Applied data conversions to MRZ data');
    logExtractedData(convertedResult, 'Mistral OCR', 'MRZ (CONVERTED)');
  }
  
  return convertedResult;
};

/**
 * Extract name, surname, dates and other data from regular text patterns
 */
const extractFromText = (text: string): Partial<ParsedNameData> => {
  const result: Partial<ParsedNameData> = {};
  
  // Normalize text for better matching
  const normalizedText = text
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s.,:<>()\/\-]/g, ' ')
    .trim();
  
  // Try to extract name patterns
  const namePatterns = [
    /(?:Name|Given\s*Names?|First\s*Name|Имя|Аты)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:имя|имена)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of namePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      result.name = match[1].trim();
      console.log('Found name from text:', result.name);
      break;
    }
  }
  
  // Try to extract surname patterns
  const surnamePatterns = [
    /(?:Surname|Family\s*Name|Last\s*Name|Фамилия|Тегі)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:фамилия)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of surnamePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      result.surname = match[1].trim();
      console.log('Found surname from text:', result.surname);
      break;
    }
  }
  
  // Extract passport number
  const passportPatterns = [
    /(?:Passport\s*(?:No|Number|№))[:\s]*([A-Z0-9]{7,12})/i,
    /(?:Паспорт\s*(?:№|номер))[:\s]*([A-Z0-9]{7,12})/i,
    /№\s*([A-Z0-9]{7,12})/i,
    /[A-Z]\d{8,9}/g // Simple pattern for passport numbers like N162240532
  ];
  
  for (const pattern of passportPatterns) {
    const match = normalizedText.match(pattern);
    if (match && (match[1] || match[0])) {
      result.passportNumber = (match[1] || match[0]).trim();
      console.log('Found passport number from text:', result.passportNumber);
      break;
    }
  }
  
  // Extract nationality
  const nationalityPatterns = [
    /(?:Nationality|Национальность|Ұлты)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:НАЦИОНАЛЬНОСТЬ)[:\s]*([А-ЯЁ]+)/i,
    /(?:ҰЛТЫ)[:\s]*([А-ЯЁ]+)/i
  ];
  
  for (const pattern of nationalityPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      result.nationality = match[1].trim();
      console.log('Found nationality from text:', result.nationality);
      break;
    }
  }
  
  // Extract dates - various formats
  const datePatterns = [
    /\b(\d{2}[./-]\d{2}[./-]\d{4})\b/g,  // DD.MM.YYYY, DD/MM/YYYY, DD-MM-YYYY
    /\b(\d{4}[./-]\d{2}[./-]\d{2})\b/g,  // YYYY.MM.DD, YYYY/MM/DD, YYYY-MM-DD
    /\b(\d{2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4})\b/gi,  // DD MMM YYYY
    /\b(\d{1,2}\s+(?:января|февраля|марта|апреля|мая|июня|июля|августа|сентября|октября|ноября|декабря)\s+\d{4})\b/gi // Russian months
  ];
  
  let allDates: string[] = [];
  for (const pattern of datePatterns) {
    const matches = [...normalizedText.matchAll(pattern)].map(match => match[1]);
    allDates = [...allDates, ...matches];
  }
  
  console.log('Found dates in text:', allDates);
  
  // Try to assign dates based on context
  for (const date of allDates) {
    const dateIndex = normalizedText.indexOf(date);
    const context = normalizedText.substring(
      Math.max(0, dateIndex - 50),
      Math.min(normalizedText.length, dateIndex + date.length + 50)
    ).toLowerCase();
    
    if (context.includes('birth') || context.includes('рожд') || context.includes('born') || 
        context.includes('дата рождения') || context.includes('туған')) {
      result.dateOfBirth = date;
      console.log('Assigned birth date from context:', date);
    } else if (context.includes('issue') || context.includes('выда') || context.includes('берілген') ||
               context.includes('дата выдачи')) {
      result.passportIssueDate = date;
      console.log('Assigned issue date from context:', date);
    } else if (context.includes('expir') || context.includes('valid') || context.includes('действ') ||
               context.includes('годен до') || context.includes('дейін жарамды')) {
      result.passportExpiryDate = date;
      console.log('Assigned expiry date from context:', date);
    }
  }
  
  // If we have unassigned dates, make educated guesses
  const unassignedDates = allDates.filter(date => 
    date !== result.dateOfBirth && 
    date !== result.passportIssueDate && 
    date !== result.passportExpiryDate
  );
  
  if (unassignedDates.length > 0) {
    // Sort dates chronologically
    const sortedDates = unassignedDates.sort((a, b) => {
      const parseDate = (dateStr: string): Date => {
        // Handle DD.MM.YYYY format
        if (/^\d{2}[./-]\d{2}[./-]\d{4}$/.test(dateStr)) {
          const [day, month, year] = dateStr.split(/[./-]/);
          return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        }
        return new Date(dateStr);
      };
      
      return parseDate(a).getTime() - parseDate(b).getTime();
    });
    
    // Assign by chronological order if not already assigned
    if (!result.dateOfBirth && sortedDates[0]) {
      result.dateOfBirth = sortedDates[0];
      console.log('Assigned birth date by chronology:', sortedDates[0]);
    }
    if (!result.passportIssueDate && sortedDates[1]) {
      result.passportIssueDate = sortedDates[1];
      console.log('Assigned issue date by chronology:', sortedDates[1]);
    }
    if (!result.passportExpiryDate && sortedDates[2]) {
      result.passportExpiryDate = sortedDates[2];
      console.log('Assigned expiry date by chronology:', sortedDates[2]);
    }
  }
  
  // Log extracted text data
  logExtractedData(result, 'Mistral OCR', 'TEXT');
  
  // Apply data conversions
  const convertedResult = applyDataConversions(result);
  
  if (convertedResult !== result) {
    console.log('Applied data conversions to text data');
    logExtractedData(convertedResult, 'Mistral OCR', 'TEXT (CONVERTED)');
  }
  
  return convertedResult;
};

/**
 * Process images with Tesseract as fallback
 */
const processImagesWithTesseract = async (images: Array<{ id: string; image_base64?: string }>): Promise<string> => {
  console.log('Processing images with Tesseract as fallback...');
  
  const { extractTextFromImage } = await import('./ocr');
  
  for (const image of images) {
    if (!image.image_base64) {
      console.log(`Skipping image ${image.id} - no base64 data`);
      continue;
    }
    
    try {
      console.log(`Processing image ${image.id} with Tesseract...`);
      
      // Convert base64 to buffer for Tesseract
      const base64Data = image.image_base64.replace(/^data:image\/[a-z]+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Create a temporary file-like object that Tesseract can process
      const imageBlob = new Blob([buffer]);
      const imageFile = new File([imageBlob], image.id, { type: 'image/jpeg' });
      
      const text = await extractTextFromImage(imageFile);
      
      if (text && text.trim().length > 10) {
        console.log(`Successfully extracted text from ${image.id}, length:`, text.length);
        return text;
      }
    } catch (error) {
      console.error(`Error processing image ${image.id} with Tesseract:`, error);
    }
  }
  
  throw new Error('Failed to extract text from any images using Tesseract');
};

/**
 * Parse name and surname from Mistral OCR result with Tesseract fallback
 */
export const parseNameFromMistralOcr = async (
  result: MistralOcrProcessResult
): Promise<ParsedNameData> => {
  console.log('\n🔍 === НАЧАЛО АНАЛИЗА ДАННЫХ ПАСПОРТА ===');
  console.log('📱 Источник: Mistral OCR');
  console.log('📏 Длина текста:', result.text?.length || 0);
  console.log('🖼️ Количество изображений:', result.images?.length || 0);
  
  if (!result.success || !result.text) {
    throw new Error('Invalid Mistral OCR result');
  }
  
  const parsedData: ParsedNameData = {
    rawText: result.text,
    source: 'mistral'
  };
  
  console.log('\n📝 ИСХОДНЫЙ ТЕКСТ MISTRAL OCR:');
  console.log('-'.repeat(50));
  console.log(result.text.substring(0, 300) + (result.text.length > 300 ? '...' : ''));
  console.log('-'.repeat(50));
  
  // Step 1: Try to extract from MRZ line
  console.log('\n🔎 ЭТАП 1: Извлечение данных из MRZ (Machine Readable Zone)');
  const mrzData = extractFromMRZ(result.text);
  
  // Step 2: Try to extract from regular text patterns
  console.log('\n🔎 ЭТАП 2: Извлечение данных из обычного текста');
  const textData = extractFromText(result.text);
  
  // Combine data from both sources (MRZ has priority for dates, text for names if better)
  parsedData.name = mrzData.name || textData.name;
  parsedData.surname = mrzData.surname || textData.surname;
  parsedData.dateOfBirth = mrzData.dateOfBirth || textData.dateOfBirth;
  parsedData.passportIssueDate = mrzData.passportIssueDate || textData.passportIssueDate;
  parsedData.passportExpiryDate = mrzData.passportExpiryDate || textData.passportExpiryDate;
  parsedData.passportNumber = mrzData.passportNumber || textData.passportNumber;
  parsedData.nationality = mrzData.nationality || textData.nationality;
  parsedData.citizenship = mrzData.citizenship || textData.citizenship; // Use already converted citizenship
  
  // Log data combination
  logDataCombination(mrzData, textData, parsedData);
  
  // Check if we have essential data (name and surname)
  if (parsedData.name && parsedData.surname) {
    console.log('\n✅ УСПЕШНО: Найдены все основные данные');
    logExtractedData(parsedData, 'FINAL', 'ИТОГОВЫЙ РЕЗУЛЬТАТ');
    return parsedData;
  }
  
  // Step 3: Partial success - we found some data but missing name or surname
  if (parsedData.name || parsedData.surname || parsedData.passportNumber) {
    console.log('\n⚠️ ЧАСТИЧНЫЙ УСПЕХ: Найдена часть данных, не хватает имени или фамилии');
    logExtractedData(parsedData, 'PARTIAL', 'ЧАСТИЧНЫЙ РЕЗУЛЬТАТ');
    
    // If we have partial data but still need more, try Tesseract
    if ((!parsedData.name || !parsedData.surname) && result.images && result.images.length > 0) {
      console.log('\n🔄 ЭТАП 3: Tesseract fallback для недостающих данных...');
      try {
        const tesseractText = await processImagesWithTesseract(result.images);
        parsedData.rawText += '\n\n--- TESSERACT FALLBACK ---\n' + tesseractText;
        parsedData.source = 'tesseract';
        
         // Try to extract missing parts from Tesseract text
         console.log('\n🔎 Поиск недостающих данных в Tesseract тексте...');
         const tesseractMrz = extractFromMRZ(tesseractText);
         const tesseractTextData = extractFromText(tesseractText);
         
         console.log('\n📋 TESSERACT MRZ (дополнительные данные):');
         console.log(tesseractMrz);
         console.log('\n📋 TESSERACT TEXT (дополнительные данные):');
         console.log(tesseractTextData);
         
         // Fill in missing data
         if (!parsedData.name) {
           parsedData.name = tesseractMrz.name || tesseractTextData.name;
         }
         if (!parsedData.surname) {
           parsedData.surname = tesseractMrz.surname || tesseractTextData.surname;
         }
         if (!parsedData.dateOfBirth) {
           parsedData.dateOfBirth = tesseractMrz.dateOfBirth || tesseractTextData.dateOfBirth;
         }
         if (!parsedData.passportIssueDate) {
           parsedData.passportIssueDate = tesseractMrz.passportIssueDate || tesseractTextData.passportIssueDate;
         }
         if (!parsedData.passportExpiryDate) {
           parsedData.passportExpiryDate = tesseractMrz.passportExpiryDate || tesseractTextData.passportExpiryDate;
         }
         if (!parsedData.passportNumber) {
           parsedData.passportNumber = tesseractMrz.passportNumber || tesseractTextData.passportNumber;
         }
         if (!parsedData.nationality) {
           parsedData.nationality = tesseractMrz.nationality || tesseractTextData.nationality;
         }
         if (!parsedData.citizenship) {
           parsedData.citizenship = tesseractMrz.citizenship || tesseractTextData.citizenship;
         }
         
         console.log('\n✅ TESSERACT FALLBACK ЗАВЕРШЕН');
         logExtractedData(parsedData, 'TESSERACT', 'РЕЗУЛЬТАТ ПОСЛЕ TESSERACT');
      } catch (tesseractError) {
        console.warn('❌ Tesseract fallback failed:', tesseractError);
      }
    }
    
    return parsedData;
  }
  
  // Step 4: No name/surname found in Mistral text - try Tesseract fallback
  console.log('\n❌ НЕУДАЧА: Не найдены имя/фамилия в Mistral OCR');
  console.log('\n🔄 ЭТАП 4: Полный Tesseract fallback...');
  
  if (!result.images || result.images.length === 0) {
    throw new Error('No passport data found and no images available for Tesseract fallback');
  }
  
  try {
    const tesseractText = await processImagesWithTesseract(result.images);
    parsedData.rawText = tesseractText;
    parsedData.source = 'tesseract';
    
    console.log('📝 Tesseract извлек текст, длина:', tesseractText.length);
    console.log('Первые 200 символов:', tesseractText.substring(0, 200));
    
    // Try to extract from Tesseract text
    console.log('\n🔎 Извлечение данных из Tesseract текста...');
    const tesseractMrz = extractFromMRZ(tesseractText);
    const tesseractTextData = extractFromText(tesseractText);
    
    console.log('\n📋 TESSERACT MRZ результат:');
    console.log(tesseractMrz);
    console.log('\n📋 TESSERACT TEXT результат:');
    console.log(tesseractTextData);
    
    // Combine all data from Tesseract
    parsedData.name = tesseractMrz.name || tesseractTextData.name;
    parsedData.surname = tesseractMrz.surname || tesseractTextData.surname;
    parsedData.dateOfBirth = tesseractMrz.dateOfBirth || tesseractTextData.dateOfBirth;
    parsedData.passportIssueDate = tesseractMrz.passportIssueDate || tesseractTextData.passportIssueDate;
    parsedData.passportExpiryDate = tesseractMrz.passportExpiryDate || tesseractTextData.passportExpiryDate;
    parsedData.passportNumber = tesseractMrz.passportNumber || tesseractTextData.passportNumber;
    parsedData.nationality = tesseractMrz.nationality || tesseractTextData.nationality;
    parsedData.citizenship = tesseractMrz.citizenship || tesseractTextData.citizenship;
    
    if (parsedData.name && parsedData.surname) {
      console.log('\n✅ ПОЛНЫЙ УСПЕХ TESSERACT: Найдены все основные данные');
      logExtractedData(parsedData, 'TESSERACT', 'ФИНАЛЬНЫЙ РЕЗУЛЬТАТ TESSERACT');
    } else {
      console.log('\n⚠️ ЧАСТИЧНЫЙ УСПЕХ TESSERACT: Найдена часть данных');
      logExtractedData(parsedData, 'TESSERACT', 'ЧАСТИЧНЫЙ РЕЗУЛЬТАТ TESSERACT');
    }
    
    return parsedData;
  } catch (error) {
    console.error('Tesseract fallback failed:', error);
    throw new Error(`Failed to extract passport data from both Mistral OCR and Tesseract: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Convenience function to parse document and extract name data
 */
export const parseDocumentNameData = async (
  filePath: string,
  fileType: 'image' | 'pdf',
  originalFileName?: string,
  agentId?: string
): Promise<ParsedNameData> => {
  console.log('\n🚀 === НАЧАЛО ОБРАБОТКИ ДОКУМЕНТА ===');
  console.log('📂 Путь к файлу:', filePath);
  console.log('📋 Тип файла:', fileType);
  console.log('📝 Имя файла:', originalFileName || 'не указано');
  console.log('🔍 OCR движки доступны:', {
    openrouter: isOpenRouterOcrAvailable(),
    claude: isClaudeOcrAvailable(),
    mistral: !!process.env.MISTRAL_API_KEY
  });
  
  try {
    // Priority 1: Try OpenRouter Vision OCR first
    if (isOpenRouterOcrAvailable() && agentId) {
      console.log('\n🎯 ПРИОРИТЕТ 1: Обработка с помощью OpenRouter Vision OCR...');
      try {
        const openrouterResult = await processDocumentWithOpenRouter(filePath, fileType, agentId);
        
        if (openrouterResult.success && openrouterResult.extractedData) {
          console.log('\n✅ OpenRouter Vision OCR успешно завершен');
          console.log('📏 Длина извлеченного текста:', openrouterResult.rawText.length);
          console.log('🎯 Токены использованы:', openrouterResult.tokensUsed);
          
          // OpenRouter data should already be converted, but let's make sure
          let nameData = openrouterResult.extractedData;
          
          // Double-check that dates are in correct format and citizenship is mapped
          if (nameData.dateOfBirth && !nameData.dateOfBirth.match(/^\d{4}-\d{2}-\d{2}$/)) {
            console.log('⚠️ OpenRouter date needs conversion:', nameData.dateOfBirth);
            nameData = {
              ...nameData,
              dateOfBirth: convertDateFormat(nameData.dateOfBirth)
            };
          }
          if (nameData.passportIssueDate && !nameData.passportIssueDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            console.log('⚠️ OpenRouter passport issue date needs conversion:', nameData.passportIssueDate);
            nameData = {
              ...nameData,
              passportIssueDate: convertDateFormat(nameData.passportIssueDate)
            };
          }
          if (nameData.passportExpiryDate && !nameData.passportExpiryDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            console.log('⚠️ OpenRouter passport expiry date needs conversion:', nameData.passportExpiryDate);
            nameData = {
              ...nameData,
              passportExpiryDate: convertDateFormat(nameData.passportExpiryDate)
            };
          }
          if (nameData.nationality && !nameData.citizenship) {
            console.log('⚠️ OpenRouter citizenship needs mapping:', nameData.nationality);
            nameData = {
              ...nameData,
              citizenship: mapNationalityToCitizenship(nameData.nationality)
            };
          }
          
          console.log('\n🎉 === ФИНАЛЬНЫЙ РЕЗУЛЬТАТ ОБРАБОТКИ ДОКУМЕНТА (OPENROUTER) ===');
          logExtractedData(nameData, 'OPENROUTER', 'ОКОНЧАТЕЛЬНЫЙ РЕЗУЛЬТАТ');
          
          // Success statistics
          const totalFields = 8;
          const extractedFields = [
            nameData.name, nameData.surname, nameData.dateOfBirth,
            nameData.passportIssueDate, nameData.passportExpiryDate,
            nameData.passportNumber, nameData.nationality, nameData.citizenship
          ].filter(Boolean).length;
          
          console.log(`\n📊 СТАТИСТИКА ИЗВЛЕЧЕНИЯ (OPENROUTER):`);
          console.log(`✅ Успешно извлечено: ${extractedFields}/${totalFields} полей (${Math.round(extractedFields/totalFields*100)}%)`);
          console.log(`🎯 Критические поля (имя+фамилия): ${nameData.name && nameData.surname ? '✅ НАЙДЕНЫ' : '❌ НЕ НАЙДЕНЫ'}`);
          console.log(`📱 Источник данных: 🤖 OpenRouter Claude Sonnet-4`);
          console.log('=' .repeat(70));
          
          return nameData;
        } else {
          console.log('\n⚠️ OpenRouter Vision OCR не смог обработать документ:', openrouterResult.error);
          console.log('🔄 Переходим к Claude OCR...');
        }
      } catch (openrouterError) {
        console.error('\n❌ Ошибка OpenRouter Vision OCR:', openrouterError);
        console.log('🔄 Переходим к Claude OCR...');
      }
    } else {
      if (!isOpenRouterOcrAvailable()) {
        console.log('\n⚠️ OpenRouter Vision OCR недоступен (проверьте NEXT_OR_KEY)');
      }
      if (!agentId) {
        console.log('\n⚠️ Agent ID не предоставлен для OpenRouter OCR');
      }
      console.log('🔄 Переходим к Claude OCR...');
    }

    // Priority 2: Try Claude Vision OCR
    if (isClaudeOcrAvailable() && agentId) {
      console.log('\n🎯 ПРИОРИТЕТ 2: Обработка с помощью Claude Vision OCR...');
      try {
        const claudeResult = await processDocumentWithClaude(filePath, fileType, agentId);
        
        if (claudeResult.success && claudeResult.extractedData) {
          console.log('\n✅ Claude Vision OCR успешно завершен');
          console.log('📏 Длина извлеченного текста:', claudeResult.rawText.length);
          console.log('🎯 Токены использованы:', claudeResult.tokensUsed);
          
          // Claude data should already be converted, but let's make sure
          let nameData = claudeResult.extractedData;
          
          // Double-check that dates are in correct format and citizenship is mapped
          if (nameData.dateOfBirth && !nameData.dateOfBirth.match(/^\d{4}-\d{2}-\d{2}$/)) {
            console.log('⚠️ Claude date needs conversion:', nameData.dateOfBirth);
            nameData = {
              ...nameData,
              dateOfBirth: convertDateFormat(nameData.dateOfBirth)
            };
          }
          if (nameData.passportIssueDate && !nameData.passportIssueDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            console.log('⚠️ Claude passport issue date needs conversion:', nameData.passportIssueDate);
            nameData = {
              ...nameData,
              passportIssueDate: convertDateFormat(nameData.passportIssueDate)
            };
          }
          if (nameData.passportExpiryDate && !nameData.passportExpiryDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            console.log('⚠️ Claude passport expiry date needs conversion:', nameData.passportExpiryDate);
            nameData = {
              ...nameData,
              passportExpiryDate: convertDateFormat(nameData.passportExpiryDate)
            };
          }
          if (nameData.nationality && !nameData.citizenship) {
            console.log('⚠️ Claude citizenship needs mapping:', nameData.nationality);
            nameData = {
              ...nameData,
              citizenship: mapNationalityToCitizenship(nameData.nationality)
            };
          }
          
          console.log('\n🎉 === ФИНАЛЬНЫЙ РЕЗУЛЬТАТ ОБРАБОТКИ ДОКУМЕНТА (CLAUDE) ===');
          logExtractedData(nameData, 'CLAUDE', 'ОКОНЧАТЕЛЬНЫЙ РЕЗУЛЬТАТ');
          
          // Success statistics
          const totalFields = 8;
          const extractedFields = [
            nameData.name, nameData.surname, nameData.dateOfBirth,
            nameData.passportIssueDate, nameData.passportExpiryDate,
            nameData.passportNumber, nameData.nationality, nameData.citizenship
          ].filter(Boolean).length;
          
          console.log(`\n📊 СТАТИСТИКА ИЗВЛЕЧЕНИЯ (CLAUDE):`);
          console.log(`✅ Успешно извлечено: ${extractedFields}/${totalFields} полей (${Math.round(extractedFields/totalFields*100)}%)`);
          console.log(`🎯 Критические поля (имя+фамилия): ${nameData.name && nameData.surname ? '✅ НАЙДЕНЫ' : '❌ НЕ НАЙДЕНЫ'}`);
          console.log(`📱 Источник данных: 🤖 Claude Vision OCR`);
          console.log('=' .repeat(70));
          
          return nameData;
        } else {
          console.log('\n⚠️ Claude Vision OCR не смог обработать документ:', claudeResult.error);
          console.log('🔄 Переходим к Mistral OCR...');
        }
      } catch (claudeError) {
        console.error('\n❌ Ошибка Claude Vision OCR:', claudeError);
        console.log('🔄 Переходим к Mistral OCR...');
      }
    } else {
      if (!isClaudeOcrAvailable()) {
        console.log('\n⚠️ Claude Vision OCR недоступен (проверьте NEXT_CLAUDE_KEY)');
      }
      if (!agentId) {
        console.log('\n⚠️ Agent ID не предоставлен для Claude OCR');
      }
      console.log('🔄 Переходим к Mistral OCR...');
    }

    // Priority 3: Fallback to Mistral OCR
    console.log('\n🎯 ПРИОРИТЕТ 3: Обработка с помощью Mistral OCR...');
    const ocrResult = await processDocumentWithMistral(filePath, fileType, originalFileName);
    
    if (!ocrResult.success) {
      throw new Error(`Mistral OCR failed: ${ocrResult.error}`);
    }
    
    console.log('\n✅ Mistral OCR успешно завершен');
    console.log('📏 Длина извлеченного текста:', ocrResult.text.length);
    
    // Parse name data with Tesseract fallback
    console.log('\n⚙️ Начинаем парсинг данных паспорта...');
    const nameData = await parseNameFromMistralOcr(ocrResult);
    
    console.log('\n🎉 === ФИНАЛЬНЫЙ РЕЗУЛЬТАТ ОБРАБОТКИ ДОКУМЕНТА ===');
    logExtractedData(nameData, nameData.source.toUpperCase(), 'ОКОНЧАТЕЛЬНЫЙ РЕЗУЛЬТАТ');
    
    // Success statistics
    const totalFields = 8;
    const extractedFields = [
      nameData.name, nameData.surname, nameData.dateOfBirth,
      nameData.passportIssueDate, nameData.passportExpiryDate,
      nameData.passportNumber, nameData.nationality, nameData.citizenship
    ].filter(Boolean).length;
    
    console.log(`\n📊 СТАТИСТИКА ИЗВЛЕЧЕНИЯ:`);
    console.log(`✅ Успешно извлечено: ${extractedFields}/${totalFields} полей (${Math.round(extractedFields/totalFields*100)}%)`);
    console.log(`🎯 Критические поля (имя+фамилия): ${nameData.name && nameData.surname ? '✅ НАЙДЕНЫ' : '❌ НЕ НАЙДЕНЫ'}`);
    console.log(`📱 Источник данных: ${nameData.source === 'mistral' ? '🤖 Mistral OCR' : '🔍 Tesseract OCR'}`);
    console.log('=' .repeat(70));
    
    return nameData;
  } catch (error) {
    console.error('\n❌ ОШИБКА при обработке документа:', error);
    throw error;
  }
};
