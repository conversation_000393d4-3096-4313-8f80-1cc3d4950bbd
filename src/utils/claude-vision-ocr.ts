import { v4 as uuidv4 } from 'uuid';
import { supabase } from './supabase';
import * as fs from 'fs';
import * as path from 'path';
import {
  ClaudeVisionConfig,
  ClaudeVisionRequest,
  ClaudeVisionResponse,
  ClaudeVisionProcessResult,
  ClaudeExtractedData,
  ClaudeOcrStatus
} from '../types/claude-vision';
import { uploadTempFile } from './file-storage';
import { ParsedNameData, TempFileInfo } from '../types/ocr-common';
import { COUNTRIES } from '../constants/countries';

// Claude Vision OCR Module

// Default configuration
const DEFAULT_CONFIG: Partial<ClaudeVisionConfig> = {
  baseUrl: 'https://api.anthropic.com/v1/messages',
  model: 'claude-3-5-sonnet-20241022',
  maxTokens: 2000,
  timeout: 60000,
};

// OCR Engine Configuration
const USE_CLAUDE_OCR = process.env.NEXT_CLAUDE_KEY && process.env.NEXT_CLAUDE_KEY.length > 0;

// Optimized prompts for passport OCR
const PASSPORT_OCR_PROMPT = `
Проанализируй это изображение паспорта или ID документа и извлеки следующие данные.
Верни результат строго в формате JSON без дополнительных комментариев:

{
  "name": "имя ТОЛЬКО в латинице (если найдено)",
  "surname": "фамилия ТОЛЬКО в латинице (если найдено)", 
  "dateOfBirth": "дата рождения в формате DD.MM.YYYY",
  "passportIssueDate": "дата выдачи в формате DD.MM.YYYY",
  "passportExpiryDate": "дата окончания в формате DD.MM.YYYY",
  "passportNumber": "номер паспорта",
  "nationality": "национальность/гражданство"
}

КРИТИЧЕСКИ ВАЖНО для имени и фамилии:
- Имя и фамилия ДОЛЖНЫ быть в латинице (A-Z, a-z)
- Если в документе есть латинские имена - используй их как есть
- Если имена только на кириллице - транслитерируй их согласно международным стандартам:
  * А→A, Б→B, В→V, Г→G, Д→D, Е→E, Ё→YO, Ж→ZH, З→Z, И→I, Й→Y, К→K, Л→L, М→M, Н→N, О→O, П→P, Р→R, С→S, Т→T, У→U, Ф→F, Х→KH, Ц→TS, Ч→CH, Ш→SH, Щ→SHCH, Ъ→IE, Ы→Y, Ь→, Э→E, Ю→YU, Я→YA
  * Қ→Q, Ә→AE, Ң→NG, Ғ→GH, Ү→UE, Ұ→U, Һ→H, Ө→OE, І→I
- Примеры: Иванов → IVANOV, Айжан → AIZHAN, Мұхтар → MUKHTAR

Общие правила:
- Если данные не найдены, используй null для этого поля
- Даты всегда в формате DD.MM.YYYY
- Ищи данные в MRZ (машиночитаемая зона) и в основном тексте
- Поддерживай русский, казахский, английский языки
- Будь точен и не придумывай данные
- Имя и фамилия ОБЯЗАТЕЛЬНО в латинице!
`;

/**
 * Get Claude Vision configuration from environment
 */
const getClaudeConfig = (): ClaudeVisionConfig => {
  const apiKey = process.env.NEXT_CLAUDE_KEY;
  
  if (!apiKey) {
    throw new Error('NEXT_CLAUDE_KEY environment variable is required');
  }

  // Validate API key format
  if (!apiKey.startsWith('sk-ant-api03-')) {
    console.warn('⚠️ Claude API key format warning: key should start with "sk-ant-api03-"');
  }

  return {
    apiKey,
    ...DEFAULT_CONFIG
  } as ClaudeVisionConfig;
};

/**
 * Make a request to Claude Vision API
 */
const callClaudeVisionApi = async (
  request: ClaudeVisionRequest,
  config: ClaudeVisionConfig
): Promise<ClaudeVisionResponse> => {
  try {
    console.log('Making request to Claude Vision API...');
    console.log('Request model:', request.model);
    console.log('Request max_tokens:', request.max_tokens);

    const postData = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(config.timeout)
    };

    const response = await fetch(config.baseUrl, postData);

    const responseData = await response.json();

    if (!response.ok) {
      console.error('Claude Vision API error:', responseData);
      console.error('HTTP Status:', response.status);
      console.error('HTTP Status Text:', response.statusText);
      
      // Provide more specific error messages
      if (response.status === 401) {
        throw new Error('Claude API authentication failed. Please check your NEXT_CLAUDE_KEY.');
      } else if (response.status === 400) {
        throw new Error(`Claude API bad request: ${responseData.error?.message || 'Invalid request'}`);
      } else if (response.status === 429) {
        throw new Error('Claude API rate limit exceeded. Please try again later.');
      } else {
        throw new Error(`Claude Vision API error: ${responseData.error?.message || 'Unknown error'}`);
      }
    }

    console.log('Claude Vision API response received successfully');
    console.log('Usage:', responseData.usage);
    
    return responseData as ClaudeVisionResponse;

  } catch (error) {
    console.error('Error calling Claude Vision API:', error);
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('Claude Vision API request timed out');
      }
      throw error;
    }
    throw new Error('Unknown error calling Claude Vision API');
  }
};

/**
 * Convert date from DD.MM.YYYY to YYYY-MM-DD format
 */
const convertDateFormat = (dateStr: string): string => {
  if (!dateStr) return '';
  
  // Check if it's already in YYYY-MM-DD format
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr;
  }
  
  // Convert DD.MM.YYYY to YYYY-MM-DD
  if (/^\d{2}\.\d{2}\.\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('.');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Handle DD/MM/YYYY format
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('/');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Handle DD-MM-YYYY format
  if (/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('-');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  console.warn('Unknown date format:', dateStr);
  return dateStr; // Return as-is if we can't parse it
};

/**
 * Transliterate Cyrillic names to Latin according to international standards
 */
const transliterateName = (name: string): string => {
  if (!name) return '';
  
  // If already in Latin, return as is
  if (/^[A-Za-z\s\-']+$/.test(name)) {
    return name.toUpperCase();
  }
  
  // Transliteration map for Russian and Kazakh
  const transliterationMap: { [key: string]: string } = {
    // Russian alphabet
    'А': 'A', 'а': 'A',
    'Б': 'B', 'б': 'B',
    'В': 'V', 'в': 'V',
    'Г': 'G', 'г': 'G',
    'Д': 'D', 'д': 'D',
    'Е': 'E', 'е': 'E',
    'Ё': 'YO', 'ё': 'YO',
    'Ж': 'ZH', 'ж': 'ZH',
    'З': 'Z', 'з': 'Z',
    'И': 'I', 'и': 'I',
    'Й': 'Y', 'й': 'Y',
    'К': 'K', 'к': 'K',
    'Л': 'L', 'л': 'L',
    'М': 'M', 'м': 'M',
    'Н': 'N', 'н': 'N',
    'О': 'O', 'о': 'O',
    'П': 'P', 'п': 'P',
    'Р': 'R', 'р': 'R',
    'С': 'S', 'с': 'S',
    'Т': 'T', 'т': 'T',
    'У': 'U', 'у': 'U',
    'Ф': 'F', 'ф': 'F',
    'Х': 'KH', 'х': 'KH',
    'Ц': 'TS', 'ц': 'TS',
    'Ч': 'CH', 'ч': 'CH',
    'Ш': 'SH', 'ш': 'SH',
    'Щ': 'SHCH', 'щ': 'SHCH',
    'Ъ': 'IE', 'ъ': 'IE',
    'Ы': 'Y', 'ы': 'Y',
    'Ь': '', 'ь': '',
    'Э': 'E', 'э': 'E',
    'Ю': 'YU', 'ю': 'YU',
    'Я': 'YA', 'я': 'YA',
    
    // Kazakh specific letters
    'Ә': 'AE', 'ә': 'AE',
    'Қ': 'Q', 'қ': 'Q',
    'Ң': 'NG', 'ң': 'NG',
    'Ғ': 'GH', 'ғ': 'GH',
    'Ү': 'UE', 'ү': 'UE',
    'Ұ': 'U', 'ұ': 'U',
    'Һ': 'H', 'һ': 'H',
    'Ө': 'OE', 'ө': 'OE',
    'І': 'I', 'і': 'I',
    
    // Common punctuation and spaces
    ' ': ' ',
    '-': '-',
    "'": "'",
    '"': '"'
  };
  
  let result = '';
  for (let i = 0; i < name.length; i++) {
    const char = name[i];
    if (transliterationMap[char] !== undefined) {
      result += transliterationMap[char];
    } else if (/[A-Za-z]/.test(char)) {
      // Already Latin
      result += char.toUpperCase();
    } else {
      // Unknown character, keep as is
      result += char;
    }
  }
  
  // Clean up multiple spaces and trim
  result = result.replace(/\s+/g, ' ').trim();
  
  console.log(`Transliterated name: "${name}" → "${result}"`);
  return result;
};

/**
 * Map nationality/citizenship from Cyrillic to country list
 */
const mapNationalityToCitizenship = (nationality: string): string => {
  if (!nationality) return '';
  
  // Create mapping from common nationality terms to countries
  const nationalityMap: { [key: string]: string } = {
    'казахстанская': 'Казахстан',
    'казахстанец': 'Казахстан',
    'казахстанка': 'Казахстан',
    'казахская': 'Казахстан',
    'казах': 'Казахстан',
    'kaz': 'Казахстан',
    'казахстан': 'Казахстан',
    
    'российская': 'Россия',
    'русская': 'Россия',
    'русский': 'Россия',
    'российский': 'Россия',
    'россиянин': 'Россия',
    'россиянка': 'Россия',
    'rus': 'Россия',
    'россия': 'Россия',
    
    'американская': 'США',
    'американец': 'США',
    'американка': 'США',
    'usa': 'США',
    'united states': 'США',
    'сша': 'США',
    
    'немецкая': 'Германия',
    'немец': 'Германия',
    'немка': 'Германия',
    'german': 'Германия',
    'ger': 'Германия',
    'германия': 'Германия',
    
    'французская': 'Франция',
    'француз': 'Франция',
    'французженка': 'Франция',
    'french': 'Франция',
    'fra': 'Франция',
    'франция': 'Франция',
    
    'британская': 'Великобритания',
    'английская': 'Великобритания',
    'англичанин': 'Великобритания',
    'англичанка': 'Великобритания',
    'british': 'Великобритания',
    'uk': 'Великобритания',
    'gbr': 'Великобритания',
    'великобритания': 'Великобритания',
    
    'китайская': 'Китай',
    'китаец': 'Китай',
    'китаянка': 'Китай',
    'chinese': 'Китай',
    'chn': 'Китай',
    'китай': 'Китай',
    
    'узбекская': 'Узбекистан',
    'узбек': 'Узбекистан',
    'узбечка': 'Узбекистан',
    'uzb': 'Узбекистан',
    'узбекистан': 'Узбекистан',
    
    'кыргызская': 'Кыргызстан',
    'кыргыз': 'Кыргызстан',
    'кыргызка': 'Кыргызстан',
    'kgz': 'Кыргызстан',
    'кыргызстан': 'Кыргызстан',
    
    'таджикская': 'Таджикистан',
    'таджик': 'Таджикистан',
    'таджичка': 'Таджикистан',
    'tjk': 'Таджикистан',
    'таджикистан': 'Таджикистан',
    
    'турецкая': 'Турция',
    'турок': 'Турция',
    'турчанка': 'Турция',
    'turkish': 'Турция',
    'tur': 'Турция',
    'турция': 'Турция'
  };
  
  const normalized = nationality.toLowerCase().trim();
  
  // Try direct mapping first
  if (nationalityMap[normalized]) {
    console.log(`Mapped nationality "${nationality}" to "${nationalityMap[normalized]}"`);
    return nationalityMap[normalized];
  }
  
  // Try partial matching
  for (const [key, value] of Object.entries(nationalityMap)) {
    if (normalized.includes(key) || key.includes(normalized)) {
      console.log(`Partially mapped nationality "${nationality}" to "${value}" via "${key}"`);
      return value;
    }
  }
  
  // If no mapping found, check if it's already in the countries list
  const foundCountry = COUNTRIES.find(country => 
    country.toLowerCase() === normalized || 
    normalized.includes(country.toLowerCase())
  );
  
  if (foundCountry) {
    console.log(`Found nationality "${nationality}" in countries list as "${foundCountry}"`);
    return foundCountry;
  }
  
  console.warn(`Could not map nationality "${nationality}" to any country`);
  return nationality; // Return as-is if we can't map it
};

/**
 * Parse JSON response from Claude
 */
const parseClaudeResponse = (response: ClaudeVisionResponse): ParsedNameData => {
  try {
    const content = response.content[0]?.text || '';
    console.log('Raw Claude response:', content);
    
    // Try to extract JSON from response
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      // If no JSON found, try to parse the whole response as JSON
      jsonMatch = [content];
    }
    
    const jsonData = JSON.parse(jsonMatch[0]);
    
    // Convert null values to undefined and ensure proper types
    const rawNationality = jsonData.nationality || undefined;
    const mappedCitizenship = rawNationality ? mapNationalityToCitizenship(rawNationality) : undefined;
    
    // Ensure names are in Latin (apply transliteration as fallback)
    const rawName = jsonData.name || undefined;
    const rawSurname = jsonData.surname || undefined;
    const transliteratedName = rawName ? transliterateName(rawName) : undefined;
    const transliteratedSurname = rawSurname ? transliterateName(rawSurname) : undefined;
    
    const parsedData: ParsedNameData = {
      name: transliteratedName,
      surname: transliteratedSurname,
      dateOfBirth: jsonData.dateOfBirth ? convertDateFormat(jsonData.dateOfBirth) : undefined,
      passportIssueDate: jsonData.passportIssueDate ? convertDateFormat(jsonData.passportIssueDate) : undefined,
      passportExpiryDate: jsonData.passportExpiryDate ? convertDateFormat(jsonData.passportExpiryDate) : undefined,
      passportNumber: jsonData.passportNumber || undefined,
      nationality: rawNationality,
      citizenship: mappedCitizenship,
      rawText: content,
      source: 'claude' as const
    };
    
    console.log('Parsed data from Claude:', parsedData);
    console.log('Data conversions applied:', {
      'name': rawName ? `${rawName} -> ${transliteratedName}` : 'not converted',
      'surname': rawSurname ? `${rawSurname} -> ${transliteratedSurname}` : 'not converted',
      'dateOfBirth': jsonData.dateOfBirth ? `${jsonData.dateOfBirth} -> ${parsedData.dateOfBirth}` : 'not converted',
      'passportIssueDate': jsonData.passportIssueDate ? `${jsonData.passportIssueDate} -> ${parsedData.passportIssueDate}` : 'not converted',
      'passportExpiryDate': jsonData.passportExpiryDate ? `${jsonData.passportExpiryDate} -> ${parsedData.passportExpiryDate}` : 'not converted',
      'citizenship': rawNationality ? `${rawNationality} -> ${mappedCitizenship}` : 'not converted'
    });
    return parsedData;
  } catch (error) {
    console.error('Error parsing Claude response:', error);
    // Return raw text if JSON parsing fails
    return {
      rawText: response.content[0]?.text || '',
      source: 'claude' as const
    };
  }
};

/**
 * Process document with Claude Vision API using temporary file upload
 */
export const processDocumentWithClaude = async (
  filePath: string,
  fileType: 'image' | 'pdf',
  agentId: string
): Promise<ClaudeVisionProcessResult> => {
  let tempFile: TempFileInfo | null = null;

  try {
    console.log('=== PROCESSING DOCUMENT WITH CLAUDE VISION ===');
    console.log('File path:', filePath);
    console.log('File type:', fileType);
    console.log('Agent ID:', agentId);

    // Get configuration
    const config = getClaudeConfig();

    // Upload file to temporary storage
    tempFile = await uploadTempFile(filePath);

    // Create request
    const request: ClaudeVisionRequest = {
      model: config.model,
      max_tokens: config.maxTokens,
      messages: [{
        role: 'user',
        content: [
          {
            type: 'text',
            text: PASSPORT_OCR_PROMPT
          },
          {
            type: fileType === 'pdf' ? 'document' : 'image',
            source: {
              type: 'url',
              url: tempFile.url
            }
          }
        ]
      }]
    };

    // Call Claude Vision API
    const response = await callClaudeVisionApi(request, config);

    // Parse response
    const extractedData = parseClaudeResponse(response);

    console.log('Claude Vision processing completed successfully');
    console.log('Tokens used:', response.usage);

    return {
      success: true,
      extractedData,
      rawText: response.content[0]?.text || '',
      tokensUsed: response.usage
    };

  } catch (error) {
    console.error('Error processing document with Claude Vision:', error);
    return {
      success: false,
      rawText: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    // Cleanup temporary file
    if (tempFile) {
      try {
        await tempFile.cleanup();
      } catch (cleanupError) {
        console.warn('Error cleaning up temp file:', cleanupError);
      }
    }
  }
};

/**
 * Check if Claude OCR is available
 */
export const isClaudeOcrAvailable = (): boolean => {
  return !!USE_CLAUDE_OCR;
};

/**
 * Get Claude OCR configuration status
 */
export const getClaudeOcrStatus = (): ClaudeOcrStatus => {
  return {
    available: !!USE_CLAUDE_OCR,
    hasApiKey: !!process.env.NEXT_CLAUDE_KEY,
    model: DEFAULT_CONFIG.model
  };
};