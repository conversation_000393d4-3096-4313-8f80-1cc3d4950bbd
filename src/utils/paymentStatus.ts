// Payment Status Constants for Database Compatibility
export const PAYMENT_STATUS = {
  PAID: 'оплачено',
  UNPAID: 'не оплачено'
} as const;

// Type-safe payment status type
export type PaymentStatusType = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];

// Safe boolean conversion from payment status string
export const isPaymentPaid = (status: string): boolean => {
  return status === PAYMENT_STATUS.PAID;
};

// Convert boolean to payment status string (for backward compatibility)
export const booleanToPaymentStatusString = (isPaid: boolean): PaymentStatusType => {
  return isPaid ? PAYMENT_STATUS.PAID : PAYMENT_STATUS.UNPAID;
};

// Convert payment status string to boolean
export const paymentStatusToBoolean = (status: string): boolean => {
  return status === PAYMENT_STATUS.PAID;
};

// Safe payment status validation
export const isValidPaymentStatus = (status: string): status is PaymentStatusType => {
  return Object.values(PAYMENT_STATUS).includes(status as PaymentStatusType);
};

// Payment status display names for UI
export const PAYMENT_STATUS_DISPLAY = {
  [PAYMENT_STATUS.PAID]: 'Оплачено',
  [PAYMENT_STATUS.UNPAID]: 'Не оплачено'
} as const;

// Get display name for payment status
export const getPaymentStatusDisplay = (status: string): string => {
  return PAYMENT_STATUS_DISPLAY[status as PaymentStatusType] || 'Неизвестно';
};

// Get display name from boolean payment status
export const getBooleanPaymentStatusDisplay = (isPaid: boolean): string => {
  return isPaid ? PAYMENT_STATUS_DISPLAY[PAYMENT_STATUS.PAID] : PAYMENT_STATUS_DISPLAY[PAYMENT_STATUS.UNPAID];
}; 