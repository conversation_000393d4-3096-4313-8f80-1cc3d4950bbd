// Input sanitization utilities to prevent XSS and ensure data integrity

// Basic HTML entity encoding to prevent XSS
export const sanitizeHtml = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

// Sanitize text input - removes dangerous characters but preserves formatting
export const sanitizeTextInput = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  // Remove null bytes, control characters (except common whitespace)
  return input
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    .trim();
};

// Sanitize name fields (only letters, spaces, hyphens, apostrophes)
export const sanitizeName = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .replace(/[^\p{L}\s\-']/gu, '') // Unicode letters, spaces, hyphens, apostrophes
    .replace(/\s+/g, ' ') // Multiple spaces to single space
    .replace(/[-']{2,}/g, match => match[0]) // Multiple consecutive hyphens/apostrophes to single
    .trim();
};

// Sanitize email input
export const sanitizeEmail = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .toLowerCase()
    .replace(/[^\w@.-]/g, '')
    .trim();
};

// Sanitize phone number input (Kazakhstan format)
export const sanitizePhone = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  // Allow only digits, spaces, +, -, (, )
  return input.replace(/[^\d\s+()-]/g, '');
};

// Sanitize URL input (for social media links)
export const sanitizeUrl = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  const sanitized = input.trim();
  
  // Check if it's a valid URL format
  try {
    // If it doesn't start with protocol, add https://
    const urlToCheck = sanitized.startsWith('http') ? sanitized : `https://${sanitized}`;
    const url = new URL(urlToCheck);
    
    // Only allow certain protocols
    if (!['http:', 'https:'].includes(url.protocol)) {
      return '';
    }
    
    // Basic domain validation for social media
    const allowedDomains = [
      'instagram.com', 'www.instagram.com',
      'facebook.com', 'www.facebook.com',
      'twitter.com', 'www.twitter.com', 'x.com', 'www.x.com',
      'linkedin.com', 'www.linkedin.com',
      'tiktok.com', 'www.tiktok.com',
      'youtube.com', 'www.youtube.com',
      'telegram.me', 'telegram.org', 't.me'
    ];
    
    const domain = url.hostname.toLowerCase();
    if (!allowedDomains.some(allowed => domain === allowed || domain.endsWith(`.${allowed}`))) {
      return '';
    }
    
    return url.toString();
  } catch {
    return '';
  }
};

// Sanitize country names (for Step 9 travel history)
export const sanitizeCountryName = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .replace(/[^\p{L}\s\-'()]/gu, '') // Unicode letters, spaces, hyphens, apostrophes, parentheses
    .replace(/\s+/g, ' ') // Multiple spaces to single space
    .trim()
    .slice(0, 100); // Limit length to 100 characters
};

// Sanitize address input
export const sanitizeAddress = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .replace(/[^\p{L}\p{N}\s\-.,#/\\]/gu, '') // Letters, numbers, spaces, common address chars
    .replace(/\s+/g, ' ') // Multiple spaces to single space
    .trim()
    .slice(0, 250); // Limit length
};

// Sanitize numeric input (IIN, passport numbers, etc.)
export const sanitizeNumeric = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input.replace(/[^\d]/g, '');
};

// Sanitize alphanumeric input (passport numbers, visa numbers)
export const sanitizeAlphanumeric = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .replace(/[^\p{L}\p{N}]/gu, '') // Only letters and numbers
    .toUpperCase()
    .trim();
};

// Sanitize file name
export const sanitizeFileName = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .replace(/[<>:"/\\|?*\x00-\x1f]/g, '') // Remove dangerous file name characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .slice(0, 100); // Limit length
};

// General purpose sanitizer that applies appropriate sanitization based on field type
export const sanitizeByFieldType = (value: string, fieldType: 'text' | 'name' | 'email' | 'phone' | 'url' | 'address' | 'numeric' | 'alphanumeric' | 'country'): string => {
  if (!value || typeof value !== 'string') return '';
  
  switch (fieldType) {
    case 'name':
      return sanitizeName(value);
    case 'email':
      return sanitizeEmail(value);
    case 'phone':
      return sanitizePhone(value);
    case 'url':
      return sanitizeUrl(value);
    case 'address':
      return sanitizeAddress(value);
    case 'numeric':
      return sanitizeNumeric(value);
    case 'alphanumeric':
      return sanitizeAlphanumeric(value);
    case 'country':
      return sanitizeCountryName(value);
    case 'text':
    default:
      return sanitizeTextInput(value);
  }
};