// Custom localization system - Vercel-friendly alternative to i18next
import { useRouter } from 'next/router';
import { createContext, useContext, ReactNode } from 'react';

// Language type
export type Language = 'ru' | 'en' | 'kz';

// Translation structure
export interface Translations {
  [key: string]: string | Translations;
}

// All translations in one place
const translations: Record<Language, Translations> = {
  ru: {
    common: {
      loading: 'Загрузка...',
      save: 'Сохранить',
      cancel: 'Отмена',
      delete: 'Удалить',
      edit: 'Редактировать',
      add: 'Добавить',
      search: 'Поиск',
      filter: 'Фильтр',
      export: 'Экспорт',
      refresh: 'Обновить',
      close: 'Закрыть',
      yes: 'Да',
      no: 'Нет',
      error: 'Ошибка',
      success: 'Успешно',
      warning: 'Предупреждение',
      info: 'Информация',
      adminPanel: 'Панель администратора',
      superAdmin: 'Супер администратор',
      admin: 'Администратор',
      male: 'Мужской',
      female: 'Женский',
      dateNotSpecified: 'Дата не указана',
      invalidDate: 'Неверная дата',
      dateError: 'Ошибка даты',
      currency: 'теңге',
      notSpecified: 'Не указано',
      notSpecifiedFemale: 'Не указана',
      notSpecifiedMale: 'Не указан',
      totalApplications: 'Всего заявок',
      allCountries: 'Все страны',
      manager: 'Менеджер',
      phoneNotSpecified: 'Телефон не указан',
      emailNotSpecified: 'Email не указан',
      paidConsularFee: 'Консульский сбор оплачен',
      notPaidConsularFee: 'Консульский сбор не оплачен',
      atTime: 'в',
      noClients: 'Нет клиентов',
      editClientTitle: 'Редактировать клиента',
      dataRefreshed: 'Данные успешно обновлены',
      dataRefreshError: 'Ошибка при обновлении данных',
      consularFeePayment: 'Консульский сбор',
      actions: 'Действия',
      clientsNotFound: 'Клиенты не найдены'
    },
    navigation: {
      dashboard: 'Главное',
      clients: 'Клиенты',
      statistics: 'Статистика',
      companies: 'Компании',
      employees: 'Сотрудники',
      settings: 'Настройки',
      priceList: 'Прайс-лист',
      logout: 'Выйти'
    },
    dashboard: {
      periodLabel: 'Период',
      title: 'Панель управления',
      period: 'Период',
      resetFilter: 'Сбросить фильтр',
      addClient: 'Добавить клиента',
      refreshData: 'Обновить данные',
      refresh: 'Обновить',
      exportExcel: 'Экспорт Excel',
      help: 'Справка',
      helpTitle: 'Справка',
      totalClients: 'Всего клиентов',
      purchased: 'Купили пакет',
      submitted: 'Подано',
      income: 'Доход',
      incomeLabel: 'Доход',
      awaitingPayment: 'Ожидают оплаты',
      requiresFixes: 'Требуют правок',
      requiresFixesComment: 'Требуются правки'
    },
    statistics: {
      allCountries: 'Все страны',
      country: 'Страна',
      title: 'Статистика',
      loadingStatistics: 'Загрузка статистики...',
      dynamicAnalytics: 'Динамическая аналитика с фильтрацией по датам и странам',
      dateFrom: 'Дата от',
      dateTo: 'Дата до',
      visaCountry: 'Страна визы',
      company: 'Компания',
      allCompanies: 'Все компании',
      approvalPercentage: '% Одобрений',
      totalIncome: 'Общий доход',
      topCountry: 'Топ страна',
      noData: 'Нет данных',
      applications: 'заявок',
      approved: 'одобрено',
      out: 'из',
      rejectionReasons: 'Причины отказов',
      submissionDynamics: 'Динамика подачи заявок',
      apiError: 'Ошибка API',
      loading: 'Загрузка...',
      loadingError: 'Ошибка при загрузке аналитики',
      noDataToDisplay: 'Нет данных для отображения',
      incomeVsApproval: 'Доход против одобрений',
      approvalRateLabel: 'Процент одобрений',
      approvalPercent: 'Процент одобрений',
      submitted: 'Подано',
      loadingMessage: 'Загрузка статистики...',
      totalApplications: 'Всего заявок',
      total: 'Всего',
      rejected: 'Отклонено',
      income: 'Доход',
      currency: 'теңге',
      statusDistribution: 'Распределение по статусам',
      topProfessions: 'Топ профессии',
      ageDistribution: 'Распределение по возрасту',
      genderStats: 'Статистика по полу',
      countryOverview: 'Обзор по странам',
      countryStats: 'Статистика по стране',
      months: {
        'янв.': 'янв.',
        'февр.': 'февр.',
        'март': 'март',
        'апр.': 'апр.',
        'май': 'май',
        'июнь': 'июнь',
        'июль': 'июль',
        'авг.': 'авг.',
        'сент.': 'сент.',
        'окт.': 'окт.',
        'нояб.': 'нояб.',
        'дек.': 'дек.'
      }
    },
    visaStatus: {
      pending: 'Ожидает',
      submitted: 'Подано',
      approved: 'Одобрено',
      rejected: 'Отклонено',
      unknown: 'Неизвестно'
    },
    paymentStatus: {
      paid: 'оплачено',
      unpaid: 'не оплачено',
      pending: 'ожидает оплаты'
    },
    rejectionReasons: {
      insufficientFinancialDocuments: 'Недостаточные финансовые документы',
      incompleteDocuments: 'Неполный пакет документов',
      doubtsAboutTravelPurpose: 'Сомнения в цели поездки',
      invitationProblems: 'Проблемы с приглашением',
      otherReasons: 'Другие причины'
    },
    incomeRanges: {
      low: 'До 450,000 теңге',
      medium: '450,000 - 1,350,000 теңге',
      mediumHigh: '1,350,000 - 2,250,000 теңге',
      high: '2,250,000 - 4,500,000 теңге',
      veryHigh: 'Свыше 4,500,000 теңге'
    },
    clients: {
      paidConsularFee: 'Консульский сбор оплачен',
      notPaidConsularFee: 'Консульский сбор не оплачен',
      helpButton: 'Помощь',
      title: 'Управление клиентами',
      addClient: 'Добавить клиента',
      addPaidClient: 'Добавить оплаченного клиента',
      editClient: 'Редактировать клиента',
      deleteClient: 'Удалить клиента',
      clientId: 'ID клиента',
      firstName: 'Имя',
      lastName: 'Фамилия',
      phone: 'Телефон',
      email: 'Email',
      age: 'Возраст',
      visaCountry: 'Страна визы',
      profession: 'Профессия',
      income: 'Доход (теңге)',
      maritalStatus: 'Семейное положение',
      visaStatus: 'Статус визы',
      paymentStatus: 'Статус оплаты',
      servicePackage: 'Пакет услуг',
      packagePrice: 'Стоимость пакета (тенге)',
      consularFee: 'Консульский сбор',
      requiresFixes: 'Требуются правки',
      comment: 'Комментарий',
      currentStep: 'Текущий шаг',
      createdDate: 'Дата создания',
      updatedDate: 'Дата обновления',
      visaHistory: 'Визовая история',
      hasProperty: 'Имущество',
      hasSponsor: 'Спонсор',
      paid: 'Оплачен',
      unpaid: 'Не оплачен',
      paidService: 'оплачено',
      unpaidService: 'не оплачено',
      hasVisa: 'Есть',
      noVisa: 'Нет',
      noDataForExport: 'Нет данных для экспорта',
      exportSuccess: 'Данные успешно экспортированы',
      fileCreated: 'Файл создан с цветовым кодированием',
      clientsCount: 'клиентов',
      clientsSingular: 'клиент',
      clientsPlural: 'а',
      phoneNotSpecified: 'Телефон не указан',
      emailNotSpecified: 'Email не указан',
      paidConsularFee: 'Консульский сбор оплачен',
      notPaidConsularFee: 'Консульский сбор не оплачен',
      quickActions: 'Быстрые действия',
      backToCountries: 'Назад к странам',
      showAllCountries: 'Показать все страны',
      boardView: 'Trello',
      tableView: 'Таблица',
      advancedFilter: 'Расширенный фильтр',
      help: 'Помощь',
      unpaidClientsDescription: 'Все клиенты, которые не оплатили услуги (любой этап анкеты)',
      exportData: 'Экспорт данных',
      refreshData: 'Обновить данные',
      removeDuplicates: 'Удалить дубликаты',
      selectCountry: 'Выберите страну для управления клиентами',
      totalApplicationsLabel: 'Всего заявок за весь период',
      filteredApplicationsLabel: 'Отфильтровано заявок',
      dataExportedSuccess: 'Данные успешно экспортированы',
      fileCreatedWithCoding: 'Файл создан с цветовым кодированием',
      exportError: 'Ошибка при экспорте данных',
      tryAgainOrContact: 'Попробуйте еще раз или обратитесь к администратору',
      dataRefreshed: 'Данные обновлены',
      duplicateCheckError: 'Ошибка при проверке дубликатов',
      duplicatesNotFound: 'Дубликаты не найдены',
      allRecordsUnique: 'Все записи уникальны',
      duplicatesFoundCount: 'Найдено',
      duplicatesFoundSuffix: 'дубликат',
      duplicatesFoundPlural: 'ов',
      duplicatesDeleteConfirm: 'Будут удалены более новые записи, оригинальные записи останутся.',
      continueRemoval: 'Продолжить удаление?',
      duplicatesRemoved: 'Дубликаты удалены',
      successfullyRemoved: 'Успешно удалено',
      removeDuplicatesError: 'Ошибка при удалении дубликатов',
      operationError: 'Произошла ошибка при выполнении операции',
      filters: {
        name: 'Имя',
        searchByName: 'Поиск по имени',
        activity: 'Деятельность',
        profession: 'Профессия',
        incomeFrom: 'Доход от',
        minimum: 'Минимум',
        incomeTo: 'Доход до',
        maximum: 'Максимум',
        visaHistory: 'Визовая история',
        all: 'Все',
        hasVisaHistory: 'Есть',
        noVisaHistory: 'Нет',
        property: 'Недвижимость',
        hasProperty: 'Есть',
        noProperty: 'Нет',
        maritalStatus: 'Семейное положение',
        married: 'Женат/Замужем',
        single: 'Холост/Не замужем',
        divorced: 'Разведен(а)',
        widowed: 'Вдовец/Вдова',
        sponsor: 'Спонсор',
        hasSponsor: 'Есть',
        noSponsor: 'Нет',
        clearFilters: 'Очистить фильтры',
        advancedFilters: 'Расширенные фильтры',
        resetFilters: 'Сбросить фильтры'
      },
      modal: {
        dateNotSpecified: 'Дата не указана',
        invalidDate: 'Неверная дата',
        dateError: 'Ошибка даты',
        phoneFormatError: 'Неверный формат телефона. Используйте формат: +7 XXX XXX XX XX',
        personalInformation: 'Личная информация',
        applicationStatus: 'Статус заявки',
        agentInformation: 'Информация об агенте',
        save: 'Сохранить',
        locked: 'Заблокировано',
        cancel: 'Отменить',
        loading: 'Загрузка...',
        required: 'Обязательное поле',
        editStepRestriction: 'Этап прогресса доступен только для чтения. Редактирование недоступно для визовых администраторов',
        stepEditRestricted: 'Изменение текущего шага недоступно для суперадмина',
        stepEditRestrictedInvitation: 'Изменение текущего шага доступно только при наличии приглашения',
        servicePackageEditRestricted: 'Изменение пакета услуг недоступно для суперадмина. Только визовые компании могут изменять пакеты услуг',
        invitationUpload: 'Загрузка приглашения',
        phoneAlreadyExists: 'Клиент с таким номером телефона уже существует',
        phoneExistsDetails: 'Номер {phone} уже зарегистрирован в системе (Клиент: {clientName})',
        phoneNumberTaken: 'Этот номер телефона уже используется',
        chooseAnotherPhone: 'Пожалуйста, выберите другой номер телефона',
        visaStatuses: {
          pending: 'ожидает',
          submitted: 'подано',
          approved: 'одобрено',
          rejected: 'отклонено'
        },
        paymentStatuses: {
          unpaid: 'не оплачено',
          paid: 'оплачено',
          partial: 'частично оплачено'
        },
        stepStatuses: {
          1: 'Начальный этап',
          2: 'Опросник заполнен',
          3: 'Анкета заполнена',
          4: 'Проверка документов',
          5: 'Подготовка кейса',
          6: 'Ожидание приглашения',
          7: 'Координация кейса',
          8: 'Заполнение форм',
          9: 'Подача документов',
          10: 'Завершено'
        }
      },
      excel: {
        clientNumber: '№',
        clientId: 'ID клиента',
        firstName: 'Имя',
        lastName: 'Фамилия',
        phone: 'Телефон',
        email: 'Email',
        age: 'Возраст',
        visaCountry: 'Страна визы',
        profession: 'Профессия',
        income: 'Доход (теңге)',
        maritalStatus: 'Семейное положение',
        visaStatus: 'Статус визы',
        paymentStatus: 'Статус оплаты',
        servicePackage: 'Пакет услуг',
        packagePrice: 'Стоимость пакета (тенге)',
        consularFee: 'Консульский сбор',
        requiresFixes: 'Требуются правки',
        comment: 'Комментарий',
        currentStep: 'Этап прогресса',
        createdDate: 'Дата создания',
        updatedDate: 'Дата обновления',
        visaHistory: 'Визовая история',
        property: 'Имущество',
        sponsor: 'Спонсор',
        paid: 'Оплачен',
        unpaid: 'Не оплачен',
        yes: 'Да',
        no: 'Нет',
        has: 'Есть',
        hasNot: 'Нет',
        sheetName: 'Клиенты'
      },
      help: {
        title: 'Справочная информация',
        subtitle: 'Руководство по использованию админ-панели',
        close: 'Закрыть',
        mainFunctions: {
          title: 'Основные функции',
          dashboard: 'Главное - общая статистика и важные уведомления',
          clients: 'Клиенты - управление заявками клиентов по странам',
          statistics: 'Статистика - детальная аналитика по периодам',
          priceList: 'Прайс-лист - управление пакетами услуг и ценами'
        },
        clientWork: {
          title: 'Работа с клиентами',
          addClient: 'Добавить клиента - создание новой заявки с автоматической отправкой ссылки',
          filters: 'Фильтры - поиск клиентов по различным критериям',
          processStages: 'Этапы процесса - отслеживание прогресса каждой заявки',
          invitations: 'Загрузка приглашений - управление документами клиентов'
        },
        applicationStatuses: {
          title: 'Статусы заявок',
          pendingPayment: 'Ожидается оплата - клиенты, прошедшие опросник',
          requiresFixes: 'Требуются правки - заявки с ошибками в документах',
          waitingInvitation: 'Ожидание приглашения - заявки на этапе получения приглашения',
          submitted: 'Подано - заявки, поданные в консульство'
        },
        exportReports: {
          title: 'Экспорт и отчеты',
          excelExport: 'Экспорт Excel - сохранение данных в формате CSV',
          filterExport: 'Фильтрация перед экспортом - выгрузка только нужных данных',
          periodStatistics: 'Статистика по периодам - отчеты за выбранные даты',
          countryAnalytics: 'Аналитика по странам - разбивка по направлениям'
        },
        hotkeys: {
          title: 'Горячие клавиши',
          escape: 'ESC - закрыть модальные окна',
          search: 'Ctrl/Cmd + F - поиск на странице',
          refresh: 'F5 - обновить данные',
          navigation: 'Tab - навигация между элементами'
        },
        rolesAccess: {
          title: 'Роли и права доступа',
          superAdmin: 'Супер Администратор - полный доступ ко всем функциям',
          admin: 'Администратор - управление клиентами и статистикой',
          manager: 'Менеджер - работа с клиентами в рамках компании',
          stageRestrictions: 'Ограничения этапов - изменение этапов только при наличии приглашения'
        },
        importantInfo: {
          title: 'Важная информация',
          security: {
            title: '🔐 Безопасность',
            description: 'Всегда выходите из системы после завершения работы. Не передавайте свои учетные данные третьим лицам.'
          },
          backup: {
            title: '💾 Резервное копирование',
            description: 'Регулярно экспортируйте данные для создания резервных копий важной информации.'
          },
          support: {
            title: '📞 Поддержка',
            description: 'При возникновении проблем обращайтесь к техническому администратору системы.'
          },
          updates: {
            title: '🔄 Обновления',
            description: 'Система регулярно обновляется. Следите за уведомлениями о новых функциях.'
          }
        },
        footer: 'Система управления визовыми заявками'
      }
    },
    companies: {
      title: 'Управление компаниями',
      addCompany: 'Добавить компанию',
      editCompany: 'Редактировать компанию',
      deleteCompany: 'Удалить компанию',
      description: 'Управление визовыми компаниями и их администраторами'
    },
    countries: {
      US: 'США',
      USA: 'США',
      UK: 'Великобритания',
      EU: 'Европа',
      Schengen: 'Шенген',
      CN: 'Китай',
      China: 'Китай',
      Canada: 'Канада',
      Australia: 'Австралия',
      Germany: 'Германия',
      France: 'Франция',
      Italy: 'Италия',
      Spain: 'Испания',
      Netherlands: 'Нидерланды',
      Belgium: 'Бельгия',
      Austria: 'Австрия',
      Switzerland: 'Швейцария',
      Portugal: 'Португалия',
      Poland: 'Польша',
      'Czech Republic': 'Чехия',
      Hungary: 'Венгрия',
      Greece: 'Греция',
      Sweden: 'Швеция',
      Norway: 'Норвегия',
      Denmark: 'Дания',
      Finland: 'Финляндия',
      Japan: 'Япония',
      'South Korea': 'Южная Корея',
      Singapore: 'Сингапур',
      'New Zealand': 'Новая Зеландия',
      Brazil: 'Бразилия',
      Mexico: 'Мексика',
      India: 'Индия',
      Thailand: 'Таиланд',
      UAE: 'ОАЭ',
      Turkey: 'Турция',
      Israel: 'Израиль',
      'South Africa': 'ЮАР'
    },
    employees: {
      title: 'Управление сотрудниками',
      description: 'Управление сотрудниками компаний',
      addManager: 'Добавить менеджера'
    }
  },
  en: {
    common: {
      loading: 'Loading...',
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      add: 'Add',
      search: 'Search',
      filter: 'Filter',
      export: 'Export',
      refresh: 'Refresh',
      close: 'Close',
      yes: 'Yes',
      no: 'No',
      error: 'Error',
      success: 'Success',
      warning: 'Warning',
      info: 'Information',
      adminPanel: 'Admin Panel',
      superAdmin: 'Super Administrator',
      admin: 'Administrator',
      male: 'Male',
      female: 'Female',
      dateNotSpecified: 'Date not specified',
      invalidDate: 'Invalid date',
      dateError: 'Date error',
      currency: 'tenge',
      notSpecified: 'Not specified',
      notSpecifiedFemale: 'Not specified',
      notSpecifiedMale: 'Not specified',
      totalApplications: 'Total Applications',
      allCountries: 'All Countries',
      manager: 'Manager',
      phoneNotSpecified: 'Phone not specified',
      emailNotSpecified: 'Email not specified',
      paidConsularFee: 'Consular fee paid',
      notPaidConsularFee: 'Consular fee not paid',
      atTime: 'at',
      noClients: 'No clients',
      editClientTitle: 'Edit client',
      dataRefreshed: 'Data refreshed successfully',
      dataRefreshError: 'Error refreshing data'
    },
    navigation: {
      dashboard: 'Dashboard',
      clients: 'Clients',
      statistics: 'Statistics',
      companies: 'Companies',
      employees: 'Employees',
      settings: 'Settings',
      priceList: 'Price List',
      logout: 'Logout'
    },
    dashboard: {
      title: 'Dashboard',
      periodLabel: 'Period',
      period: 'Period',
      resetFilter: 'Reset Filter',
      addClient: 'Add Client',
      refreshData: 'Refresh Data',
      refresh: 'Refresh',
      exportExcel: 'Export Excel',
      help: 'Help',
      helpTitle: 'Help',
      totalClients: 'Total Clients',
      purchased: 'Purchased Package',
      submitted: 'Submitted',
      income: 'Income',
      incomeLabel: 'Income',
      awaitingPayment: 'Awaiting Payment',
      requiresFixes: 'Requires Fixes',
      requiresFixesComment: 'Requires fixes'
    },
    statistics: {
      allCountries: 'All Countries',
      country: 'Country',
      title: 'Statistics',
      loadingStatistics: 'Loading statistics...',
      dynamicAnalytics: 'Dynamic analytics with date and country filtering',
      dateFrom: 'Date from',
      dateTo: 'Date to',
      visaCountry: 'Visa country',
      company: 'Company',
      allCompanies: 'All Companies',
      approvalPercentage: '% Approvals',
      totalIncome: 'Total income',
      topCountry: 'Top country',
      noData: 'No data',
      applications: 'applications',
      approved: 'approved',
      out: 'out of',
      rejectionReasons: 'Rejection Reasons',
      submissionDynamics: 'Submission Dynamics',
      apiError: 'API Error',
      loading: 'Loading...',
      loadingError: 'Error loading analytics',
      noDataToDisplay: 'No data to display',
      incomeVsApproval: 'Income vs Approval',
      approvalRateLabel: 'Approval Rate',
      approvalPercent: 'Approval Percentage',
      submitted: 'Submitted',
      loadingMessage: 'Loading statistics...',
      totalApplications: 'Total Applications',
      total: 'Total',
      rejected: 'Rejected',
      income: 'Income',
      currency: 'tenge',
      statusDistribution: 'Status Distribution',
      topProfessions: 'Top Professions',
      ageDistribution: 'Age Distribution',
      genderStats: 'Gender Statistics',
      countryOverview: 'Country Overview',
      countryStats: 'Country Statistics',
      months: {
        'янв.': 'Jan',
        'февр.': 'Feb',
        'март': 'Mar',
        'апр.': 'Apr',
        'май': 'May',
        'июнь': 'Jun',
        'июль': 'Jul',
        'авг.': 'Aug',
        'сент.': 'Sep',
        'окт.': 'Oct',
        'нояб.': 'Nov',
        'дек.': 'Dec'
      }
    },
    visaStatus: {
      pending: 'Pending',
      submitted: 'Submitted',
      approved: 'Approved',
      rejected: 'Rejected',
      unknown: 'Unknown'
    },
    rejectionReasons: {
      insufficientFinancialDocuments: 'Insufficient financial documents',
      incompleteDocuments: 'Incomplete document package',
      doubtsAboutTravelPurpose: 'Doubts about travel purpose',
      invitationProblems: 'Invitation problems',
      otherReasons: 'Other reasons'
    },
    incomeRanges: {
      low: 'Up to 450,000 tenge',
      medium: '450,000 - 1,350,000 tenge',
      mediumHigh: '1,350,000 - 2,250,000 tenge',
      high: '2,250,000 - 4,500,000 tenge',
      veryHigh: 'Over 4,500,000 tenge'
    },
    clients: {
      paidConsularFee: 'Consular fee paid',
      notPaidConsularFee: 'Consular fee not paid',
      helpButton: 'Help',
      title: 'Client Management',
      addClient: 'Add Client',
      addPaidClient: 'Add Paid Client',
      editClient: 'Edit Client',
      deleteClient: 'Delete Client',
      clientId: 'Client ID',
      firstName: 'First Name',
      lastName: 'Last Name',
      phone: 'Phone',
      email: 'Email',
      age: 'Age',
      visaCountry: 'Visa Country',
      profession: 'Profession',
      income: 'Income (tenge)',
      maritalStatus: 'Marital Status',
      visaStatus: 'Visa Status',
      paymentStatus: 'Payment Status',
      servicePackage: 'Service Package',
      packagePrice: 'Package Price (tenge)',
      consularFee: 'Consular Fee',
      requiresFixes: 'Requires Fixes',
      comment: 'Comment',
      currentStep: 'Current Step',
      createdDate: 'Created Date',
      updatedDate: 'Updated Date',
      visaHistory: 'Visa History',
      hasProperty: 'Property',
      hasSponsor: 'Sponsor',
      paid: 'Paid',
      unpaid: 'Unpaid',
      paidService: 'paid',
      unpaidService: 'unpaid',
      hasVisa: 'Yes',
      noVisa: 'No',
      noDataForExport: 'No data for export',
      exportSuccess: 'Data exported successfully',
      fileCreated: 'File created with color coding',
      exportError: 'Error exporting data',
      tryAgain: 'Please try again or contact administrator',
      dataRefreshed: 'Data refreshed',
      duplicateCheckError: 'Error checking duplicates',
      noDuplicatesFound: 'No duplicates found',
      allRecordsUnique: 'All records are unique',
      duplicatesFound: 'Found duplicates',
      duplicatesSingular: '',
      duplicatesPlural: 's',
      duplicatesRemoveConfirm: 'Newer records will be deleted, original records will remain.',
      continueRemoval: 'Continue removal?',
      duplicatesRemoved: 'Duplicates removed',
      successfullyRemoved: 'Successfully removed',
      removeDuplicatesError: 'Error removing duplicates',
      operationError: 'An error occurred during the operation',
      clientsCount: 'clients',
      clientsSingular: 'client',
      clientsPlural: 's',
      quickActions: 'Quick Actions',
      backToCountries: 'Back to Countries',
      showAllCountries: 'Show All Countries',
      boardView: 'Board View',
      tableView: 'Table View',
      advancedFilter: 'Advanced Filter',
      help: 'Help',
      unpaidClientsDescription: 'All clients who have not paid for services (any form stage)',
      exportData: 'Export Data',
      refreshData: 'Refresh Data',
      removeDuplicates: 'Remove Duplicates',
      selectCountry: 'Select country to manage clients',
      totalApplicationsLabel: 'Total Applications',
      filteredApplicationsLabel: 'Filtered Applications',
      noDataForExport: 'No data to export',
      dataExportedSuccess: 'Data exported successfully',
      fileCreatedWithCoding: 'File created with color coding',
      exportError: 'Error exporting data',
      tryAgainOrContact: 'Please try again or contact administrator',
      dataRefreshed: 'Data refreshed',
      duplicateCheckError: 'Error checking duplicates',
      duplicatesNotFound: 'No duplicates found',
      allRecordsUnique: 'All records are unique',
      duplicatesFoundCount: 'Found',
      duplicatesFoundSuffix: 'duplicate',
      duplicatesFoundPlural: 's',
      duplicatesDeleteConfirm: 'Newer records will be deleted, original records will remain.',
      continueRemoval: 'Continue removal?',
      duplicatesRemoved: 'Duplicates removed',
      successfullyRemoved: 'Successfully removed',
      removeDuplicatesError: 'Error removing duplicates',
      operationError: 'An error occurred during the operation',
      filters: {
        name: 'Name',
        searchByName: 'Search by name',
        activity: 'Activity',
        profession: 'Profession',
        incomeFrom: 'Income from',
        incomeTo: 'Income to',
        minimum: 'Minimum',
        maximum: 'Maximum',
        visaHistory: 'Visa History',
        all: 'All',
        hasVisaHistory: 'Has',
        noVisaHistory: 'No',
        property: 'Property',
        hasProperty: 'Has',
        noProperty: 'No',
        maritalStatus: 'Marital Status',
        married: 'Married',
        single: 'Single',
        divorced: 'Divorced',
        widowed: 'Widowed',
        sponsor: 'Sponsor',
        hasSponsor: 'Has',
        noSponsor: 'No',
        clearFilters: 'Clear Filters',
        advancedFilters: 'Advanced Filters',
        resetFilters: 'Reset Filters'
      },
      modal: {
        dateNotSpecified: 'Date not specified',
        invalidDate: 'Invalid date',
        dateError: 'Date error',
        phoneFormatError: 'Invalid phone format. Use format: +7 XXX XXX XX XX',
        personalInformation: 'Personal Information',
        applicationStatus: 'Application Status',
        agentInformation: 'Agent Information',
        save: 'Save',
        cancel: 'Cancel',
        locked: 'Locked',
        loading: 'Loading...',
        required: 'Required field',
        editStepRestriction: 'Progress stage is read-only. Editing is not available for visa administrators',
        stepEditRestricted: 'Step modification is not available for super admin',
        stepEditRestrictedInvitation: 'Step modification is only available when invitation exists',
        servicePackageEditRestricted: 'Service package modification is not available for super admin. Only visa companies can modify service packages',
        invitationUpload: 'Invitation Upload',
        phoneAlreadyExists: 'Client with this phone number already exists',
        phoneExistsDetails: 'Number {phone} is already registered in the system (Client: {clientName})',
        phoneNumberTaken: 'This phone number is already in use',
        chooseAnotherPhone: 'Please choose another phone number',
        visaStatuses: {
          pending: 'pending',
          submitted: 'submitted',
          approved: 'approved',
          rejected: 'rejected'
        },
        paymentStatuses: {
          unpaid: 'unpaid',
          paid: 'paid',
          partial: 'partially paid'
        },
        stepStatuses: {
          1: 'Initial Stage',
          2: 'Questionnaire Completed',
          3: 'Form Completed',
          4: 'Document Review',
          5: 'Case Preparation',
          6: 'Waiting for Invitation',
          7: 'Case Coordination',
          8: 'Form Filling',
          9: 'Document Submission',
          10: 'Completed'
        }
      },
      excel: {
        clientNumber: '№',
        clientId: 'Client ID',
        firstName: 'First Name',
        lastName: 'Last Name',
        phone: 'Phone',
        email: 'Email',
        age: 'Age',
        visaCountry: 'Visa Country',
        profession: 'Profession',
        income: 'Income (tenge)',
        maritalStatus: 'Marital Status',
        visaStatus: 'Visa Status',
        paymentStatus: 'Payment Status',
        servicePackage: 'Service Package',
        packagePrice: 'Package Price (tenge)',
        consularFee: 'Consular Fee',
        requiresFixes: 'Requires Fixes',
        comment: 'Comment',
        currentStep: 'Progress Stage',
        createdDate: 'Created Date',
        updatedDate: 'Updated Date',
        visaHistory: 'Visa History',
        property: 'Property',
        sponsor: 'Sponsor',
        paid: 'Paid',
        unpaid: 'Unpaid',
        yes: 'Yes',
        no: 'No',
        has: 'Has',
        hasNot: 'No',
        sheetName: 'Clients'
      },
      help: {
        title: 'Reference Information',
        subtitle: 'Admin panel usage guide',
        close: 'Close',
        mainFunctions: {
          title: 'Main Functions',
          dashboard: 'Dashboard - general statistics and important notifications',
          clients: 'Clients - manage client applications by countries',
          statistics: 'Statistics - detailed analytics by periods',
          priceList: 'Price List - manage service packages and prices'
        },
        clientWork: {
          title: 'Working with Clients',
          addClient: 'Add Client - create new application with automatic link sending',
          filters: 'Filters - search clients by various criteria',
          processStages: 'Process Stages - track progress of each application',
          invitations: 'Upload Invitations - manage client documents'
        },
        applicationStatuses: {
          title: 'Application Statuses',
          pendingPayment: 'Pending Payment - clients who completed questionnaire',
          requiresFixes: 'Requires Fixes - applications with document errors',
          waitingInvitation: 'Waiting Invitation - applications at invitation stage',
          submitted: 'Submitted - applications submitted to consulate'
        },
        exportReports: {
          title: 'Export and Reports',
          excelExport: 'Excel Export - save data in CSV format',
          filterExport: 'Filter Before Export - export only needed data',
          periodStatistics: 'Period Statistics - reports for selected dates',
          countryAnalytics: 'Country Analytics - breakdown by destinations'
        },
        hotkeys: {
          title: 'Hotkeys',
          escape: 'ESC - close modal windows',
          search: 'Ctrl/Cmd + F - search on page',
          refresh: 'F5 - refresh data',
          navigation: 'Tab - navigate between elements'
        },
        rolesAccess: {
          title: 'Roles and Access Rights',
          superAdmin: 'Super Administrator - full access to all functions',
          admin: 'Administrator - manage clients and statistics',
          manager: 'Manager - work with clients within company',
          stageRestrictions: 'Stage Restrictions - change stages only with invitation'
        },
        importantInfo: {
          title: 'Important Information',
          security: {
            title: '🔐 Security',
            description: 'Always log out after finishing work. Do not share your credentials with third parties.'
          },
          backup: {
            title: '💾 Backup',
            description: 'Regularly export data to create backups of important information.'
          },
          support: {
            title: '📞 Support',
            description: 'Contact the technical system administrator if you encounter problems.'
          },
          updates: {
            title: '🔄 Updates',
            description: 'The system is regularly updated. Watch for notifications about new features.'
          }
        },
        footer: 'Visa application management system'
      }
    },
    companies: {
      title: 'Company Management',
      addCompany: 'Add Company',
      editCompany: 'Edit Company',
      deleteCompany: 'Delete Company',
      description: 'Manage visa companies and their administrators'
    },
    countries: {
      US: 'United States',
      USA: 'United States',
      UK: 'United Kingdom',
      EU: 'Schengen Area',
      Schengen: 'Schengen',
      CN: 'China',
      China: 'China',
      Canada: 'Canada',
      Australia: 'Australia',
      Germany: 'Germany',
      France: 'France',
      Italy: 'Italy',
      Spain: 'Spain',
      Netherlands: 'Netherlands',
      Belgium: 'Belgium',
      Austria: 'Austria',
      Switzerland: 'Switzerland',
      Portugal: 'Portugal',
      Poland: 'Poland',
      'Czech Republic': 'Czech Republic',
      Hungary: 'Hungary',
      Greece: 'Greece',
      Sweden: 'Sweden',
      Norway: 'Norway',
      Denmark: 'Denmark',
      Finland: 'Finland',
      Japan: 'Japan',
      'South Korea': 'South Korea',
      Singapore: 'Singapore',
      'New Zealand': 'New Zealand',
      Brazil: 'Brazil',
      Mexico: 'Mexico',
      India: 'India',
      Thailand: 'Thailand',
      UAE: 'UAE',
      Turkey: 'Turkey',
      Israel: 'Israel',
      'South Africa': 'South Africa'
    },
    employees: {
      title: 'Employee Management',
      description: 'Manage company employees',
      addManager: 'Add Manager'
    }
  },
  kz: {
    common: {
      loading: 'Жүктелуде...',
      save: 'Сақтау',
      cancel: 'Бас тарту',
      delete: 'Жою',
      edit: 'Өңдеу',
      add: 'Қосу',
      search: 'Іздеу',
      filter: 'Сүзгі',
      export: 'Экспорт',
      refresh: 'Жаңарту',
      close: 'Жабу',
      yes: 'Иә',
      no: 'Жоқ',
      error: 'Қате',
      success: 'Сәтті',
      warning: 'Ескерту',
      info: 'Ақпарат',
      adminPanel: 'Әкімші панелі',
      superAdmin: 'Супер әкімші',
      admin: 'Әкімші',
      male: 'Ер адам',
      female: 'Әйел адам',
              dateNotSpecified: 'Күні көрсетілмеген',
        invalidDate: 'Жарамсыз күн',
        dateError: 'Күн қатесі',
        phoneFormatError: 'Телефон форматы дұрыс емес. Форматты пайдаланыңыз: +7 XXX XXX XX XX',
      currency: 'теңге',
      notSpecified: 'Көрсетілмеген',
      notSpecifiedFemale: 'Көрсетілмеген',
      notSpecifiedMale: 'Көрсетілмеген',
      totalApplications: 'Барлық өтінімдер',
      allCountries: 'Барлық елдер',
      manager: 'Менеджер',
      phoneNotSpecified: 'Телефон көрсетілмеген',
      emailNotSpecified: 'Email көрсетілмеген',
      paidConsularFee: 'Консулдық жиым төленген',
      notPaidConsularFee: 'Консулдық жиым төленбеген',
      atTime: 'уақытында',
      noClients: 'Клиенттер жоқ',
      editClientTitle: 'Клиентті өңдеу',
      dataRefreshed: 'Деректер сәтті жаңартылды',
      dataRefreshError: 'Деректерді жаңарту қатесі'
    },
    navigation: {
      dashboard: 'Басты бет',
      clients: 'Клиенттер',
      statistics: 'Статистика',
      companies: 'Компаниялар',
      employees: 'Қызметкерлер',
      settings: 'Баптаулар',
      priceList: 'Баға тізімі',
      logout: 'Шығу'
    },
    dashboard: {
      periodLabel: 'Период',
      title: 'Басқару панелі',
      period: 'Кезең',
      resetFilter: 'Сүзгіні тазалау',
      addClient: 'Клиент қосу',
      refreshData: 'Деректерді жаңарту',
      refresh: 'Жаңарту',
      exportExcel: 'Excel экспорты',
      help: 'Анықтама',
      helpTitle: 'Анықтама',
      totalClients: 'Барлық клиенттер',
      purchased: 'Пакет сатып алды',
      submitted: 'Тапсырылды',
      income: 'Кіріс',
      incomeLabel: 'Кіріс',
      awaitingPayment: 'Төлемді күтуде',
      requiresFixes: 'Түзету қажет',
      requiresFixesComment: 'Түзету қажет'
    },
    statistics: {
      allCountries: 'Барлық елдер',
      title: 'Статистика',
      loadingStatistics: 'Статистика жүктелуде...',
      dynamicAnalytics: 'Күн мен ел бойынша сүзгілеу арқылы динамикалық аналитика',
      dateFrom: 'Бастап',
      dateTo: 'Дейін',
      visaCountry: 'Виза елі',
      company: 'Компания',
      allCompanies: 'Барлық компаниялар',
      approvalPercentage: '% Мақұлдау',
      totalIncome: 'Жалпы кіріс',
      topCountry: 'Топ ел',
      noData: 'Деректер жоқ',
      applications: 'өтінімдер',
      approved: 'мақұлданды',
      out: 'ішінен',
      rejectionReasons: 'Бас тарту себептері',
      submissionDynamics: 'Өтініш беру динамикасы',
      apiError: 'API қатесі',
      loading: 'Жүктелуде...',
      loadingError: 'Аналитиканы жүктеу қатесі',
      noDataToDisplay: 'Көрсету үшін деректер жоқ',
      incomeVsApproval: 'Кіріс пен мақұлдау',
      approvalRateLabel: 'Мақұлдау пайызы',
      approvalPercent: 'Мақұлдау пайызы',
      submitted: 'Тапсырылды',
      loadingMessage: 'Статистика жүктелуде...',
      totalApplications: 'Барлық өтінімдер',
      total: 'Барлығы',
      rejected: 'Қабылданбады',
      income: 'Кіріс',
      currency: 'теңге',
      statusDistribution: 'Мәртебе бойынша бөлу',
      topProfessions: 'Топ мамандықтар',
      ageDistribution: 'Жас бойынша бөлу',
      genderStats: 'Жыныс бойынша статистика',
      countryOverview: 'Елдер бойынша шолу',
      countryStats: 'Ел бойынша статистика',
      rejectionReasons: 'Бас тарту себептері',
      submissionDynamics: 'Өтініш беру динамикасы',
      months: {
        'янв.': 'Қаң',
        'февр.': 'Ақп',
        'март': 'Нау',
        'апр.': 'Сәу',
        'май': 'Мам',
        'июнь': 'Мау',
        'июль': 'Шіл',
        'авг.': 'Там',
        'сент.': 'Қыр',
        'окт.': 'Қаз',
        'нояб.': 'Қар',
        'дек.': 'Жел'
      }
    },
    visaStatus: {
      pending: 'Күтуде',
      submitted: 'Тапсырылды',
      approved: 'Мақұлданды',
      rejected: 'Қабылданбады',
      unknown: 'Белгісіз'
    },
    rejectionReasons: {
      insufficientFinancialDocuments: 'Қаржылық құжаттар жеткіліксіз',
      incompleteDocuments: 'Толық емес құжаттар пакеті',
      doubtsAboutTravelPurpose: 'Сапар мақсаты туралы күмән',
      invitationProblems: 'Шақыру проблемалары',
      otherReasons: 'Басқа себептер'
    },
    incomeRanges: {
      low: '450,000 теңгеге дейін',
      medium: '450,000 - 1,350,000 теңге',
      mediumHigh: '1,350,000 - 2,250,000 теңге',
      high: '2,250,000 - 4,500,000 теңге',
      veryHigh: '4,500,000 теңгеден жоғары'
    },
    clients: {
      paidConsularFee: 'Консулдық алым төленді',
      notPaidConsularFee: 'Консулдық алым төленбеді',
      title: 'Клиенттерді басқару',
      selectCountry: 'Клиенттерді басқару үшін елді таңдаңыз',
      totalApplicationsLabel: 'Барлық өтінімдер',
      filteredApplicationsLabel: 'Сүзілген өтінімдер',
      helpButton: 'Көмек',
      addClient: 'Клиент қосу',
      addPaidClient: 'Төленген клиент қосу',
      editClient: 'Клиентті өңдеу',
      deleteClient: 'Клиентті жою',
      clientId: 'Клиент ID',
      firstName: 'Аты',
      lastName: 'Тегі',
      phone: 'Телефон',
      email: 'Email',
      age: 'Жасы',
      visaCountry: 'Виза елі',
      profession: 'Мамандығы',
      income: 'Кірісі (теңге)',
      maritalStatus: 'Отбасылық жағдайы',
      visaStatus: 'Виза мәртебесі',
      paymentStatus: 'Төлем мәртебесі',
      servicePackage: 'Қызмет пакеті',
      packagePrice: 'Пакет бағасы (теңге)',
      consularFee: 'Консулдық алым',
      requiresFixes: 'Түзету қажет',
      comment: 'Түсініктеме',
      currentStep: 'Прогресс кезеңі',
      createdDate: 'Жасалған күні',
      updatedDate: 'Жаңартылған күні',
      visaHistory: 'Виза тарихы',
      hasProperty: 'Мүлкі',
      hasSponsor: 'Демеушісі',
      paid: 'Төленді',
      unpaid: 'Төленбеді',
      paidService: 'төленді',
      unpaidService: 'төленбеді',
      hasVisa: 'Бар',
      noVisa: 'Жоқ',
      noDataForExport: 'Экспорт үшін деректер жоқ',
      exportSuccess: 'Деректер сәтті экспортталды',
      fileCreated: 'Файл түрлі-түсті кодтаумен жасалды',
      exportError: 'Деректерді экспорттау қатесі',
      tryAgain: 'Қайтадан көріңіз немесе әкімшіге хабарласыңыз',
      dataRefreshed: 'Деректер жаңартылды',
      duplicateCheckError: 'Қайталанулар тексеру қатесі',
      noDuplicatesFound: 'Қайталанулар табылмады',
      allRecordsUnique: 'Барлық жазбалар бірегей',
      duplicatesFound: 'Қайталанулар табылды',
      duplicatesSingular: '',
      duplicatesPlural: '',
      duplicatesRemoveConfirm: 'Жаңа жазбалар жойылады, түпнұсқа жазбалар қалады.',
      continueRemoval: 'Жоюды жалғастыру керек пе?',
      duplicatesRemoved: 'Қайталанулар жойылды',
      successfullyRemoved: 'Сәтті жойылды',
      removeDuplicatesError: 'Қайталануларды жою қатесі',
      operationError: 'Операция кезінде қате орын алды',
      clientsCount: 'клиенттер',
      clientsSingular: 'клиент',
      clientsPlural: '',
      quickActions: 'Жылдам әрекеттер',
      backToCountries: 'Елдерге қайту',
      showAllCountries: 'Барлық елдерді көрсету',
      boardView: 'Trello',
      tableView: 'Таблица',
      advancedFilter: 'Расширенный фильтр',
      help: 'Анықтама',
      unpaidClientsDescription: 'Қызметтер үшін төлем жасамаған барлық клиенттер (кез келген форма кезеңі)',
      phoneNotSpecified: 'Телефон көрсетілмеген',
      emailNotSpecified: 'Email көрсетілмеген',
      paidConsularFee: 'Консулдық алым төленді',
      notPaidConsularFee: 'Консулдық алым төленбеді',
      exportData: 'Деректерді экспорттау',
      refreshData: 'Деректерді жаңарту',
      removeDuplicates: 'Қайталануларды жою',
      filters: {
        name: 'Аты',
        searchByName: 'Аты бойынша іздеу',
        activity: 'Қызмет',
        profession: 'Мамандық',
        incomeFrom: 'Кіріс бастап',
        incomeTo: 'Кіріс дейін',
        minimum: 'Минимум',
        maximum: 'Максимум',
        visaHistory: 'Виза тарихы',
        all: 'Барлығы',
        hasVisaHistory: 'Бар',
        noVisaHistory: 'Жоқ',
        property: 'Мүлік',
        hasProperty: 'Бар',
        noProperty: 'Жоқ',
        maritalStatus: 'Отбасылық жағдай',
        married: 'Үйленген/Тұрмысқа шыққан',
        single: 'Бойдақ',
        divorced: 'Ажырасқан',
        widowed: 'Жесір',
        sponsor: 'Демеуші',
        hasSponsor: 'Бар',
        noSponsor: 'Жоқ',
        clearFilters: 'Сүзгілерді тазалау',
        advancedFilters: 'Кеңейтілген сүзгілер',
        resetFilters: 'Сүзгілерді қалпына келтіру'
      },
      modal: {
        dateNotSpecified: 'Күні көрсетілмеген',
        invalidDate: 'Қате күн',
        dateError: 'Күн қатесі',
        personalInformation: 'Жеке ақпарат',
        applicationStatus: 'Өтініш мәртебесі',
        agentInformation: 'Агент туралы ақпарат',
        save: 'Сақтау',
        cancel: 'Болдырмау',
        locked: 'Бұғатталған',
        loading: 'Жүктелуде...',
        required: 'Міндетті өріс',
        editStepRestriction: 'Прогресс кезеңі тек оқу үшін. Виза әкімшілері үшін өзгерту қол жетімсіз',
        stepEditRestricted: 'Супер әкімші үшін қадамды өзгерту қол жетімсіз',
        stepEditRestrictedInvitation: 'Қадамды өзгерту тек шақыру болған кезде ғана қол жетімді',
        servicePackageEditRestricted: 'Супер әкімші үшін қызмет пакетін өзгерту қол жетімсіз. Тек виза компаниялары қызмет пакеттерін өзгерте алады',
        invitationUpload: 'Шақыру жүктеу',
        phoneAlreadyExists: 'Осы телефон нөірі бар клиент бұрыннан бар',
        phoneExistsDetails: '{phone} нөмірі жүйеде тіркелген (Клиент: {clientName})',
        phoneNumberTaken: 'Бұл телефон нөмірі қолданылып жатыр',
        chooseAnotherPhone: 'Басқа телефон нөмірін таңдаңыз',
        visaStatuses: {
          pending: 'күтуде',
          submitted: 'тапсырылды',
          approved: 'мақұлданды',
          rejected: 'қабылданбады'
        },
        paymentStatuses: {
          unpaid: 'төленбеген',
          paid: 'төленген',
          partial: 'жартылай төленген'
        },
        stepStatuses: {
          1: 'Бастапқы кезең',
          2: 'Сауалнама толтырылды',
          3: 'Форма толтырылды',
          4: 'Құжаттарды тексеру',
          5: 'Іс дайындау',
          6: 'Шақыру күту',
          7: 'Іс үйлестіру',
          8: 'Форма толтыру',
          9: 'Құжат тапсыру',
          10: 'Аяқталды'
        }
      },
      help: {
        title: 'Анықтамалық ақпарат',
        subtitle: 'Әкімші панелін пайдалану нұсқаулығы',
        close: 'Жабу',
        mainFunctions: {
          title: 'Негізгі функциялар',
          dashboard: 'Басты - жалпы статистика және маңызды хабарландырулар',
          clients: 'Клиенттер - елдер бойынша клиент өтініштерін басқару',
          statistics: 'Статистика - кезеңдер бойынша толық аналитика',
          priceList: 'Баға тізімі - қызмет пакеттері мен бағаларды басқару'
        },
        clientWork: {
          title: 'Клиенттермен жұмыс',
          addClient: 'Клиент қосу - автоматты сілтеме жіберумен жаңа өтініш жасау',
          filters: 'Сүзгілер - әртүрлі критерийлер бойынша клиенттерді іздеу',
          processStages: 'Процесс кезеңдері - әр өтініштің прогресін бақылау',
          invitations: 'Шақыру жүктеу - клиент құжаттарын басқару'
        },
        applicationStatuses: {
          title: 'Өтініш мәртебелері',
          pendingPayment: 'Төлем күтілуде - сауалнаманы толтырған клиенттер',
          requiresFixes: 'Түзету қажет - құжаттарда қателері бар өтініштер',
          waitingInvitation: 'Шақыру күтілуде - шақыру алу кезеңіндегі өтініштер',
          submitted: 'Тапсырылды - консулдыққа тапсырылған өтініштер'
        },
        exportReports: {
          title: 'Экспорт және есептер',
          excelExport: 'Excel экспорты - деректерді CSV форматында сақтау',
          filterExport: 'Экспорт алдында сүзгілеу - тек қажетті деректерді шығару',
          periodStatistics: 'Кезең статистикасы - таңдалған күндер үшін есептер',
          countryAnalytics: 'Елдер бойынша аналитика - бағыттар бойынша бөлу'
        },
        hotkeys: {
          title: 'Жылдам пернелер',
          escape: 'ESC - модалды терезелерді жабу',
          search: 'Ctrl/Cmd + F - бетте іздеу',
          refresh: 'F5 - деректерді жаңарту',
          navigation: 'Tab - элементтер арасында навигация'
        },
        rolesAccess: {
          title: 'Рөлдер және қол жеткізу құқықтары',
          superAdmin: 'Супер Әкімші - барлық функцияларға толық қол жеткізу',
          admin: 'Әкімші - клиенттер мен статистиканы басқару',
          manager: 'Менеджер - компания шеңберіндегі клиенттермен жұмыс',
          stageRestrictions: 'Кезең шектеулері - кезеңдерді тек шақыру болған кезде өзгерту'
        },
        importantInfo: {
          title: 'Маңызды ақпарат',
          security: {
            title: '🔐 Қауіпсіздік',
            description: 'Жұмысты аяқтағаннан кейін әрқашан жүйеден шығыңыз. Тіркелгі деректеріңізді үшінші тұлғаларға бермеңіз.'
          },
          backup: {
            title: '💾 Сақтық көшірме',
            description: 'Маңызды ақпараттың сақтық көшірмелерін жасау үшін деректерді тұрақты экспорттаңыз.'
          },
          support: {
            title: '📞 Қолдау',
            description: 'Мәселелер туындаған жағдайда жүйенің техникалық әкімшісіне хабарласыңыз.'
          },
          updates: {
            title: '🔄 Жаңартулар',
            description: 'Жүйе тұрақты жаңартылады. Жаңа функциялар туралы хабарландыруларды қадағалаңыз.'
          }
        },
        footer: 'Виза өтініштерін басқару жүйесі'
      }
    },
    companies: {
      title: 'Компанияларды басқару',
      addCompany: 'Компания қосу',
      editCompany: 'Компанияны өңдеу',
      deleteCompany: 'Компанияны жою',
      description: 'Виза компанияларын және олардың әкімшілерін басқару'
    },
    countries: {
      US: 'АҚШ',
      USA: 'АҚШ',
      UK: 'Ұлыбритания',
      EU: 'Шенген аймағы',
      Schengen: 'Шенген',
      CN: 'Қытай',
      China: 'Қытай',
      Canada: 'Канада',
      Australia: 'Австралия',
      Germany: 'Германия',
      France: 'Франция',
      Italy: 'Италия',
      Spain: 'Испания',
      Netherlands: 'Нидерландия',
      Belgium: 'Бельгия',
      Austria: 'Австрия',
      Switzerland: 'Швейцария',
      Portugal: 'Португалия',
      Poland: 'Польша',
      'Czech Republic': 'Чехия',
      Hungary: 'Венгрия',
      Greece: 'Грекия',
      Sweden: 'Швеция',
      Norway: 'Норвегия',
      Denmark: 'Дания',
      Finland: 'Финляндия',
      Japan: 'Жапония',
      'South Korea': 'Оңтүстік Корея',
      Singapore: 'Сингапур',
      'New Zealand': 'Жаңа Зеландия',
      Brazil: 'Бразилия',
      Mexico: 'Мексика',
      India: 'Үндістан',
      Thailand: 'Тайланд',
      UAE: 'БАӘ',
      Turkey: 'Түркия',
      Israel: 'Израиль',
      'South Africa': 'Оңтүстік Африка'
    },
    employees: {
      title: 'Қызметкерлерді басқару',
      description: 'Компания қызметкерлерін басқару',
      addManager: 'Менеджер қосу'
    }
  }
};

// Helper function to get nested translation
function getNestedTranslation(obj: Translations, path: string): string {
  const keys = path.split('.');
  let current: any = obj;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return path; // Return the key if translation not found
    }
  }
  
  return typeof current === 'string' ? current : path;
}

// Translation hook
export function useTranslation() {
  const router = useRouter();
  const currentLanguage = (router.locale || 'ru') as Language;
  
  const t = (key: string): string => {
    const translation = getNestedTranslation(translations[currentLanguage], key);
    return translation;
  };
  
  return { t, currentLanguage };
}

// Language switching hook
export function useLanguage() {
  const router = useRouter();
  const currentLanguage = (router.locale || 'ru') as Language;
  
  const changeLanguage = async (locale: Language) => {
    try {
      await router.push(router.asPath, router.asPath, { locale });
    } catch (error) {
      console.error('Language change error:', error);
      // Fallback: manual URL construction
      const currentPath = router.asPath;
      let newPath = currentPath;
      
      // Remove existing locale prefix
      if (currentPath.startsWith('/en/') || currentPath.startsWith('/kz/')) {
        newPath = currentPath.substring(3);
      }
      
      // Add new locale prefix (except for Russian default)
      if (locale !== 'ru') {
        newPath = `/${locale}${newPath}`;
      }
      
      window.location.href = newPath;
    }
  };
  
  return {
    currentLanguage,
    changeLanguage,
    availableLanguages: ['ru', 'en', 'kz'] as Language[],
    languageNames: {
      ru: 'Русский',
      en: 'English',
      kz: 'Қазақша'
    }
  };
}

// Context for language (if needed)
const LanguageContext = createContext<{
  currentLanguage: Language;
  changeLanguage: (lang: Language) => void;
}>({
  currentLanguage: 'ru',
  changeLanguage: () => {}
});

export const useLanguageContext = () => useContext(LanguageContext); 