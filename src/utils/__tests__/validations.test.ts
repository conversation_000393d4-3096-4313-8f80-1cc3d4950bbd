/**
 * Tests for the centralized validation system
 * These tests ensure that the new validation system works correctly
 * and maintains backward compatibility
 */

import { getValidationSchemaForStep } from '../validations';
import { validateMultipleSteps, getStepFieldNames, useStepValidation } from '../validationHelpers';

describe('Centralized Validation System', () => {
  describe('Schema Generation', () => {
    test('should generate schema for step 1', () => {
      const schema = getValidationSchemaForStep(1);
      expect(schema).toBeDefined();
      expect(schema.fields).toHaveProperty('visaDestination');
      expect(schema.fields).toHaveProperty('otherVisaDestination');
    });

    test('should generate schema for step 2 with cross-field validation', () => {
      const schema = getValidationSchemaForStep(2);
      expect(schema).toBeDefined();
      expect(schema.fields).toHaveProperty('surname');
      expect(schema.fields).toHaveProperty('name');
      expect(schema.fields).toHaveProperty('passportNumber');
      expect(schema.fields).toHaveProperty('iin');
      expect(schema.fields).toHaveProperty('idNumber');
    });

    test('should generate different schemas for different steps', () => {
      const step1Schema = getValidationSchemaForStep(1);
      const step2Schema = getValidationSchemaForStep(2);
      
      expect(step1Schema.fields).not.toEqual(step2Schema.fields);
    });
  });

  describe('Field Names', () => {
    test('should return correct field names for step 1', () => {
      const fieldNames = getStepFieldNames(1);
      expect(fieldNames).toEqual(['visaDestination', 'otherVisaDestination']);
    });

    test('should return correct field names for step 2', () => {
      const fieldNames = getStepFieldNames(2);
      expect(fieldNames).toContain('surname');
      expect(fieldNames).toContain('name');
      expect(fieldNames).toContain('dateOfBirth');
      expect(fieldNames).toContain('passportNumber');
      expect(fieldNames).toContain('iin');
      expect(fieldNames).toContain('idNumber');
    });

    test('should return empty array for invalid step', () => {
      const fieldNames = getStepFieldNames(999 as any);
      expect(fieldNames).toEqual([]);
    });
  });

  describe('Validation Logic', () => {
    test('should validate step 1 correctly', async () => {
      const schema = getValidationSchemaForStep(1);
      
      // Valid data
      const validData = {
        visaDestination: 'usa'
      };
      await expect(schema.validate(validData)).resolves.toEqual(validData);

      // Invalid data - missing required field
      const invalidData = {};
      await expect(schema.validate(invalidData)).rejects.toThrow();

      // Conditional validation - other country
      const otherCountryData = {
        visaDestination: 'other',
        otherVisaDestination: 'Germany'
      };
      await expect(schema.validate(otherCountryData)).resolves.toEqual(otherCountryData);

      // Missing conditional field
      const missingConditionalData = {
        visaDestination: 'other'
      };
      await expect(schema.validate(missingConditionalData)).rejects.toThrow();
    });

    test('should validate step 2 cross-field validation', async () => {
      const schema = getValidationSchemaForStep(2);
      
      // Valid data with passport
      const validDataWithPassport = {
        surname: 'Ivanov',
        name: 'Ivan',
        dateOfBirth: '1990-01-01',
        citizenship: 'Kazakhstan',
        passportNumber: 'N12345678',
        passportIssueDate: '2020-01-01',
        passportExpiryDate: '2030-01-01'
      };
      await expect(schema.validate(validDataWithPassport)).resolves.toBeDefined();

      // Valid data with IIN
      const validDataWithIIN = {
        surname: 'Ivanov',
        name: 'Ivan', 
        dateOfBirth: '1990-01-01',
        citizenship: 'Kazakhstan',
        iin: '123456789012'
      };
      await expect(schema.validate(validDataWithIIN)).resolves.toBeDefined();

      // Invalid data - no identifier
      const invalidDataNoId = {
        surname: 'Ivanov',
        name: 'Ivan',
        dateOfBirth: '1990-01-01',
        citizenship: 'Kazakhstan'
      };
      await expect(schema.validate(invalidDataNoId)).rejects.toThrow();
    });

    test('should validate phone format in step 6', async () => {
      const schema = getValidationSchemaForStep(6);
      
      // Valid data
      const validData = {
        address: '123 Main St',
        city: 'Almaty',
        country: 'Kazakhstan',
        zipCode: '050000',
        phone: '****** 123 45 67'
      };
      await expect(schema.validate(validData)).resolves.toBeDefined();

      // Invalid phone format
      const invalidPhoneData = {
        ...validData,
        phone: '1234567890'
      };
      await expect(schema.validate(invalidPhoneData)).rejects.toThrow();
    });
  });

  describe('Multi-Step Validation', () => {
    test('should validate multiple steps correctly', async () => {
      const data = {
        // Step 1 data
        visaDestination: 'usa',
        
        // Step 2 data
        surname: 'Ivanov',
        name: 'Ivan',
        dateOfBirth: '1990-01-01',
        citizenship: 'Kazakhstan',
        iin: '123456789012'
      };

      const result = await validateMultipleSteps(data, [1, 2]);
      expect(result.isValid).toBe(true);
      expect(result.errors[1]).toBeNull();
      expect(result.errors[2]).toBeNull();
    });

    test('should detect errors in multi-step validation', async () => {
      const data = {
        // Missing step 1 required field
        // Missing step 2 required fields
        surname: 'Invalid123' // Invalid format
      };

      const result = await validateMultipleSteps(data, [1, 2]);
      expect(result.isValid).toBe(false);
      expect(result.errors[1]).toBeDefined();
      expect(result.errors[2]).toBeDefined();
    });
  });

  describe('Backward Compatibility', () => {
    test('should maintain step1Schema export compatibility', () => {
      const { step1Schema } = require('../validations');
      expect(step1Schema).toBeDefined();
      expect(step1Schema.fields).toHaveProperty('visaDestination');
    });

    test('should maintain step2Schema export compatibility', () => {
      const { step2Schema } = require('../validations');
      expect(step2Schema).toBeDefined();
      expect(step2Schema.fields).toHaveProperty('surname');
    });

    test('should maintain all step schemas', () => {
      const {
        step1Schema, step2Schema, step3Schema, step4Schema, step5Schema,
        step6Schema, step7Schema, step8Schema, step9Schema
      } = require('../validations');

      [step1Schema, step2Schema, step3Schema, step4Schema, step5Schema,
       step6Schema, step7Schema, step8Schema, step9Schema].forEach((schema, index) => {
        expect(schema).toBeDefined();
        expect(typeof schema.validate).toBe('function');
      });
    });
  });

  describe('Performance', () => {
    test('should generate schemas efficiently', () => {
      const startTime = Date.now();
      
      // Generate all schemas
      for (let i = 1; i <= 9; i++) {
        getValidationSchemaForStep(i);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should generate all schemas in less than 100ms
      expect(duration).toBeLessThan(100);
    });

    test('should cache schema generation', () => {
      const schema1 = getValidationSchemaForStep(1);
      const schema2 = getValidationSchemaForStep(1);
      
      // Should return the same schema object (if caching is implemented)
      // For now, just test that they have the same structure
      expect(schema1.fields).toEqual(schema2.fields);
    });
  });
});

describe('Validation Helpers', () => {
  describe('useStepValidation', () => {
    test('should return validation utilities for a step', () => {
      const stepValidation = useStepValidation(1);
      
      expect(stepValidation.schema).toBeDefined();
      expect(stepValidation.fieldNames).toEqual(['visaDestination', 'otherVisaDestination']);
      expect(typeof stepValidation.validateFields).toBe('function');
    });

    test('should validate fields correctly', async () => {
      const stepValidation = useStepValidation(1);
      
      const validData = { visaDestination: 'usa' };
      const result = await stepValidation.validateFields(validData);
      expect(result.isValid).toBe(true);

      const invalidData = {};
      const invalidResult = await stepValidation.validateFields(invalidData);
      expect(invalidResult.isValid).toBe(false);
    });
  });
});