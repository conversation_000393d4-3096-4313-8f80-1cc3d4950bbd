import { normalizePhoneNumber } from '../validations';

describe('normalizePhoneNumber', () => {
  test('should normalize Kazakhstan phone numbers correctly', () => {
    // Test cases with different input formats
    const testCases = [
      // Standard Kazakhstan format with +7
      { input: '****** 234 56 78', expected: '+77012345678' },
      { input: '+77012345678', expected: '+77012345678' },
      { input: '+7 (701) 234-56-78', expected: '+77012345678' },

      // Kazakhstan format starting with 8
      { input: '8 701 234 56 78', expected: '+77012345678' },
      { input: '87012345678', expected: '+77012345678' },
      { input: '8 (701) 234-56-78', expected: '+77012345678' },

      // Kazakhstan format without country code
      { input: '701 234 56 78', expected: '+77012345678' },
      { input: '7012345678', expected: '+77012345678' },
      { input: '(701) 234-56-78', expected: '+77012345678' },

      // Already normalized format
      { input: '+77012345678', expected: '+77012345678' },

      // Edge cases
      { input: '', expected: '' },
      { input: '****** 234 56 78 99', expected: '+77012345678' }, // Too many digits
      { input: '+7701', expected: '+77701' }, // Too few digits (should still work)
    ];

    testCases.forEach(({ input, expected }) => {
      expect(normalizePhoneNumber(input)).toBe(expected);
    });
  });

  test('should handle null and undefined inputs', () => {
    expect(normalizePhoneNumber('')).toBe('');
    // TypeScript should prevent null/undefined, but test runtime behavior
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    expect(normalizePhoneNumber(null as any)).toBe('');
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    expect(normalizePhoneNumber(undefined as any)).toBe('');
  });

  test('should handle international numbers by adding Kazakhstan prefix', () => {
    // Numbers without country code should get +7 prefix
    expect(normalizePhoneNumber('1234567890')).toBe('+71234567890');
    expect(normalizePhoneNumber('9876543210')).toBe('+79876543210');
  });

  test('should preserve 7 prefix for Kazakhstan numbers', () => {
    expect(normalizePhoneNumber('77012345678')).toBe('+77012345678');
    expect(normalizePhoneNumber('7 701 234 56 78')).toBe('+77012345678');
  });

  test('should handle various formatting characters', () => {
    const messyInput = '+7 (701) 234-56-78 ext. 123';
    expect(normalizePhoneNumber(messyInput)).toBe('+77012345678');
    
    const withSpaces = '  +7   701   234   56   78  ';
    expect(normalizePhoneNumber(withSpaces)).toBe('+77012345678');
    
    const withDots = '******.234.56.78';
    expect(normalizePhoneNumber(withDots)).toBe('+77012345678');
  });
});
