import { v4 as uuidv4 } from 'uuid';
import { supabase } from './supabase';
import * as fs from 'fs';
import * as path from 'path';
import { uploadTempFile } from './file-storage';
import { ParsedNameData, TempFileInfo } from '../types/ocr-common';
import { COUNTRIES } from '../constants/countries';

// OpenRouter Vision OCR Module

// Default configuration
const DEFAULT_CONFIG = {
  baseUrl: 'https://openrouter.ai/api/v1/chat/completions',
  model: 'anthropic/claude-3.5-sonnet', // Using Claude 3.5 Sonnet (proven working model)
  maxTokens: 2000,
  timeout: 60000,
};

// Available model options (uncomment to try different models)
// const AVAILABLE_MODELS = [
//   'anthropic/claude-3.5-sonnet',      // Most stable
//   'anthropic/claude-3-opus',          // Highest quality
//   'anthropic/claude-3-sonnet',        // Good balance
//   'anthropic/claude-3.5-haiku',       // Fastest
//   'anthropic/claude-3.7-sonnet'       // Newer version
// ];

// OCR Engine Configuration
const USE_OPENROUTER_OCR = process.env.NEXT_OR_KEY && process.env.NEXT_OR_KEY.length > 0;

// Optimized prompts for passport OCR
const PASSPORT_OCR_PROMPT = `
Проанализируй это изображение паспорта или ID документа и извлеки следующие данные.
Верни результат строго в формате JSON без дополнительных комментариев:

{
  "name": "имя ТОЛЬКО в латинице (если найдено)",
  "surname": "фамилия ТОЛЬКО в латинице (если найдено)", 
  "dateOfBirth": "дата рождения в формате DD.MM.YYYY",
  "passportIssueDate": "дата выдачи в формате DD.MM.YYYY",
  "passportExpiryDate": "дата окончания в формате DD.MM.YYYY",
  "passportNumber": "номер паспорта",
  "nationality": "национальность/гражданство"
}

КРИТИЧЕСКИ ВАЖНО для имени и фамилии:
- Имя и фамилия ДОЛЖНЫ быть в латинице (A-Z, a-z)
- Если в документе есть латинские имена - используй их как есть
- Если имена только на кириллице - транслитерируй их согласно международным стандартам:
  * А→A, Б→B, В→V, Г→G, Д→D, Е→E, Ё→YO, Ж→ZH, З→Z, И→I, Й→Y, К→K, Л→L, М→M, Н→N, О→O, П→P, Р→R, С→S, Т→T, У→U, Ф→F, Х→KH, Ц→TS, Ч→CH, Ш→SH, Щ→SHCH, Ъ→IE, Ы→Y, Ь→, Э→E, Ю→YU, Я→YA
  * Қ→Q, Ә→AE, Ң→NG, Ғ→GH, Ү→UE, Ұ→U, Һ→H, Ө→OE, І→I
- Примеры: Иванов → IVANOV, Айжан → AIZHAN, Мұхтар → MUKHTAR

Общие правила:
- Если данные не найдены, используй null для этого поля
- Даты всегда в формате DD.MM.YYYY
- Ищи данные в MRZ (машиночитаемая зона) и в основном тексте
- Поддерживай русский, казахский, английский языки
- Будь точен и не придумывай данные
- Имя и фамилия ОБЯЗАТЕЛЬНО в латинице!
`;

interface OpenRouterConfig {
  apiKey: string;
  baseUrl: string;
  model: string;
  maxTokens: number;
  timeout: number;
}

interface OpenRouterMessage {
  role: 'user' | 'system' | 'assistant';
  content: Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

interface OpenRouterRequest {
  model: string;
  max_tokens: number;
  messages: OpenRouterMessage[];
}

interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenRouterProcessResult {
  success: boolean;
  extractedData?: ParsedNameData;
  rawText: string;
  error?: string;
  tokensUsed?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenRouterOcrStatus {
  available: boolean;
  hasApiKey: boolean;
  model: string | undefined;
}

/**
 * Convert date from DD.MM.YYYY to YYYY-MM-DD format
 */
const convertDateFormat = (dateStr: string): string => {
  if (!dateStr) return '';
  
  // Check if it's already in YYYY-MM-DD format
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr;
  }
  
  // Convert DD.MM.YYYY to YYYY-MM-DD
  if (/^\d{2}\.\d{2}\.\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('.');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Handle DD/MM/YYYY format
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('/');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Handle DD-MM-YYYY format
  if (/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('-');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  console.warn('Unknown date format:', dateStr);
  return dateStr; // Return as-is if we can't parse it
};

/**
 * Transliterate Cyrillic names to Latin according to international standards
 */
const transliterateName = (name: string): string => {
  if (!name) return '';
  
  // If already in Latin, return as is
  if (/^[A-Za-z\s\-']+$/.test(name)) {
    return name.toUpperCase();
  }
  
  // Transliteration map for Russian and Kazakh
  const transliterationMap: { [key: string]: string } = {
    // Russian alphabet
    'А': 'A', 'а': 'A',
    'Б': 'B', 'б': 'B',
    'В': 'V', 'в': 'V',
    'Г': 'G', 'г': 'G',
    'Д': 'D', 'д': 'D',
    'Е': 'E', 'е': 'E',
    'Ё': 'YO', 'ё': 'YO',
    'Ж': 'ZH', 'ж': 'ZH',
    'З': 'Z', 'з': 'Z',
    'И': 'I', 'и': 'I',
    'Й': 'Y', 'й': 'Y',
    'К': 'K', 'к': 'K',
    'Л': 'L', 'л': 'L',
    'М': 'M', 'м': 'M',
    'Н': 'N', 'н': 'N',
    'О': 'O', 'о': 'O',
    'П': 'P', 'п': 'P',
    'Р': 'R', 'р': 'R',
    'С': 'S', 'с': 'S',
    'Т': 'T', 'т': 'T',
    'У': 'U', 'у': 'U',
    'Ф': 'F', 'ф': 'F',
    'Х': 'KH', 'х': 'KH',
    'Ц': 'TS', 'ц': 'TS',
    'Ч': 'CH', 'ч': 'CH',
    'Ш': 'SH', 'ш': 'SH',
    'Щ': 'SHCH', 'щ': 'SHCH',
    'Ъ': 'IE', 'ъ': 'IE',
    'Ы': 'Y', 'ы': 'Y',
    'Ь': '', 'ь': '',
    'Э': 'E', 'э': 'E',
    'Ю': 'YU', 'ю': 'YU',
    'Я': 'YA', 'я': 'YA',
    
    // Kazakh specific letters
    'Ә': 'AE', 'ә': 'AE',
    'Қ': 'Q', 'қ': 'Q',
    'Ң': 'NG', 'ң': 'NG',
    'Ғ': 'GH', 'ғ': 'GH',
    'Ү': 'UE', 'ү': 'UE',
    'Ұ': 'U', 'ұ': 'U',
    'Һ': 'H', 'һ': 'H',
    'Ө': 'OE', 'ө': 'OE',
    'І': 'I', 'і': 'I',
    
    // Common punctuation and spaces
    ' ': ' ',
    '-': '-',
    "'": "'",
    '"': '"'
  };
  
  let result = '';
  for (let i = 0; i < name.length; i++) {
    const char = name[i];
    if (transliterationMap[char] !== undefined) {
      result += transliterationMap[char];
    } else if (/[A-Za-z]/.test(char)) {
      // Already Latin
      result += char.toUpperCase();
    } else {
      // Unknown character, keep as is
      result += char;
    }
  }
  
  // Clean up multiple spaces and trim
  result = result.replace(/\s+/g, ' ').trim();
  
  console.log(`Transliterated name: "${name}" → "${result}"`);
  return result;
};

/**
 * Map nationality/citizenship from Cyrillic to country list
 */
const mapNationalityToCitizenship = (nationality: string): string => {
  if (!nationality) return '';
  
  // Create mapping from common nationality terms to countries
  const nationalityMap: { [key: string]: string } = {
    'казахстанская': 'Казахстан',
    'казахстанец': 'Казахстан',
    'казахстанка': 'Казахстан',
    'казахская': 'Казахстан',
    'казах': 'Казахстан',
    'kaz': 'Казахстан',
    'казахстан': 'Казахстан',
    
    'российская': 'Россия',
    'русская': 'Россия',
    'русский': 'Россия',
    'российский': 'Россия',
    'россиянин': 'Россия',
    'россиянка': 'Россия',
    'rus': 'Россия',
    'россия': 'Россия',
    
    'американская': 'США',
    'американец': 'США',
    'американка': 'США',
    'usa': 'США',
    'united states': 'США',
    'сша': 'США',
    
    'немецкая': 'Германия',
    'немец': 'Германия',
    'немка': 'Германия',
    'german': 'Германия',
    'ger': 'Германия',
    'германия': 'Германия',
    
    'французская': 'Франция',
    'француз': 'Франция',
    'французженка': 'Франция',
    'french': 'Франция',
    'fra': 'Франция',
    'франция': 'Франция',
    
    'британская': 'Великобритания',
    'английская': 'Великобритания',
    'англичанин': 'Великобритания',
    'англичанка': 'Великобритания',
    'british': 'Великобритания',
    'uk': 'Великобритания',
    'gbr': 'Великобритания',
    'великобритания': 'Великобритания',
    
    'китайская': 'Китай',
    'китаец': 'Китай',
    'китаянка': 'Китай',
    'chinese': 'Китай',
    'chn': 'Китай',
    'китай': 'Китай',
    
    'узбекская': 'Узбекистан',
    'узбек': 'Узбекистан',
    'узбечка': 'Узбекистан',
    'uzb': 'Узбекистан',
    'узбекистан': 'Узбекистан',
    
    'кыргызская': 'Кыргызстан',
    'кыргыз': 'Кыргызстан',
    'кыргызка': 'Кыргызстан',
    'kgz': 'Кыргызстан',
    'кыргызстан': 'Кыргызстан',
    
    'таджикская': 'Таджикистан',
    'таджик': 'Таджикистан',
    'таджичка': 'Таджикистан',
    'tjk': 'Таджикистан',
    'таджикистан': 'Таджикистан',
    
    'турецкая': 'Турция',
    'турок': 'Турция',
    'турчанка': 'Турция',
    'turkish': 'Турция',
    'tur': 'Турция',
    'турция': 'Турция'
  };
  
  const normalized = nationality.toLowerCase().trim();
  
  // Try direct mapping first
  if (nationalityMap[normalized]) {
    console.log(`Mapped nationality "${nationality}" to "${nationalityMap[normalized]}"`);
    return nationalityMap[normalized];
  }
  
  // Try partial matching
  for (const [key, value] of Object.entries(nationalityMap)) {
    if (normalized.includes(key) || key.includes(normalized)) {
      console.log(`Partially mapped nationality "${nationality}" to "${value}" via "${key}"`);
      return value;
    }
  }
  
  // If no mapping found, check if it's already in the countries list
  const foundCountry = COUNTRIES.find(country => 
    country.toLowerCase() === normalized || 
    normalized.includes(country.toLowerCase())
  );
  
  if (foundCountry) {
    console.log(`Found nationality "${nationality}" in countries list as "${foundCountry}"`);
    return foundCountry;
  }
  
  console.warn(`Could not map nationality "${nationality}" to any country`);
  return nationality; // Return as-is if we can't map it
};

/**
 * Get OpenRouter configuration from environment
 */
const getOpenRouterConfig = (): OpenRouterConfig => {
  const apiKey = process.env.NEXT_OR_KEY;
  
  if (!apiKey) {
    throw new Error('NEXT_OR_KEY environment variable is required');
  }

  // Allow model to be configured via environment variable
  const model = process.env.NEXT_OR_MODEL || DEFAULT_CONFIG.model;
  
  return {
    apiKey,
    model,
    ...DEFAULT_CONFIG
  };
};

/**
 * Check OpenRouter credits
 */
const checkOpenRouterCredits = async (apiKey: string): Promise<any> => {
  try {
    console.log('🔍 Checking OpenRouter credits...');
    
    const response = await fetch('https://openrouter.ai/api/v1/credits', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      }
    });

    const creditsData = await response.json();

    if (!response.ok) {
      console.error('❌ Failed to check credits:', creditsData);
      throw new Error(`Failed to check credits: ${creditsData.error?.message || 'Unknown error'}`);
    }

    console.log('💰 OpenRouter Credits:', creditsData);
    
    // Check if credits are low
    if (creditsData.balance && creditsData.balance < 0.1) {
      console.warn('⚠️ WARNING: OpenRouter credits are running low!');
      console.warn('💰 Current balance:', creditsData.balance);
    }

    return creditsData;
  } catch (error) {
    console.error('❌ Error checking OpenRouter credits:', error);
    throw error;
  }
};

/**
 * Make a request to OpenRouter API
 */
const callOpenRouterApi = async (
  request: OpenRouterRequest,
  config: OpenRouterConfig
): Promise<OpenRouterResponse> => {
  try {
    console.log('🚀 Making request to OpenRouter API...');
    console.log('📋 Request details:', {
      model: request.model,
      max_tokens: request.max_tokens,
      messages_count: request.messages.length,
      content_types: request.messages[0]?.content?.map(c => c.type) || []
    });

    // Check credits before making the request
    try {
      await checkOpenRouterCredits(config.apiKey);
    } catch (creditsError) {
      console.warn('⚠️ Could not check credits, proceeding anyway:', creditsError);
    }

    const postData = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        'HTTP-Referer': 'https://visaai.vercel.app', // Optional: your app URL
        'X-Title': 'VisaAI OCR', // Optional: your app name
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(config.timeout)
    };

    console.log('🔄 Sending request to:', config.baseUrl);
    const response = await fetch(config.baseUrl, postData);

    let responseData;
    try {
      responseData = await response.json();
    } catch (jsonError) {
      console.error('❌ Failed to parse response as JSON:', jsonError);
      console.error('📄 Raw response:', await response.text());
      throw new Error('Invalid JSON response from OpenRouter API');
    }

    console.log('📥 Response received:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      ok: response.ok
    });

    if (!response.ok) {
      console.error('❌ OpenRouter API error details:', {
        status: response.status,
        statusText: response.statusText,
        error: responseData?.error,
        full_response: responseData
      });
      
      // Detailed error handling
      if (response.status === 401) {
        throw new Error('🔐 OpenRouter API authentication failed. Please check your NEXT_OR_KEY.');
      } else if (response.status === 400) {
        const errorMsg = responseData.error?.message || 'Invalid request';
        console.error('🔍 Bad request details:', {
          error_message: errorMsg,
          error_code: responseData.error?.code,
          request_model: request.model,
          request_body: JSON.stringify(request, null, 2)
        });
        throw new Error(`🚫 OpenRouter API bad request: ${errorMsg}`);
      } else if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        console.error('⏳ Rate limit details:', {
          retry_after: retryAfter,
          error: responseData.error
        });
        throw new Error(`⏳ OpenRouter API rate limit exceeded. Retry after: ${retryAfter || 'unknown'}`);
      } else if (response.status === 500) {
        console.error('🏥 OpenRouter Internal Server Error:', {
          error: responseData.error,
          request_id: response.headers.get('x-request-id'),
          timestamp: new Date().toISOString()
        });
        throw new Error(`🏥 OpenRouter Internal Server Error. This is a temporary issue with OpenRouter. Please try again later.`);
      } else if (response.status === 503) {
        console.error('🚧 OpenRouter Service Unavailable:', {
          error: responseData.error,
          retry_after: response.headers.get('Retry-After')
        });
        throw new Error(`🚧 OpenRouter service is temporarily unavailable. Please try again later.`);
      } else {
        console.error('❓ Unexpected OpenRouter API error:', {
          status: response.status,
          error: responseData.error,
          full_response: responseData
        });
        throw new Error(`❓ OpenRouter API error (${response.status}): ${responseData.error?.message || 'Unknown error'}`);
      }
    }

    console.log('✅ OpenRouter API response received successfully');
    console.log('📊 Usage statistics:', responseData.usage);
    console.log('🤖 Model used:', responseData.model);
    
    return responseData as OpenRouterResponse;

  } catch (error) {
    console.error('💥 Error calling OpenRouter API:', error);
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('⏰ OpenRouter API request timed out after ' + config.timeout + 'ms');
      }
      throw error;
    }
    throw new Error('❓ Unknown error calling OpenRouter API');
  }
};

/**
 * Parse JSON response from OpenRouter
 */
const parseOpenRouterResponse = (response: OpenRouterResponse): ParsedNameData => {
  try {
    const content = response.choices[0]?.message?.content || '';
    console.log('Raw OpenRouter response:', content);
    
    // Try to extract JSON from response
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      // If no JSON found, try to parse the whole response as JSON
      jsonMatch = [content];
    }
    
    const jsonData = JSON.parse(jsonMatch[0]);
    
    // Convert null values to undefined and ensure proper types
    const rawNationality = jsonData.nationality || undefined;
    const mappedCitizenship = rawNationality ? mapNationalityToCitizenship(rawNationality) : undefined;
    
    // Ensure names are in Latin (apply transliteration as fallback)
    const rawName = jsonData.name || undefined;
    const rawSurname = jsonData.surname || undefined;
    const transliteratedName = rawName ? transliterateName(rawName) : undefined;
    const transliteratedSurname = rawSurname ? transliterateName(rawSurname) : undefined;
    
    const parsedData: ParsedNameData = {
      name: transliteratedName,
      surname: transliteratedSurname,
      dateOfBirth: jsonData.dateOfBirth ? convertDateFormat(jsonData.dateOfBirth) : undefined,
      passportIssueDate: jsonData.passportIssueDate ? convertDateFormat(jsonData.passportIssueDate) : undefined,
      passportExpiryDate: jsonData.passportExpiryDate ? convertDateFormat(jsonData.passportExpiryDate) : undefined,
      passportNumber: jsonData.passportNumber || undefined,
      nationality: rawNationality,
      citizenship: mappedCitizenship,
      rawText: content,
      source: 'claude' as const
    };
    
    console.log('Parsed data from OpenRouter:', parsedData);
    console.log('Data conversions applied:', {
      'name': rawName ? `${rawName} -> ${transliteratedName}` : 'not converted',
      'surname': rawSurname ? `${rawSurname} -> ${transliteratedSurname}` : 'not converted',
      'dateOfBirth': jsonData.dateOfBirth ? `${jsonData.dateOfBirth} -> ${parsedData.dateOfBirth}` : 'not converted',
      'passportIssueDate': jsonData.passportIssueDate ? `${jsonData.passportIssueDate} -> ${parsedData.passportIssueDate}` : 'not converted',
      'passportExpiryDate': jsonData.passportExpiryDate ? `${jsonData.passportExpiryDate} -> ${parsedData.passportExpiryDate}` : 'not converted',
      'citizenship': rawNationality ? `${rawNationality} -> ${mappedCitizenship}` : 'not converted'
    });
    return parsedData;
  } catch (error) {
    console.error('Error parsing OpenRouter response:', error);
    // Return raw text if JSON parsing fails
    return {
      rawText: response.choices[0]?.message?.content || '',
      source: 'claude' as const
    };
  }
};

/**
 * Convert file to base64 for PDF processing
 */
const convertFileToBase64 = async (filePath: string): Promise<string> => {
  try {
    const fileBuffer = await fs.promises.readFile(filePath);
    const base64Data = fileBuffer.toString('base64');
    
    // Determine MIME type based on file extension
    const ext = path.extname(filePath).toLowerCase();
    let mimeType = 'application/octet-stream';
    
    if (ext === '.pdf') {
      mimeType = 'application/pdf';
    } else if (['.jpg', '.jpeg'].includes(ext)) {
      mimeType = 'image/jpeg';
    } else if (ext === '.png') {
      mimeType = 'image/png';
    } else if (ext === '.webp') {
      mimeType = 'image/webp';
    }
    
    return `data:${mimeType};base64,${base64Data}`;
  } catch (error) {
    console.error('Error converting file to base64:', error);
    throw error;
  }
};

/**
 * Process document with OpenRouter Vision API using temporary file upload
 */
export const processDocumentWithOpenRouter = async (
  filePath: string,
  fileType: 'image' | 'pdf',
  agentId: string
): Promise<OpenRouterProcessResult> => {
  let tempFile: TempFileInfo | null = null;

  try {
    console.log('=== PROCESSING DOCUMENT WITH OPENROUTER VISION ===');
    console.log('📄 File path:', filePath);
    console.log('📋 File type:', fileType);
    console.log('🆔 Agent ID:', agentId);

    // Get configuration
    const config = getOpenRouterConfig();

    let request: any;

    if (fileType === 'pdf') {
      console.log('📄 Processing PDF with base64 encoding and native plugin...');
      
      // For PDF, use base64 encoding with file type and native plugin
      const base64Data = await convertFileToBase64(filePath);
      
      request = {
        model: config.model,
        max_tokens: config.maxTokens,
        messages: [{
          role: 'user',
          content: [
            {
              type: 'text',
              text: PASSPORT_OCR_PROMPT
            },
            {
              type: 'file',
              file: {
                filename: path.basename(filePath),
                file_data: base64Data
              }
            }
          ]
        }],
        plugins: [
          {
            id: 'file-parser',
            pdf: {
              engine: 'native' // Use native processing for better quality
            }
          }
        ]
      };
    } else {
      console.log('🖼️ Processing image with URL...');
      
      // For images, use URL method (works better)
      tempFile = await uploadTempFile(filePath);
      
      request = {
        model: config.model,
        max_tokens: config.maxTokens,
        messages: [{
          role: 'user',
          content: [
            {
              type: 'text',
              text: PASSPORT_OCR_PROMPT
            },
            {
              type: 'image_url',
              image_url: {
                url: tempFile.url
              }
            }
          ]
        }]
      };
    }

    console.log('🔄 Request structure:', {
      model: request.model,
      max_tokens: request.max_tokens,
      content_types: request.messages[0].content.map((c: any) => c.type),
      has_plugins: !!request.plugins
    });

    // Call OpenRouter API
    const response = await callOpenRouterApi(request, config);

    // Parse response
    const extractedData = parseOpenRouterResponse(response);

    console.log('✅ OpenRouter Vision processing completed successfully');
    console.log('📊 Tokens used:', response.usage);

    return {
      success: true,
      extractedData,
      rawText: response.choices[0]?.message?.content || '',
      tokensUsed: response.usage
    };

  } catch (error) {
    console.error('❌ Error processing document with OpenRouter Vision:', error);
    return {
      success: false,
      rawText: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    // Cleanup temporary file (only for images)
    if (tempFile) {
      try {
        await tempFile.cleanup();
        console.log('🧹 Temp file cleaned up successfully');
      } catch (cleanupError) {
        console.warn('⚠️ Error cleaning up temp file:', cleanupError);
      }
    }
  }
};

/**
 * Check if OpenRouter OCR is available
 */
export const isOpenRouterOcrAvailable = (): boolean => {
  return !!USE_OPENROUTER_OCR;
};

/**
 * Get OpenRouter OCR configuration status
 */
export const getOpenRouterOcrStatus = (): OpenRouterOcrStatus => {
  return {
    available: !!USE_OPENROUTER_OCR,
    hasApiKey: !!process.env.NEXT_OR_KEY,
    model: DEFAULT_CONFIG.model
  };
};