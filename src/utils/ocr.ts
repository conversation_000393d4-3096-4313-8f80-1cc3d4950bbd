import { createWorker } from 'tesseract.js';
import fs from 'fs';
import path from 'path';
import pdfParse from 'pdf-parse';
import { processDocumentWithMistral } from './mistral-ocr';
import { processDocumentWithClaude, isClaudeOcrAvailable } from './claude-vision-ocr';
import { processDocumentWithOpenRouter, isOpenRouterOcrAvailable } from './openrouter-vision-ocr';
import { processHeicFile, HeicConversionResult } from './heic-converter';

// OCR Engine Configuration
const USE_MISTRAL_OCR = process.env.USE_MISTRAL_OCR === 'true' || process.env.MISTRAL_API_KEY;
const USE_CLAUDE_OCR = isClaudeOcrAvailable();
const USE_OPENROUTER_OCR = isOpenRouterOcrAvailable();

// Configure Tesseract.js for serverless environment (Vercel)
// In serverless environments, we need to use the Node.js API directly to avoid WASM loading issues
const TESSERACT_CONFIG = {
  // Use CDN for language data as it's smaller and more reliable
  langPath: 'https://tessdata.projectnaptha.com/4.0.0',
  // For serverless environments, we'll use the Node.js API without worker threads
  // This avoids the WASM file loading issues entirely
};

// File type detection
export enum FileType {
  IMAGE = 'image',
  PDF = 'pdf',
  UNSUPPORTED = 'unsupported'
}

// Configuration for PDF processing (serverless compatible)
// Note: PDF_CONFIG is kept for future use when PDF conversion is implemented
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const PDF_CONFIG = {
  maxPages: 10, // Maximum pages to process (not used in serverless)
  maxFileSize: 50 * 1024 * 1024, // 50MB max
  timeout: 60000, // 60 seconds timeout
};

export interface ExtractedDocumentData {
  surname?: string;
  name?: string;
  dateOfBirth?: string;
  citizenship?: string;
  passportNumber?: string;
  passportIssueDate?: string;
  passportExpiryDate?: string;
  iin?: string;
  idNumber?: string;
  gender?: string;
  nationality?: string;
  birthPlace?: string;
  rawText?: string; // Store the raw text for debugging
}

// File type detection function
export const detectFileType = async (filePath: string): Promise<FileType> => {
  try {
    console.log('Detecting file type for:', filePath);

    if (!fs.existsSync(filePath)) {
      throw new Error('File does not exist');
    }

    // Read first few bytes to detect file signature
    const fd = fs.openSync(filePath, 'r');
    const buffer = Buffer.alloc(10);
    fs.readSync(fd, buffer, 0, 10, 0);
    fs.closeSync(fd);

    // Check PDF signature
    if (buffer.toString('ascii', 0, 4) === '%PDF') {
      console.log('Detected file type: PDF');
      return FileType.PDF;
    }

    // Check image signatures
    const signatures = {
      jpeg: [0xFF, 0xD8, 0xFF],
      png: [0x89, 0x50, 0x4E, 0x47],
      bmp: [0x42, 0x4D],
      gif: [0x47, 0x49, 0x46],
      webp: [0x52, 0x49, 0x46, 0x46], // RIFF (WebP container)
      heic: [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63], // HEIC signature
      heif: [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x6D, 0x69, 0x66, 0x31] // HEIF signature
    };

    for (const [type, signature] of Object.entries(signatures)) {
      if (signature.every((byte, index) => buffer[index] === byte)) {
        console.log(`Detected file type: ${type.toUpperCase()} image`);
        return FileType.IMAGE;
      }
    }

    // Fallback: check file extension
    const ext = path.extname(filePath).toLowerCase();
    if (['.pdf'].includes(ext)) {
      console.log('Detected file type by extension: PDF');
      return FileType.PDF;
    }

    if (['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp', '.tiff', '.tif', '.heic', '.heif'].includes(ext)) {
      console.log('Detected file type by extension: IMAGE');
      return FileType.IMAGE;
    }

    console.log('File type not supported');
    return FileType.UNSUPPORTED;
  } catch (error) {
    console.error('Error detecting file type:', error);
    return FileType.UNSUPPORTED;
  }
};

// Extract text directly from PDF (if it has text layer)
export const extractTextFromPdf = async (pdfPath: string): Promise<string> => {
  try {
    console.log('Attempting to extract text directly from PDF...');
    const dataBuffer = fs.readFileSync(pdfPath);
    const data = await pdfParse(dataBuffer);

    if (data.text && data.text.trim().length > 0) {
      console.log('Successfully extracted text from PDF:', data.text.length, 'characters');
      return data.text;
    }

    console.log('No text found in PDF, will need OCR');
    return '';
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return '';
  }
};

// Convert PDF to images for OCR processing (serverless compatible fallback)
export const convertPdfToImages = async (_pdfPath: string): Promise<string[]> => {
  console.log('PDF to image conversion not available in serverless environment');
  console.log('This function is a placeholder for environments that support PDF conversion');

  // In a serverless environment, we'll rely on text extraction instead
  // This function exists for compatibility but will not be used if text extraction succeeds
  throw new Error('PDF to image conversion not supported in serverless environment. Use text extraction instead.');
};

// Helper function to create worker for serverless environment
const createWorkerForServerless = async (languages: string) => {
  try {
    console.log(`Creating Tesseract worker for serverless environment with languages: ${languages}`);
    console.log('Environment check:', {
      isVercel: !!process.env.VERCEL,
      nodeEnv: process.env.NODE_ENV,
      platform: process.platform
    });

    // For serverless environments, use minimal configuration to avoid WASM loading issues
    // The key is to let Tesseract.js use its built-in Node.js API without external URLs

    let workerOptions: { langPath: string, workerPath?: string, corePath?: string } = {
      langPath: TESSERACT_CONFIG.langPath,
    };

    // In serverless environments, explicitly avoid external CDN URLs
    // Let Tesseract.js use the local WASM files included via Next.js config
    if (process.env.VERCEL || process.env.NODE_ENV === 'production') {
      console.log('Configuring for serverless environment - using local WASM files...');

      // Don't specify corePath or workerPath to force usage of local bundled files
      // This allows Tesseract.js to use the WASM files included via outputFileTracingIncludes
      workerOptions = {
        langPath: TESSERACT_CONFIG.langPath,
        // Explicitly avoid CDN URLs - let it use local files
      };

      console.log('Worker options for serverless:', workerOptions);
    } else {
      // For development, we can still use CDN URLs
      workerOptions = {
        langPath: TESSERACT_CONFIG.langPath,
        workerPath: 'https://unpkg.com/tesseract.js@v4.1.1/dist/worker.min.js',
        corePath: 'https://unpkg.com/tesseract.js-core@v4.0.2',
      };
      console.log('Worker options for development:', workerOptions);
    }

    const worker = await createWorker(languages, undefined, workerOptions);

    console.log('Tesseract worker created successfully for serverless environment');
    return worker;
  } catch (error) {
    console.error('Failed to create serverless worker:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });

    // Try with just English if multi-language fails
    if (languages !== 'eng') {
      console.log('Retrying with English only...');
      return await createWorkerForServerless('eng');
    }

    // If English also fails, try with absolutely minimal configuration
    console.log('Trying with absolutely minimal configuration...');
    try {
      // Use the most basic createWorker call possible - no options at all
      const worker = await createWorker('eng');
      console.log('Worker created with minimal configuration');
      return worker;
    } catch (finalError) {
      console.error('All worker creation attempts failed:', finalError);
      throw new Error(`Failed to create OCR worker: ${finalError instanceof Error ? finalError.message : 'Unknown error'}`);
    }
  }
};

export const extractTextFromImage = async (
  fileOrPath: File | string,
  _fileType?: string // Add underscore to indicate intentionally unused parameter
): Promise<string> => {
  let worker = null;

  try {
    console.log('Starting OCR extraction...');

    if (typeof fileOrPath === 'string') {
      console.log('File path provided for OCR:', fileOrPath);
    } else {
      console.log('File info:', {
        name: fileOrPath.name,
        type: fileOrPath.type,
        size: `${(fileOrPath.size / (1024 * 1024)).toFixed(2)} MB`
      });
    }

    // Try to use multiple languages for better recognition in serverless environment
    console.log('Creating OCR worker for serverless environment...');

    try {
      worker = await createWorkerForServerless('eng+rus+kaz');
      console.log('Multi-language worker created successfully');
    } catch (workerError) {
      console.error('Failed to create multi-language worker, trying English only:', workerError);
      try {
        worker = await createWorkerForServerless('eng');
        console.log('English-only worker created successfully');
      } catch (englishWorkerError) {
        console.error('Failed to create English worker:', englishWorkerError);
        throw new Error(`Failed to create OCR worker: ${englishWorkerError instanceof Error ? englishWorkerError.message : 'Unknown error'}`);
      }
    }

    try {
      console.log('Starting recognition...');
      const { data: { text } } = await worker.recognize(fileOrPath);
      console.log('Recognition completed, text length:', text.length);
      return text;
    } catch (recognitionError) {
      console.error('OCR recognition error:', recognitionError);
      throw recognitionError;
    }
  } catch (error) {
    console.error('OCR Error:', error);
    throw new Error(`Failed to extract text from image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Ensure worker is always terminated
    if (worker) {
      try {
        await worker.terminate();
        console.log('OCR worker terminated successfully');
      } catch (terminateError) {
        console.error('Error terminating OCR worker:', terminateError);
      }
    }
  }
};

/**
 * Transliterate Cyrillic names to Latin according to international standards
 */
const transliterateName = (name: string): string => {
  if (!name) return '';
  
  // If already in Latin, return as is
  if (/^[A-Za-z\s\-']+$/.test(name)) {
    return name.toUpperCase();
  }
  
  // Transliteration map for Russian and Kazakh
  const transliterationMap: { [key: string]: string } = {
    // Russian alphabet
    'А': 'A', 'а': 'A',
    'Б': 'B', 'б': 'B',
    'В': 'V', 'в': 'V',
    'Г': 'G', 'г': 'G',
    'Д': 'D', 'д': 'D',
    'Е': 'E', 'е': 'E',
    'Ё': 'YO', 'ё': 'YO',
    'Ж': 'ZH', 'ж': 'ZH',
    'З': 'Z', 'з': 'Z',
    'И': 'I', 'и': 'I',
    'Й': 'Y', 'й': 'Y',
    'К': 'K', 'к': 'K',
    'Л': 'L', 'л': 'L',
    'М': 'M', 'м': 'M',
    'Н': 'N', 'н': 'N',
    'О': 'O', 'о': 'O',
    'П': 'P', 'п': 'P',
    'Р': 'R', 'р': 'R',
    'С': 'S', 'с': 'S',
    'Т': 'T', 'т': 'T',
    'У': 'U', 'у': 'U',
    'Ф': 'F', 'ф': 'F',
    'Х': 'KH', 'х': 'KH',
    'Ц': 'TS', 'ц': 'TS',
    'Ч': 'CH', 'ч': 'CH',
    'Ш': 'SH', 'ш': 'SH',
    'Щ': 'SHCH', 'щ': 'SHCH',
    'Ъ': 'IE', 'ъ': 'IE',
    'Ы': 'Y', 'ы': 'Y',
    'Ь': '', 'ь': '',
    'Э': 'E', 'э': 'E',
    'Ю': 'YU', 'ю': 'YU',
    'Я': 'YA', 'я': 'YA',
    
    // Kazakh specific letters
    'Ә': 'AE', 'ә': 'AE',
    'Қ': 'Q', 'қ': 'Q',
    'Ң': 'NG', 'ң': 'NG',
    'Ғ': 'GH', 'ғ': 'GH',
    'Ү': 'UE', 'ү': 'UE',
    'Ұ': 'U', 'ұ': 'U',
    'Һ': 'H', 'һ': 'H',
    'Ө': 'OE', 'ө': 'OE',
    'І': 'I', 'і': 'I',
    
    // Common punctuation and spaces
    ' ': ' ',
    '-': '-',
    "'": "'",
    '"': '"'
  };
  
  let result = '';
  for (let i = 0; i < name.length; i++) {
    const char = name[i];
    if (transliterationMap[char] !== undefined) {
      result += transliterationMap[char];
    } else if (/[A-Za-z]/.test(char)) {
      // Already Latin
      result += char.toUpperCase();
    } else {
      // Unknown character, keep as is
      result += char;
    }
  }
  
  // Clean up multiple spaces and trim
  result = result.replace(/\s+/g, ' ').trim();
  
  console.log(`Transliterated name: "${name}" → "${result}"`);
  return result;
};

export const parsePassportData = (text: string): ExtractedDocumentData => {
  console.log('Parsing passport data from text...');
  
  const data: ExtractedDocumentData = {
    rawText: text // Store raw text for debugging
  };
  
  // Clean and normalize text to improve matching
  const normalizedText = text
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s.,:<>()\/\-]/g, ' ')
    .trim();
  
  console.log('Normalized text length:', normalizedText.length);
  
  // Extract passport number - pattern like: "N12345678" or similar formats
  const passportNumberPatterns = [
    /\b[A-Z]\d{8}\b/,                             // Standard format N12345678
    /Passport No\.?:?\s*([A-Z0-9]{7,9})/i,        // With label
    /№\s*([A-Z0-9]{7,9})/i,                       // With № symbol
    /Паспорт\s*№?\s*([A-Z0-9]{7,9})/i,            // Russian/Kazakh label
    /номер\s*(?:паспорта)?:?\s*([A-Z0-9]{7,9})/i  // Another Russian variant
  ];
  
  for (const pattern of passportNumberPatterns) {
    const match = normalizedText.match(pattern);
    if (match && (match[1] || match[0])) {
      data.passportNumber = match[1] || match[0];
      console.log('Found passport number:', data.passportNumber);
      break;
    }
  }
  
  // Extract IIN (Individual Identification Number) - 12 digits
  const iinMatch = normalizedText.match(/\b\d{12}\b/);
  if (iinMatch) {
    data.iin = iinMatch[0];
    console.log('Found IIN:', data.iin);
  }
  
  // Extract name patterns with various labels in different languages
  const namePatterns = [
    /(?:Given names|Name|Имя|Аты|Имена)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:first name|given name)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:имя|имена)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of namePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.name = transliterateName(match[1].trim());
      console.log('Found name:', data.name);
      break;
    }
  }
  
  // If we still don't have a name, try a more generic approach
  if (!data.name) {
    // Look for capitalized words near name-related terms
    const genericNameMatches = normalizedText.match(/(?:name|имя|first|given|имена)[:\s]+([A-ZА-Я][a-zа-я]+)/i);
    if (genericNameMatches && genericNameMatches[1]) {
      data.name = transliterateName(genericNameMatches[1].trim());
      console.log('Found name using generic approach:', data.name);
    }
  }
  
  // Extract surname patterns
  const surnamePatterns = [
    /(?:Surname|Фамилия|Тегі)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:last name|family name)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:фамилия)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of surnamePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.surname = transliterateName(match[1].trim());
      console.log('Found surname:', data.surname);
      break;
    }
  }
  
  // If we still don't have a surname, try a more generic approach
  if (!data.surname && data.name) {
    // Look for capitalized words near surname-related terms
    const genericSurnameMatches = normalizedText.match(/(?:surname|family|фамилия|last)[:\s]+([A-ZА-Я][a-zа-я]+)/i);
    if (genericSurnameMatches && genericSurnameMatches[1]) {
      data.surname = transliterateName(genericSurnameMatches[1].trim());
      console.log('Found surname using generic approach:', data.surname);
    }
  }
  
  // Extract gender
  const genderMatch = normalizedText.match(/(?:Gender|Sex|Пол|Жынысы)[:\s]+([MFmfМЖмж]|Male|Female|Муж|Жен|мужской|женский)/i);
  if (genderMatch && genderMatch[1]) {
    const genderValue = genderMatch[1].trim().toLowerCase();
    if (['m', 'male', 'м', 'муж', 'мужской'].includes(genderValue)) {
      data.gender = 'M';
      console.log('Found gender: Male');
    } else if (['f', 'female', 'ж', 'жен', 'женский'].includes(genderValue)) {
      data.gender = 'F';
      console.log('Found gender: Female');
    }
  }
  
  // Extract nationality
  const nationalityMatch = normalizedText.match(/(?:Nationality|Национальность|Ұлты)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i);
  if (nationalityMatch && nationalityMatch[1]) {
    data.nationality = nationalityMatch[1].trim();
    console.log('Found nationality:', data.nationality);
  }
  
  // Extract dates (birth, issue, expiry)
  // Format: DD.MM.YYYY or YYYY-MM-DD or DD MMM YYYY
  const datePatterns = [
    /\b(\d{2}[./-]\d{2}[./-]\d{4})\b/g,  // DD.MM.YYYY or DD/MM/YYYY or DD-MM-YYYY
    /\b(\d{4}[./-]\d{2}[./-]\d{2})\b/g,  // YYYY.MM.DD or YYYY/MM/DD or YYYY-MM-DD
    /\b(\d{2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4})\b/gi  // DD MMM YYYY
  ];
  
  let dates: string[] = [];
  for (const pattern of datePatterns) {
    const matches = [...normalizedText.matchAll(pattern)].map(match => match[0]);
    dates = [...dates, ...matches];
  }
  
  console.log('Found dates:', dates);
  
  // Assign dates based on context clues in surrounding text
  for (let i = 0; i < dates.length; i++) {
    const currentDate = dates[i];
    // Look for context 50 characters before and after the date
    const dateContext = normalizedText.substring(
      Math.max(0, normalizedText.indexOf(currentDate) - 50), 
      Math.min(normalizedText.length, normalizedText.indexOf(currentDate) + currentDate.length + 50)
    );
    
    if (dateContext.match(/birth|рожд|date of birth|дата рождения|туған|туылған|born/i)) {
      data.dateOfBirth = currentDate;
      console.log('Found date of birth:', currentDate);
    } else if (dateContext.match(/issue|выда|date of issue|дата выдачи|берілген|берілді/i)) {
      data.passportIssueDate = currentDate;
      console.log('Found issue date:', currentDate);
    } else if (dateContext.match(/expir|действ|valid until|годен до|дейін жарамды|expiry|expire/i)) {
      data.passportExpiryDate = currentDate;
      console.log('Found expiry date:', currentDate);
    }
  }
  
  // If we have multiple dates but couldn't associate them by context, 
  // make an educated guess based on chronological order
  if (dates.length > 0) {
    // Try to convert dates to timestamps for comparison
    try {
      const sortedDates = [...dates].sort((a, b) => {
        // Convert to a common format (YYYY-MM-DD)
        const formatDate = (dateStr: string): string => {
          // DD.MM.YYYY or DD/MM/YYYY or DD-MM-YYYY
          if (/^\d{2}[./-]\d{2}[./-]\d{4}$/.test(dateStr)) {
            const [day, month, year] = dateStr.split(/[./-]/);
            return `${year}-${month}-${day}`;
          }
          // YYYY.MM.DD or YYYY/MM/DD or YYYY-MM-DD
          if (/^\d{4}[./-]\d{2}[./-]\d{2}$/.test(dateStr)) {
            return dateStr.replace(/[./]/g, '-');
          }
          // DD MMM YYYY
          if (/^\d{2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4}$/i.test(dateStr)) {
            const [day, month, year] = dateStr.split(/\s+/);
            const monthMap: {[key: string]: string} = {
              'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 'may': '05', 'jun': '06',
              'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
            };
            const monthNum = monthMap[month.toLowerCase().substring(0, 3)];
            return `${year}-${monthNum}-${day}`;
          }
          return dateStr;
        };
        
        return new Date(formatDate(a)).getTime() - new Date(formatDate(b)).getTime();
      });
      
      // If we have multiple dates but couldn't assign them by context
      if (sortedDates.length >= 3) {
        if (!data.dateOfBirth) {
          data.dateOfBirth = sortedDates[0]; // Earliest date is likely birth date
          console.log('Assigned birth date by chronology:', sortedDates[0]);
        }
        if (!data.passportIssueDate) {
          data.passportIssueDate = sortedDates[1]; // Middle date is likely issue date
          console.log('Assigned issue date by chronology:', sortedDates[1]);
        }
        if (!data.passportExpiryDate) {
          data.passportExpiryDate = sortedDates[sortedDates.length - 1]; // Latest date is likely expiry
          console.log('Assigned expiry date by chronology:', sortedDates[sortedDates.length - 1]);
        }
      } else if (sortedDates.length === 2) {
        // If we have only two dates, make a reasonable guess
        if (!data.dateOfBirth) {
          data.dateOfBirth = sortedDates[0]; // Earlier date is likely birth date
          console.log('Assigned birth date by chronology (2 dates):', sortedDates[0]);
        }
        if (!data.passportIssueDate) {
          data.passportIssueDate = sortedDates[1]; // Later date is likely issue date
          console.log('Assigned issue date by chronology (2 dates):', sortedDates[1]);
        }
      }
    } catch (error) {
      console.error('Error sorting dates:', error);
    }
  }
  
  // Extract citizenship
  const citizenshipPatterns = [
    /(?:Citizenship|Nationality|Гражданство|Азаматтығы)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:Citizen of|Country)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i,
    /(?:гражданство)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё]+)/i
  ];
  
  for (const pattern of citizenshipPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.citizenship = match[1].trim();
      console.log('Found citizenship:', data.citizenship);
      break;
    }
  }
  
  // Extract birth place
  const birthPlacePatterns = [
    /(?:Place of birth|Birth place|Место рождения|Туған жері)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё\s]+?)(?:\b\d|\n|,)/i,
    /(?:Born in|Born at)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё\s]+?)(?:\b\d|\n|,)/i,
    /(?:место рождения)[:\s]+([A-Za-zА-Яа-яІіҢңҒғҮүӨөҚқЁё\s]+?)(?:\b\d|\n|,)/i
  ];
  
  for (const pattern of birthPlacePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.birthPlace = match[1].trim();
      console.log('Found birth place:', data.birthPlace);
      break;
    }
  }
  
  // Extract ID card number (usually 9 digits)
  const idNumberPatterns = [
    /(?:ID Number|№ удостоверения|ID card no|Жеке куәлік №)[:\s]*(\d{9})/i,
    /(?:ID|Identity card)[:\s]*(\d{9})/i,
    /(?:удостоверение личности|личное удостоверение)[:\s]*(?:№)?(\d{9})/i
  ];
  
  for (const pattern of idNumberPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      data.idNumber = match[1];
      console.log('Found ID number:', data.idNumber);
      break;
    }
  }
  
  return data;
};

// Process document with Tesseract.js (fallback method)
const processWithTesseract = async (
  filePath: string,
  fileType: FileType,
  _tempFiles: string[]
): Promise<string> => {
  let extractedText = '';

  if (fileType === FileType.PDF) {
    console.log('=== PROCESSING PDF DOCUMENT WITH TESSERACT ===');

    // Step 2a: Try to extract text directly from PDF
    const directText = await extractTextFromPdf(filePath);

    if (directText && directText.trim().length > 50) {
      console.log('Successfully extracted text directly from PDF');
      extractedText = directText;
    } else {
      console.log('PDF has no text layer or insufficient text');
      console.log('PDF to image conversion not available in serverless environment');

      // In serverless environments, we cannot convert PDF to images
      // So we'll use whatever text we extracted, even if it's minimal
      if (directText && directText.trim().length > 0) {
        console.log('Using minimal text extracted from PDF');
        extractedText = directText;
      } else {
        throw new Error('PDF contains no extractable text and image conversion is not available in serverless environment. Please upload an image file (JPG, PNG, HEIC, etc.) instead.');
      }
    }
  } else if (fileType === FileType.IMAGE) {
    console.log('=== PROCESSING IMAGE DOCUMENT WITH TESSERACT ===');

    // Note: HEIC files are now automatically converted to JPEG before reaching this point
    // Step 2c: Process image directly with OCR
    extractedText = await extractTextFromImage(filePath);
  }

  return extractedText;
};

// Universal document processing function
export const processDocument = async (filePath: string, agentId?: string): Promise<ExtractedDocumentData> => {
  let heicConversion: HeicConversionResult | null = null;

  try {
    console.log('=== STARTING DOCUMENT PROCESSING ===');
    console.log('File path:', filePath);
    console.log('OCR Engines available:', {
      openrouter: USE_OPENROUTER_OCR,
      claude: USE_CLAUDE_OCR,
      mistral: USE_MISTRAL_OCR,
      tesseract: true
    });

    // Step 1: Process HEIC files (convert to JPEG if needed)
    console.log('=== STEP 1: HEIC PROCESSING ===');
    heicConversion = await processHeicFile(filePath);

    if (!heicConversion.success) {
      throw new Error(`HEIC processing failed: ${heicConversion.error}`);
    }

    const processFilePath = heicConversion.convertedPath || filePath;
    console.log('File to process:', processFilePath);
    console.log('Was converted from HEIC:', heicConversion.wasConverted);

    // Step 2: Detect file type
    const fileType = await detectFileType(processFilePath);
    console.log('Detected file type:', fileType);

    if (fileType === FileType.UNSUPPORTED) {
      throw new Error('Unsupported file type. Please upload a PDF or image file (JPG, PNG, HEIC, etc.)');
    }

    let extractedText = '';
    const tempFiles: string[] = [];

    // Step 2: Choose OCR engine (priority: OpenRouter > Claude > Mistral > Tesseract)
    if (USE_OPENROUTER_OCR && agentId) {
      console.log('=== USING OPENROUTER VISION OCR ===');
      console.log('processFilePath', processFilePath);
      console.log('fileType', fileType);
      console.log('agentId', agentId);
      try {
        const openrouterFileType = fileType === FileType.PDF ? 'pdf' : 'image';
        const result = await processDocumentWithOpenRouter(processFilePath, openrouterFileType, agentId);

        if (result.success && result.extractedData) {
          // Convert OpenRouter result to ExtractedDocumentData format
          const openrouterData = result.extractedData;
          const extractedData: ExtractedDocumentData = {
            name: openrouterData.name,
            surname: openrouterData.surname,
            dateOfBirth: openrouterData.dateOfBirth,
            passportNumber: openrouterData.passportNumber,
            passportIssueDate: openrouterData.passportIssueDate,
            passportExpiryDate: openrouterData.passportExpiryDate,
            nationality: openrouterData.nationality,
            citizenship: openrouterData.citizenship,
            rawText: openrouterData.rawText
          };
          
          console.log('OpenRouter Vision OCR completed successfully');
          console.log('Extracted data:', extractedData);
          console.log('Tokens used:', result.tokensUsed);
          
          return extractedData;
        } else {
          console.warn('OpenRouter Vision OCR failed:', result.error);
          console.log('Falling back to Claude OCR...');
        }
      } catch (error) {
        console.error('OpenRouter Vision OCR error:', error);
        console.log('Falling back to Claude OCR...');
      }
    }
    
    // Fallback to Claude OCR
    if (USE_CLAUDE_OCR && agentId) {
      console.log('=== USING CLAUDE VISION OCR ===');
      console.log('processFilePath', processFilePath);
      console.log('fileType', fileType);
      console.log('agentId', agentId);
      try {
        const claudeFileType = fileType === FileType.PDF ? 'pdf' : 'image';
        const result = await processDocumentWithClaude(processFilePath, claudeFileType, agentId);

        if (result.success && result.extractedData) {
          // Convert Claude result to ExtractedDocumentData format
          const claudeData = result.extractedData;
          const extractedData: ExtractedDocumentData = {
            name: claudeData.name,
            surname: claudeData.surname,
            dateOfBirth: claudeData.dateOfBirth,
            passportNumber: claudeData.passportNumber,
            passportIssueDate: claudeData.passportIssueDate,
            passportExpiryDate: claudeData.passportExpiryDate,
            nationality: claudeData.nationality,
            citizenship: claudeData.citizenship,
            rawText: claudeData.rawText
          };
          
          console.log('Claude Vision OCR completed successfully');
          console.log('Extracted data:', extractedData);
          console.log('Tokens used:', result.tokensUsed);
          
          return extractedData;
        } else {
          console.warn('Claude Vision OCR failed:', result.error);
          console.log('Falling back to Mistral OCR...');
        }
      } catch (error) {
        console.error('Claude Vision OCR error:', error);
        console.log('Falling back to Mistral OCR...');
      }
    }
    // process.exit(0);
    // Fallback to Mistral OCR
    if (USE_MISTRAL_OCR) {
      console.log('=== USING MISTRAL OCR ===');

      try {
        const mistralFileType = fileType === FileType.PDF ? 'pdf' : 'image';
        const result = await processDocumentWithMistral(processFilePath, mistralFileType);

        if (result.success && result.text) {
          extractedText = result.text;
          console.log('Mistral OCR completed successfully');
          console.log('Extracted text length:', extractedText.length);
        } else {
          console.warn('Mistral OCR failed:', result.error);
          console.log('Falling back to Tesseract.js...');
          // Fall back to Tesseract.js
          extractedText = await processWithTesseract(processFilePath, fileType, tempFiles);
        }
      } catch (error) {
        console.error('Mistral OCR error:', error);
        console.log('Falling back to Tesseract.js...');
        // Fall back to Tesseract.js
        extractedText = await processWithTesseract(processFilePath, fileType, tempFiles);
      }
    } else {
      console.log('=== USING TESSERACT.JS ===');
      extractedText = await processWithTesseract(processFilePath, fileType, tempFiles);
    }

    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error('No text could be extracted from the document');
    }

    console.log('=== TEXT EXTRACTION COMPLETED ===');
    console.log('Total extracted text length:', extractedText.length);

    // Step 3: Parse the extracted text for passport data
    console.log('=== PARSING PASSPORT DATA ===');
    const parsedData = parsePassportData(extractedText);

    console.log('=== DOCUMENT PROCESSING COMPLETED ===');
    console.log('Extracted data summary:', {
      hasName: !!parsedData.name,
      hasSurname: !!parsedData.surname,
      hasPassportNumber: !!parsedData.passportNumber,
      hasDateOfBirth: !!parsedData.dateOfBirth,
      hasGender: !!parsedData.gender,
      hasNationality: !!parsedData.nationality
    });

    return parsedData;

  } catch (error) {
    console.error('=== DOCUMENT PROCESSING ERROR ===');
    console.error('Error details:', error);
    throw error;
  } finally {
    // Cleanup HEIC conversion files
    if (heicConversion) {
      try {
        await heicConversion.cleanup();
        console.log('HEIC conversion cleanup completed');
      } catch (cleanupError) {
        console.warn('Error during HEIC cleanup:', cleanupError);
      }
    }
  }
};

export const extractDocumentData = async (fileOrPath: File | string, fileType?: string): Promise<ExtractedDocumentData> => {
  try {
    console.log('Starting document data extraction...');
    const text = await extractTextFromImage(fileOrPath, fileType);
    if (!text || text.trim().length === 0) {
      console.error('No text extracted from document');
      throw new Error('No text could be extracted from the document');
    }
    
    const parsedData = parsePassportData(text);
    
    // Check if we extracted meaningful data (at least name or passport number)
    if (!parsedData.name && !parsedData.passportNumber && !parsedData.iin) {
      console.warn('OCR extraction yielded insufficient data', { 
        extractedFields: Object.keys(parsedData).filter(k => k !== 'rawText'),
        textLength: text.length
      });
      
      // Return partial data instead of throwing error
      return { 
        ...parsedData,
        rawText: text 
      };
    }
    
    console.log('Document data extraction completed successfully');
    return parsedData;
  } catch (error) {
    console.error('Document data extraction error:', error);
    // Return an empty object with just the error message and raw text if possible
    return { 
      rawText: error instanceof Error ? error.message : 'Unknown error during OCR processing' 
    };
  }
}; 