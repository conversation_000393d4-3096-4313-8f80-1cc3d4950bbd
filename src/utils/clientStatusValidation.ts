import { 
  PaymentStatus, 
  ClientProgressStatus 
} from '../types/universal-status';

/**
 * Validates client status changes to prevent data inconsistencies
 * Ensures paid clients cannot be set back to 'прошли опросник'
 */

export interface ClientStatusUpdate {
  client_progress_status: string;
  service_payment_status: string;
  service_package_id?: string;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  suggestedStatus?: string;
}

/**
 * Validates that a client status update is logically consistent
 * @param update - The proposed status update
 * @returns ValidationResult indicating if the update is valid
 */
export function validateClientStatusUpdate(update: ClientStatusUpdate): ValidationResult {
  const { client_progress_status, service_payment_status, service_package_id } = update;

  // Rule 1: Paid clients cannot be at "прошли опросник" status
  if (service_payment_status === PaymentStatus.PAID && 
      client_progress_status === ClientProgressStatus.COMPLETED_QUESTIONNAIRE) {
    return {
      isValid: false,
      error: 'Paid clients cannot have status "прошли опросник". They should be at least "оплатили пакет".',
      suggestedStatus: ClientProgressStatus.PAID_PACKAGE
    };
  }

  // Rule 2: Clients with service packages should be marked as paid
  if (service_package_id && service_payment_status === PaymentStatus.UNPAID) {
    return {
      isValid: false,
      error: 'Clients with service packages should have payment status "оплачено".',
      suggestedStatus: ClientProgressStatus.PAID_PACKAGE
    };
  }

  // Rule 3: Clients at "оплатили пакет" status should be paid
  if (client_progress_status === ClientProgressStatus.PAID_PACKAGE && 
      service_payment_status === PaymentStatus.UNPAID) {
    return {
      isValid: false,
      error: 'Clients with status "оплатили пакет" should have payment status "оплачено".'
    };
  }

  return { isValid: true };
}

/**
 * Auto-corrects client status based on payment status and service package
 * @param update - The proposed status update
 * @returns Corrected status update
 */
export function autoCorrectClientStatus(update: ClientStatusUpdate): ClientStatusUpdate {
  const corrected = { ...update };

  // Auto-correct: If paid but at "прошли опросник", move to "оплатили пакет"
  if (corrected.service_payment_status === PaymentStatus.PAID && 
      corrected.client_progress_status === ClientProgressStatus.COMPLETED_QUESTIONNAIRE) {
    corrected.client_progress_status = ClientProgressStatus.PAID_PACKAGE;
  }

  // Auto-correct: If has service package, mark as paid
  if (corrected.service_package_id && corrected.service_payment_status === PaymentStatus.UNPAID) {
    corrected.service_payment_status = PaymentStatus.PAID;
    if (corrected.client_progress_status === ClientProgressStatus.COMPLETED_QUESTIONNAIRE) {
      corrected.client_progress_status = ClientProgressStatus.PAID_PACKAGE;
    }
  }

  return corrected;
}

/**
 * Middleware function to validate client status updates before database operations
 * @param update - The proposed status update
 * @param autoCorrect - Whether to auto-correct invalid statuses
 * @returns Validated and optionally corrected update
 */
export function processClientStatusUpdate(
  update: ClientStatusUpdate, 
  autoCorrect: boolean = true
): { update: ClientStatusUpdate; validation: ValidationResult } {
  
  // First, validate the original update
  const validation = validateClientStatusUpdate(update);
  
  if (!validation.isValid && autoCorrect) {
    // Auto-correct the status if validation failed
    const correctedUpdate = autoCorrectClientStatus(update);
    const correctedValidation = validateClientStatusUpdate(correctedUpdate);
    
    return {
      update: correctedUpdate,
      validation: correctedValidation.isValid 
        ? { isValid: true } 
        : correctedValidation
    };
  }
  
  return { update, validation };
}

/**
 * Database trigger function equivalent for JavaScript validation
 * Call this before any client status update operations
 */
export function enforceClientStatusBusinessRules(update: ClientStatusUpdate): ClientStatusUpdate {
  const { update: correctedUpdate } = processClientStatusUpdate(update, true);
  return correctedUpdate;
} 