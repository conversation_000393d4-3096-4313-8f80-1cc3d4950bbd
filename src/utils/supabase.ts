import { createClient } from '@supabase/supabase-js';
import { VisaFormData } from './types';
import { normalizePhoneNumber } from './validations';
import { CLIENT_PROGRESS_STATUS, SERVICE_PAYMENT_STATUS, VISA_STATUS } from '../types/client-status';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create Supabase client with explicit configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  },
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    }
  }
});

/**
 * Convert visaDestination values from Step1 to normalized visaCountry codes
 * Used for consistent filtering in admin panel
 */
const convertVisaDestinationToCountry = (visaDestination: string, otherVisaDestination?: string): string => {
  // Handle "other" case
  if (visaDestination === 'other' && otherVisaDestination) {
    // Try to normalize the other destination
    const normalized = otherVisaDestination.toLowerCase();
    if (normalized.includes('сша') || normalized.includes('америк') || normalized.includes('usa') || normalized.includes('america')) {
      return 'US';
    }
    if (normalized.includes('британ') || normalized.includes('англи') || normalized.includes('uk') || normalized.includes('britain')) {
      return 'UK';
    }
    if (normalized.includes('канад') || normalized.includes('canada')) {
      return 'Canada';
    }
    if (normalized.includes('австрал') || normalized.includes('australia')) {
      return 'Australia';
    }
    if (normalized.includes('китай') || normalized.includes('china')) {
      return 'CN';
    }
    // Default to the raw value
    return otherVisaDestination;
  }

  // Map from Step1 values to display names (consistent with Step1 labels)
  const mapping: { [key: string]: string } = {
    'usa': 'США',
    'uk': 'Великобритания', 
    'canada': 'Канада',
    'australia': 'Австралия',
    'schengen': 'Шенген',
    'other': otherVisaDestination || 'Другое'
  };

  return mapping[visaDestination] || visaDestination;
};

/**
 * Determine client progress status based on form step
 */
const getClientProgressStatusByStep = (step: number, paymentStatus: string = SERVICE_PAYMENT_STATUS.UNPAID): string => {
  // For paid clients, progress through the workflow
  if (paymentStatus === SERVICE_PAYMENT_STATUS.PAID) {
    if (step >= 10) {
      return CLIENT_PROGRESS_STATUS.SUBMITTED;
    } else if (step >= 7) {
      return CLIENT_PROGRESS_STATUS.FORM_FILLING;
    } else if (step >= 5) {
      return CLIENT_PROGRESS_STATUS.BOT_COLLECTING;
    } else {
      return CLIENT_PROGRESS_STATUS.PAID_PACKAGE; // Paid clients start at "Оплатили пакет услуг"
    }
  }
  
  // For unpaid clients, progress based on form completion
  if (step >= 10) {
    return CLIENT_PROGRESS_STATUS.SUBMITTED;
  } else if (step >= 3) {
    return CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE; // "Прошли опросник"
  } else if (step >= 1) {
    return CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE; // Still "Прошли опросник" but started
  } else {
    return CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE; // Default status
  }
};

export const saveFormData = async (
  agentId: string,
  formData: VisaFormData,
  step: number,
  companyId?: string,
  uploadedFiles: { [key: string]: string } = {},
  retryCount = 0
): Promise<{ data: unknown; error: Error | null }> => {
  const maxRetries = 3;

  // Prepare form data for database - add visaCountry field for consistent filtering
  const formDataForDb = {
    ...formData,
    // Convert visaDestination to visaCountry for API compatibility
    visaCountry: formData.visaDestination ? 
      convertVisaDestinationToCountry(formData.visaDestination, formData.otherVisaDestination) : 
      undefined
  };

  // Save to database from step 1 onwards to ensure users appear on admin dashboard
  // Only save to localStorage for step 0 (if exists)
  if (step < 1) {
    console.log(`Step ${step} - Saving to localStorage only, not saving to database yet`);
    
    // Save to localStorage for temporary storage
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(`form_data_${agentId}`, JSON.stringify({
          formData: formDataForDb, // Use formDataForDb here too for consistency
          step,
          uploadedFiles,
          timestamp: new Date().toISOString()
        }));
        console.log('Form data saved to localStorage');
      } catch (error) {
        console.error('Error saving to localStorage:', error);
      }
    }
    
    // Return success without database operation
    return { data: { step_status: step, form_data: formDataForDb }, error: null };
  }

  // For step 1 and above, save to database
  console.log(`Step ${step} - Saving to database`);

  try {
    // Extract and normalize phone number from form data for separate storage
    const phoneNumber = formData.phone ? normalizePhoneNumber(formData.phone) : null;

    // Check if this is the first time saving to database (step 1)
    // If so, try to get any existing localStorage data to merge
    let finalFormData = formDataForDb;
    if (step === 1 && typeof window !== 'undefined') {
      try {
        const localStorageData = localStorage.getItem(`form_data_${agentId}`);
        if (localStorageData) {
          const parsedData = JSON.parse(localStorageData);
          // Merge localStorage data with current form data
          finalFormData = { ...parsedData.formData, ...formDataForDb };
          console.log('Merged localStorage data with current form data');
          
          // Clean up localStorage
          localStorage.removeItem(`form_data_${agentId}`);
        }
      } catch (error) {
        console.error('Error retrieving localStorage data:', error);
      }
    }

    // Determine client progress status based on step
    const clientProgressStatus = getClientProgressStatusByStep(step, SERVICE_PAYMENT_STATUS.UNPAID);

    // Prepare update data
    const updateData: any = {
      agent_id: agentId,
      step_status: step,
      form_data: finalFormData,
      uploaded_files: uploadedFiles,
      phone_number: phoneNumber,
      company_id: companyId || null,
      client_progress_status: clientProgressStatus,
      service_payment_status: SERVICE_PAYMENT_STATUS.UNPAID, // Initialize payment status
      visa_status: VISA_STATUS.PENDING, // Initialize visa status  
      consular_fee_paid: false, // Initialize consular fee status
      requires_fixes: false, // Initialize fixes status
      whatsapp_redirected: false, // Initialize WhatsApp status
      updated_at: new Date().toISOString(),
    };

    // Check if this is a new application (doesn't exist yet)
    const { data: existingApp } = await supabase
      .from('visa_applications')
      .select('id, created_at')
      .eq('agent_id', agentId)
      .single();
    
    // Only set created_at if the application doesn't exist yet (new user)
    if (!existingApp) {
      updateData.created_at = new Date().toISOString();
      console.log('Setting created_at for new application:', agentId);
    }

    const { data, error } = await supabase
      .from('visa_applications')
      .upsert(updateData, {
        onConflict: 'agent_id'
      })
      .select();

    if (error) throw error;

    console.log('Form data saved to database successfully');
    return { data, error: null };
  } catch (error) {
    console.error(`Error saving form data (attempt ${retryCount + 1}):`, error);

    // Handle specific constraint violation error
    if (error instanceof Error && error.message.includes('duplicate key value violates unique constraint')) {
      console.log('Attempting to handle constraint violation by updating existing record...');

      // Try a direct update instead of upsert
      try {
        // Extract and normalize phone number from form data for separate storage
        const phoneNumber = formData.phone ? normalizePhoneNumber(formData.phone) : null;

        // Determine client progress status based on step (same logic as upsert)
        const clientProgressStatus = getClientProgressStatusByStep(step, SERVICE_PAYMENT_STATUS.UNPAID);

        // Prepare update data for direct update
        const directUpdateData: any = {
          step_status: step,
          form_data: formDataForDb,
          uploaded_files: uploadedFiles,
          phone_number: phoneNumber,
          company_id: companyId || null,
          client_progress_status: clientProgressStatus,
          service_payment_status: SERVICE_PAYMENT_STATUS.UNPAID, // Initialize payment status
          visa_status: VISA_STATUS.PENDING, // Initialize visa status
          consular_fee_paid: false, // Initialize consular fee status
          requires_fixes: false, // Initialize fixes status
          whatsapp_redirected: false, // Initialize WhatsApp status
          updated_at: new Date().toISOString(),
        };

        // Check if this is a new application (doesn't exist yet)
        const { data: existingApp } = await supabase
          .from('visa_applications')
          .select('id, created_at')
          .eq('agent_id', agentId)
          .single();
        
        // Only set created_at if the application doesn't exist yet (new user)
        if (!existingApp) {
          directUpdateData.created_at = new Date().toISOString();
          console.log('Setting created_at for new application (direct update):', agentId);
        }

        const { data: updateData, error: updateError } = await supabase
          .from('visa_applications')
          .update(directUpdateData)
          .eq('agent_id', agentId)
          .select();

        if (updateError) throw updateError;

        console.log('Form data updated successfully via direct update', phoneNumber ? `with phone: ${phoneNumber}` : 'without phone');
        return { data: updateData, error: null };
      } catch (updateErr) {
        console.error('Direct update also failed:', updateErr);
        return { data: null, error: updateErr instanceof Error ? updateErr : new Error(String(updateErr)) };
      }
    }

    // Retry on network errors or temporary failures
    if (retryCount < maxRetries &&
        (error instanceof Error &&
         (error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('503') ||
          error.message.includes('502')))) {

      console.log(`Retrying save operation in ${(retryCount + 1) * 1000}ms...`);
      await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
      return saveFormData(agentId, formData, step, companyId, uploadedFiles, retryCount + 1);
    }

    return { data: null, error: error instanceof Error ? error : new Error(String(error)) };
  }
};

/**
 * Create a new user record in Supabase
 */
export const createNewUser = async (agentId: string) => {
  try {
    console.log(`Creating new user record for agent ${agentId}`);

    const { data, error } = await supabase
      .from('visa_applications')
      .insert({
        agent_id: agentId,
        step_status: 1,
        form_data: {},
        uploaded_files: {},
        phone_number: null,
        whatsapp_redirected: false,
        client_progress_status: CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE, // Set initial status for trello board
        service_payment_status: SERVICE_PAYMENT_STATUS.UNPAID, // Initialize payment status
        visa_status: VISA_STATUS.PENDING, // Initialize visa status
        consular_fee_paid: false, // Initialize consular fee status
        requires_fixes: false, // Initialize fixes status
        // Don't set created_at initially - let it be set when form is actually submitted
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating new user:', error);
      throw error;
    }

    console.log('New user record created successfully');
    return { data, error: null };
  } catch (error) {
    console.error('Failed to create new user:', error);
    return { data: null, error: error instanceof Error ? error : new Error(String(error)) };
  }
};

/**
 * Get existing user data or create new user if not found
 */
export const getOrCreateUserData = async (agentId: string) => {
  try {
    console.log(`Getting or creating user data for agent ${agentId}`);

    // First try to get existing data from database
    const { data: existingData, error: getError } = await getFormData(agentId);

    if (getError) {
      console.error('Error getting existing data:', getError);
      return { data: null, error: getError, isNewUser: false };
    }

    // If database data exists, return it
    if (existingData) {
      console.log('Found existing user data in database');
      return { data: existingData, error: null, isNewUser: false };
    }

    // If no database data found, check localStorage for early-stage data
    if (typeof window !== 'undefined') {
      try {
        const localStorageData = localStorage.getItem(`form_data_${agentId}`);
        if (localStorageData) {
          const parsedData = JSON.parse(localStorageData);
          console.log('Found existing user data in localStorage');
          
          // Return localStorage data in database format
          return { 
            data: {
              agent_id: agentId,
              step_status: parsedData.step || 1,
              form_data: parsedData.formData || {},
              uploaded_files: parsedData.uploadedFiles || {},
              phone_number: parsedData.formData?.phone || null,
              whatsapp_redirected: false,
              created_at: parsedData.timestamp || new Date().toISOString(),
              updated_at: parsedData.timestamp || new Date().toISOString(),
            }, 
            error: null, 
            isNewUser: false 
          };
        }
      } catch (error) {
        console.error('Error retrieving localStorage data:', error);
      }
    }

    // If no data found anywhere, this is a new user
    // Don't create database record yet - wait until step 3
    console.log('No existing data found, treating as new user (no database record created yet)');
    return { 
      data: {
        agent_id: agentId,
        step_status: 1,
        form_data: {},
        uploaded_files: {},
        phone_number: null,
        whatsapp_redirected: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }, 
      error: null, 
      isNewUser: true 
    };
  } catch (error) {
    console.error('Error in getOrCreateUserData:', error);
    return { data: null, error: error instanceof Error ? error : new Error(String(error)), isNewUser: false };
  }
};

export const getFormData = async (agentId: string, retryCount = 0): Promise<{ data: unknown; error: Error | null }> => {
  const maxRetries = 3;

  try {
    console.log(`Getting form data for agent ${agentId}, attempt ${retryCount + 1}`);

    // Validate inputs
    if (!agentId || agentId.trim() === '') {
      throw new Error('Agent ID is required');
    }

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase configuration is missing');
    }

    const { data, error } = await supabase
      .from('visa_applications')
      .select('*')
      .eq('agent_id', agentId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no data found

    if (error) {
      console.error('Supabase error:', error);
      throw error;
    }

    console.log('Form data retrieved successfully:', data ? 'found' : 'not found');
    return { data, error: null };
  } catch (error) {
    console.error(`Error getting form data (attempt ${retryCount + 1}):`, error);

    // Retry on network errors or 406 errors
    if (retryCount < maxRetries &&
        (error instanceof Error &&
         (error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('406') ||
          error.message.includes('503') ||
          error.message.includes('502')))) {

      console.log(`Retrying get operation in ${(retryCount + 1) * 1000}ms...`);
      await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
      return getFormData(agentId, retryCount + 1);
    }

    return { data: null, error: error instanceof Error ? error : new Error(String(error)) };
  }
};

export const searchApplicationsByPhone = async (phoneNumber: string) => {
  try {
    if (!phoneNumber) {
      return { data: null, error: new Error('Phone number is required') };
    }

    // Normalize the phone number for consistent searching
    const normalizedPhone = normalizePhoneNumber(phoneNumber);
    console.log('Searching for application with normalized phone:', normalizedPhone);

    const { data, error } = await supabase
      .from('visa_applications')
      .select('*')
      .eq('phone_number', normalizedPhone)
      .order('created_at', { ascending: false })
      .limit(1); // Get the most recent application

    if (error) {
      console.error('Supabase error searching applications by phone:', error);
      throw error;
    }

    console.log('Search results by phone:', data);

    // Check if we got a valid result
    if (data && data.length > 0) {
      return { data: data[0], error: null };
    } else {
      return { data: null, error: null }; // Not found but no error
    }
  } catch (error) {
    console.error('Error searching for applications by phone:', error);
    return { data: null, error };
  }
};

export const markWhatsappRedirected = async (agentId: string) => {
  try {
    const { error } = await supabase
      .from('visa_applications')
      .update({ whatsapp_redirected: true })
      .eq('agent_id', agentId);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error marking WhatsApp redirected:', error);
    return { error };
  }
};

// Test Supabase connection
export const testSupabaseConnection = async () => {
  try {
    console.log('Testing Supabase connection...');
    console.log('Supabase URL:', supabaseUrl);
    console.log('Supabase Key (first 10 chars):', supabaseAnonKey.substring(0, 10) + '...');

    // Validate configuration first
    if (!supabaseUrl || !supabaseAnonKey) {
      const configError = new Error('Supabase configuration is missing');
      console.error('Configuration error:', configError);
      return { success: false, error: configError };
    }

    // Try a simple query to test the connection
    // If the table doesn't exist, we'll get a different error than connection issues
    const { data, error } = await supabase
      .from('visa_applications')
      .select('id')
      .limit(1);

    if (error) {
      // Check if it's a table not found error (which means connection works)
      if (error.code === 'PGRST106' || error.message.includes('does not exist')) {
        console.log('Supabase connection successful, but table needs to be created');
        return { success: true, data: null, needsTableCreation: true };
      }

      console.error('Supabase connection test failed:', error);
      return { success: false, error };
    }

    console.log('Supabase connection test successful');
    return { success: true, data };
  } catch (error) {
    console.error('Supabase connection test error:', error);
    return { success: false, error };
  }
};
