import { getValidationSchemaForStep } from './validations';

/**
 * Centralized validation system helper functions
 * Provides utilities for working with the new unified validation system
 */

// Type for step numbers
export type StepNumber = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

/**
 * Get validation schema for a specific step
 * This is the main function components should use
 */
export const getStepValidation = (stepNumber: StepNumber) => {
  return getValidationSchemaForStep(stepNumber);
};

/**
 * Validate data for multiple steps at once
 * Useful for cross-step validation or final form submission
 */
export const validateMultipleSteps = async (
  data: any, 
  steps: StepNumber[]
): Promise<{
  isValid: boolean;
  errors: { [stepNumber: number]: any };
}> => {
  const results: { [stepNumber: number]: any } = {};
  let isValid = true;

  for (const stepNumber of steps) {
    try {
      const schema = getValidationSchemaForStep(stepNumber);
      await schema.validate(data, { abortEarly: false });
      results[stepNumber] = null; // No errors
    } catch (error: any) {
      isValid = false;
      results[stepNumber] = error.errors || error.message;
    }
  }

  return {
    isValid,
    errors: results
  };
};

/**
 * Check if specific fields are valid for a step
 * Useful for partial validation or field-specific checks
 */
export const validateStepFields = async (
  data: any,
  stepNumber: StepNumber,
  fieldNames: string[]
): Promise<{
  isValid: boolean;
  fieldErrors: { [fieldName: string]: string };
}> => {
  const schema = getValidationSchemaForStep(stepNumber);
  const fieldErrors: { [fieldName: string]: string } = {};
  let isValid = true;

  for (const fieldName of fieldNames) {
    try {
      await schema.validateAt(fieldName, data);
    } catch (error: any) {
      isValid = false;
      fieldErrors[fieldName] = error.message;
    }
  }

  return {
    isValid,
    fieldErrors
  };
};

/**
 * Get all field names for a specific step
 * Useful for debugging or form field generation
 */
export const getStepFieldNames = (stepNumber: StepNumber): string[] => {
  const stepFields: { [key: number]: string[] } = {
    1: ['visaDestination', 'otherVisaDestination'],
    2: ['surname', 'name', 'dateOfBirth', 'citizenship', 'passportNumber', 'passportIssueDate', 'passportExpiryDate', 'iin', 'idNumber'],
    3: ['fullNameCyrillic', 'hasOtherNames', 'otherNames', 'gender', 'maritalStatus', 'cityOfBirth', 'countryOfBirth', 'hasOtherCitizenship', 'otherCitizenship', 'isPermanentResidentOtherCountry', 'permanentResidenceCountry', 'nationality'],
    4: ['hasOwnTravelPurpose', 'travelPurposeDescription', 'departureDate', 'returnDate', 'destination', 'hasInvitation', 'invitationFile', 'travelWithOthers', 'travelAsGroup', 'groupName', 'companions'],
    5: ['hasBeenToUSA', 'visitYear', 'visitPurpose', 'visitDuration', 'visitDurationType', 'visitVisaType', 'hasUSVisa', 'lastVisaDate', 'visaNumber', 'isSameVisaType', 'isSameCountry', 'hasVisaRejections', 'rejectionVisaType', 'rejectionDate', 'visaRejections', 'hasPreviousDS160', 'previousDS160File', 'ssn', 'taxId'],
    6: ['address', 'city', 'stateProvince', 'country', 'zipCode', 'phone', 'email', 'socialMediaLinks'],
    7: ['hasSpouse', 'spouseLastName', 'spouseFirstName', 'spouseMiddleName', 'spouseCityOfBirth', 'spouseCountryOfBirth', 'spouseDateOfBirth', 'spouseCitizenship', 'wasSpouseInUSA', 'spouseUSAEntryDate', 'spouseUSAStayDuration', 'spouseUSAStayDurationType', 'fatherSurname', 'fatherName', 'fatherDateOfBirth', 'isFatherDateOfBirthUnknown', 'isFatherInUSA', 'fatherUSAReason', 'motherSurname', 'motherName', 'motherDateOfBirth', 'isMotherDateOfBirthUnknown', 'isMotherInUSA', 'motherUSAReason', 'hasRelativesInUSA', 'relatives'],
    8: ['occupation', 'educationStatus', 'educationLocation', 'institutionName', 'hasJob', 'companyName', 'workAddress', 'workExperience', 'workPhone', 'position', 'workState', 'workZipCode', 'income', 'institutionState', 'institutionZipCode', 'workStateNA', 'workZipCodeNA', 'incomeNA', 'institutionStateNA', 'institutionZipCodeNA', 'lastCompletedEducation', 'otherEducation', 'isCurrentStudent', 'universityName', 'universityAddress', 'faculty', 'startDate', 'endDate'],
    9: ['visitedCountries', 'hasNotTraveled']
  };
  
  return stepFields[stepNumber] || [];
};

/**
 * Validation summary for debugging
 * Shows which fields are required for each step
 */
export const getValidationSummary = () => {
  const summary: { [stepNumber: number]: { totalFields: number; fieldNames: string[] } } = {};
  
  for (let step = 1; step <= 9; step++) {
    const fieldNames = getStepFieldNames(step as StepNumber);
    summary[step] = {
      totalFields: fieldNames.length,
      fieldNames
    };
  }
  
  return summary;
};

/**
 * Hook-style validation helper for React components
 * Returns validation state and helper functions
 */
export const useStepValidation = (stepNumber: StepNumber) => {
  const schema = getValidationSchemaForStep(stepNumber);
  
  return {
    schema,
    fieldNames: getStepFieldNames(stepNumber),
    validateFields: async (data: any, fields?: string[]) => {
      if (fields) {
        return await validateStepFields(data, stepNumber, fields);
      } else {
        try {
          await schema.validate(data, { abortEarly: false });
          return { isValid: true, errors: null };
        } catch (error: any) {
          return { isValid: false, errors: error.errors || error.message };
        }
      }
    }
  };
};