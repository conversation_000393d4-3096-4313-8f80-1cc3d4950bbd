import { Admin } from '../types/admin';

const ADMIN_SESSION_KEY = 'visa_admin_session';

export const setAdminSession = (admin: Omit<Admin, 'password'>) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(ADMIN_SESSION_KEY, JSON.stringify(admin));
  }
};

export const getAdminSession = (): Omit<Admin, 'password'> | null => {
  if (typeof window !== 'undefined') {
    const session = localStorage.getItem(ADMIN_SESSION_KEY);
    return session ? JSON.parse(session) : null;
  }
  return null;
};

export const clearAdminSession = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(ADMIN_SESSION_KEY);
  }
};

export const loginAdmin = async (username: string, password: string): Promise<Omit<Admin, 'password'>> => {
  const response = await fetch('/api/admin/auth', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  });

  const data = await response.json();

  if (!response.ok) {
    // Handle different types of blocking/errors
    if (data.blocked) {
      if (data.userBlocked) {
        throw new Error('Ваш аккаунт был заблокирован администратором. Обратитесь к руководству для получения доступа.');
      } else if (data.companyName) {
        throw new Error(`Компания "${data.companyName}" была заблокирована администратором системы. Доступ к панели администратора временно ограничен.`);
      } else {
        throw new Error(data.error || 'Доступ заблокирован');
      }
    }
    throw new Error(data.error || 'Ошибка входа в систему');
  }

  setAdminSession(data.admin);
  return data.admin;
};

export const logoutAdmin = () => {
  clearAdminSession();
};

export const isAdminAuthenticated = (): boolean => {
  return getAdminSession() !== null;
};

export const requireSuperAdmin = (admin: Omit<Admin, 'password'> | null): boolean => {
  return admin?.role === 'super_admin';
}; 