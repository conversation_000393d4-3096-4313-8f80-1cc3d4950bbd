import convert from 'heic-convert';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface HeicConversionResult {
  success: boolean;
  convertedPath?: string;
  originalPath: string;
  wasConverted: boolean;
  cleanup: () => Promise<void>;
  error?: string;
}

/**
 * Check if a file is HEIC/HEIF based on extension and/or content
 */
export const isHeicFile = (filePath: string): boolean => {
  const ext = path.extname(filePath).toLowerCase();
  return ['.heic', '.heif'].includes(ext);
};

/**
 * Check if a file is HEIC/HEIF based on file signature
 */
export const isHeicFileBySignature = async (filePath: string): Promise<boolean> => {
  try {
    if (!fs.existsSync(filePath)) {
      return false;
    }

    // Read first 12 bytes to check HEIC signature
    const fd = fs.openSync(filePath, 'r');
    const buffer = Buffer.alloc(12);
    fs.readSync(fd, buffer, 0, 12, 0);
    fs.closeSync(fd);

    // Check for HEIC signatures
    const heicSignatures = [
      [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63], // HEIC
      [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x6D, 0x69, 0x66, 0x31]  // HEIF
    ];

    return heicSignatures.some(signature => 
      signature.every((byte, index) => buffer[index] === byte)
    );
  } catch (error) {
    console.warn('Error checking HEIC signature:', error);
    return false;
  }
};

/**
 * Convert HEIC file to JPEG
 */
export const convertHeicToJpeg = async (
  heicPath: string,
  quality: number = 0.8
): Promise<{ jpegPath: string; cleanup: () => Promise<void> }> => {
  try {
    console.log('Converting HEIC to JPEG:', heicPath);

    // Read HEIC file
    const heicBuffer = fs.readFileSync(heicPath);
    console.log('HEIC file size:', heicBuffer.length, 'bytes');

    // Convert to JPEG
    const jpegBuffer = await convert({
      buffer: heicBuffer,
      format: 'JPEG',
      quality: quality
    });

    console.log('Converted JPEG size:', jpegBuffer.length, 'bytes');

    // Generate unique filename for converted JPEG
    const tempDir = path.dirname(heicPath);
    const uniqueId = uuidv4();
    const jpegPath = path.join(tempDir, `converted-${uniqueId}.jpg`);

    // Write JPEG file
    fs.writeFileSync(jpegPath, jpegBuffer);
    console.log('JPEG file written to:', jpegPath);

    // Return path and cleanup function
    return {
      jpegPath,
      cleanup: async () => {
        try {
          if (fs.existsSync(jpegPath)) {
            fs.unlinkSync(jpegPath);
            console.log('Cleaned up converted JPEG file:', jpegPath);
          }
        } catch (cleanupError) {
          console.warn('Failed to cleanup converted JPEG file:', cleanupError);
        }
      }
    };

  } catch (error) {
    console.error('Error converting HEIC to JPEG:', error);
    throw new Error(`Failed to convert HEIC to JPEG: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Process a file, converting HEIC to JPEG if necessary
 * This is the main function to use in the OCR pipeline
 */
export const processHeicFile = async (filePath: string): Promise<HeicConversionResult> => {
  const cleanupFunctions: (() => Promise<void>)[] = [];

  try {
    // Check if file is HEIC by extension or signature
    const isHeicByExtension = isHeicFile(filePath);
    const isHeicBySignature = await isHeicFileBySignature(filePath);
    
    if (!isHeicByExtension && !isHeicBySignature) {
      // Not a HEIC file, return original path
      return {
        success: true,
        convertedPath: filePath,
        originalPath: filePath,
        wasConverted: false,
        cleanup: async () => {
          // No cleanup needed for non-converted files
        }
      };
    }

    console.log('HEIC file detected, converting to JPEG...');

    // Convert HEIC to JPEG
    const { jpegPath, cleanup } = await convertHeicToJpeg(filePath);
    cleanupFunctions.push(cleanup);

    return {
      success: true,
      convertedPath: jpegPath,
      originalPath: filePath,
      wasConverted: true,
      cleanup: async () => {
        // Run all cleanup functions
        for (const cleanupFn of cleanupFunctions) {
          await cleanupFn();
        }
      }
    };

  } catch (error) {
    console.error('Error processing HEIC file:', error);
    
    // Cleanup any partial conversions
    for (const cleanupFn of cleanupFunctions) {
      try {
        await cleanupFn();
      } catch (cleanupError) {
        console.warn('Error during cleanup:', cleanupError);
      }
    }

    return {
      success: false,
      originalPath: filePath,
      wasConverted: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      cleanup: async () => {
        // No cleanup needed for failed conversions
      }
    };
  }
};

/**
 * Get optimal JPEG quality based on file size
 */
export const getOptimalJpegQuality = (fileSizeBytes: number): number => {
  // Adjust quality based on file size to manage memory usage
  if (fileSizeBytes > 5 * 1024 * 1024) { // > 5MB
    return 0.6; // Lower quality for large files
  } else if (fileSizeBytes > 2 * 1024 * 1024) { // > 2MB
    return 0.7;
  } else {
    return 0.8; // Higher quality for smaller files
  }
};

/**
 * Convert HEIC with optimal settings for serverless environment
 */
export const convertHeicOptimized = async (
  heicPath: string
): Promise<{ jpegPath: string; cleanup: () => Promise<void> }> => {
  try {
    // Get file size for optimization
    const stats = fs.statSync(heicPath);
    const quality = getOptimalJpegQuality(stats.size);
    
    console.log(`Converting HEIC with optimized quality: ${quality} (file size: ${stats.size} bytes)`);
    
    return await convertHeicToJpeg(heicPath, quality);
  } catch (error) {
    console.error('Error in optimized HEIC conversion:', error);
    throw error;
  }
};
