// Cross-step validation utilities to ensure data consistency across form steps

import { Step3Data, Step4Data, Step5Data, Step7Data } from './types';

export interface CrossStepValidationError {
  step: number;
  field: string;
  message: string;
}

// Validate consistency between marital status and spouse information
export const validateMaritalStatusConsistency = (
  step3Data: Step3Data, 
  step7Data: Step7Data
): CrossStepValidationError[] => {
  const errors: CrossStepValidationError[] = [];

  // If married in step 3, must have spouse info in step 7
  if (step3Data.maritalStatus === 'married' && !step7Data.hasSpouse) {
    errors.push({
      step: 7,
      field: 'hasSpouse',
      message: 'Вы указали, что состоите в браке, но не предоставили информацию о супруге'
    });
  }

  // If not married in step 3, shouldn't have spouse info in step 7
  if (step3Data.maritalStatus !== 'married' && step7Data.hasSpouse) {
    errors.push({
      step: 7,
      field: 'hasSpouse',
      message: 'Вы указали информацию о супруге, но не состоите в браке'
    });
  }

  // If has spouse in step 7, validate required spouse fields
  if (step7Data.hasSpouse) {
    if (!step7Data.spouseFirstName?.trim()) {
      errors.push({
        step: 7,
        field: 'spouseFirstName',
        message: 'Имя супруга обязательно'
      });
    }
    if (!step7Data.spouseLastName?.trim()) {
      errors.push({
        step: 7,
        field: 'spouseLastName',
        message: 'Фамилия супруга обязательна'
      });
    }
    if (!step7Data.spouseDateOfBirth) {
      errors.push({
        step: 7,
        field: 'spouseDateOfBirth',
        message: 'Дата рождения супруга обязательна'
      });
    }
  }

  return errors;
};

// Validate travel dates consistency
export const validateTravelDatesConsistency = (step4Data: Step4Data): CrossStepValidationError[] => {
  const errors: CrossStepValidationError[] = [];

  if (step4Data.departureDate && step4Data.returnDate) {
    const departureDate = new Date(step4Data.departureDate);
    const returnDate = new Date(step4Data.returnDate);

    // Return date must be after departure date
    if (returnDate <= departureDate) {
      errors.push({
        step: 4,
        field: 'returnDate',
        message: 'Дата возвращения должна быть после даты вылета'
      });
    }

    // Check for reasonable trip duration (not more than 6 months)
    const diffTime = Math.abs(returnDate.getTime() - departureDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 180) {
      errors.push({
        step: 4,
        field: 'returnDate',
        message: 'Продолжительность поездки не должна превышать 6 месяцев'
      });
    }

    // Check if departure is too far in the future (more than 1 year)
    const now = new Date();
    const maxFutureDate = new Date();
    maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 1);

    if (departureDate > maxFutureDate) {
      errors.push({
        step: 4,
        field: 'departureDate',
        message: 'Дата вылета не может быть более чем через год'
      });
    }
  }

  return errors;
};

// Validate visa history consistency
export const validateVisaHistoryConsistency = (step5Data: Step5Data): CrossStepValidationError[] => {
  const errors: CrossStepValidationError[] = [];

  // If has US visa, validate visa details
  if (step5Data.hasUSVisa) {
    if (!step5Data.visaNumber?.trim()) {
      errors.push({
        step: 5,
        field: 'visaNumber',
        message: 'Номер визы обязателен, если у вас есть виза США'
      });
    }

    if (!step5Data.lastVisaDate) {
      errors.push({
        step: 5,
        field: 'lastVisaDate',
        message: 'Дата последней визы обязательна, если у вас есть виза США'
      });
    }
  }

  // If has been to USA, validate visit details
  if (step5Data.hasBeenToUSA) {
    if (!step5Data.visitYear) {
      errors.push({
        step: 5,
        field: 'visitYear',
        message: 'Год поездки обязателен, если вы были в США'
      });
    }

    if (!step5Data.visitPurpose?.trim()) {
      errors.push({
        step: 5,
        field: 'visitPurpose',
        message: 'Цель поездки обязательна, если вы были в США'
      });
    }
  }

  // If has visa rejections, validate rejection details
  if (step5Data.hasVisaRejections) {
    if (!step5Data.rejectionVisaType?.trim()) {
      errors.push({
        step: 5,
        field: 'rejectionVisaType',
        message: 'Тип визы обязателен при отказе в визе'
      });
    }

    if (!step5Data.rejectionDate) {
      errors.push({
        step: 5,
        field: 'rejectionDate',
        message: 'Дата отказа обязательна при отказе в визе'
      });
    }
  }

  return errors;
};

// Validate age consistency across family members
export const validateFamilyAgeConsistency = (
  step3Data: Step3Data, 
  step7Data: Step7Data
): CrossStepValidationError[] => {
  const errors: CrossStepValidationError[] = [];

  // Calculate applicant's age (assuming dateOfBirth is from step 2)
  // For now, we'll skip this validation as we don't have step 2 data
  // This could be enhanced when we have access to all step data

  // Validate parent ages if provided
  if (step7Data.fatherDateOfBirth) {
    const fatherAge = calculateAge(new Date(step7Data.fatherDateOfBirth));
    if (fatherAge < 30) {
      errors.push({
        step: 7,
        field: 'fatherDateOfBirth',
        message: 'Возраст отца должен быть не менее 30 лет'
      });
    }
    if (fatherAge > 120) {
      errors.push({
        step: 7,
        field: 'fatherDateOfBirth',
        message: 'Возраст отца не может превышать 120 лет'
      });
    }
  }

  if (step7Data.motherDateOfBirth) {
    const motherAge = calculateAge(new Date(step7Data.motherDateOfBirth));
    if (motherAge < 25) {
      errors.push({
        step: 7,
        field: 'motherDateOfBirth',
        message: 'Возраст матери должен быть не менее 25 лет'
      });
    }
    if (motherAge > 120) {
      errors.push({
        step: 7,
        field: 'motherDateOfBirth',
        message: 'Возраст матери не может превышать 120 лет'
      });
    }
  }

  return errors;
};

// Helper function to calculate age
const calculateAge = (birthDate: Date): number => {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

// Main function to validate all cross-step consistency
export const validateAllCrossStepConsistency = (
  step3Data?: Step3Data,
  step4Data?: Step4Data,
  step5Data?: Step5Data,
  step7Data?: Step7Data
): CrossStepValidationError[] => {
  const allErrors: CrossStepValidationError[] = [];

  // Validate marital status consistency
  if (step3Data && step7Data) {
    allErrors.push(...validateMaritalStatusConsistency(step3Data, step7Data));
  }

  // Validate travel dates
  if (step4Data) {
    allErrors.push(...validateTravelDatesConsistency(step4Data));
  }

  // Validate visa history
  if (step5Data) {
    allErrors.push(...validateVisaHistoryConsistency(step5Data));
  }

  // Validate family age consistency
  if (step3Data && step7Data) {
    allErrors.push(...validateFamilyAgeConsistency(step3Data, step7Data));
  }

  return allErrors;
};

// Function to group errors by step for display
export const groupErrorsByStep = (errors: CrossStepValidationError[]): Record<number, CrossStepValidationError[]> => {
  return errors.reduce((acc, error) => {
    if (!acc[error.step]) {
      acc[error.step] = [];
    }
    acc[error.step].push(error);
    return acc;
  }, {} as Record<number, CrossStepValidationError[]>);
};