// Component to display cross-step validation errors

import React from 'react';
import { CrossStepValidationError } from '../utils/crossStepValidation';

interface CrossStepValidationDisplayProps {
  errors: CrossStepValidationError[];
  currentStep?: number;
  onNavigateToStep?: (step: number) => void;
}

const CrossStepValidationDisplay: React.FC<CrossStepValidationDisplayProps> = ({
  errors,
  currentStep,
  onNavigateToStep
}) => {
  if (errors.length === 0) {
    return null;
  }

  // Group errors by step
  const errorsByStep = errors.reduce((acc, error) => {
    if (!acc[error.step]) {
      acc[error.step] = [];
    }
    acc[error.step].push(error);
    return acc;
  }, {} as Record<number, CrossStepValidationError[]>);

  const getStepName = (step: number): string => {
    const stepNames: Record<number, string> = {
      1: 'Выбор страны',
      2: 'Документы',
      3: 'Личная информация',
      4: 'Цель поездки',
      5: 'Визовая история',
      6: 'Контактная информация',
      7: 'Семейная информация',
      8: 'Образование и работа',
      9: 'История поездок'
    };
    return stepNames[step] || `Шаг ${step}`;
  };

  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg 
            className="h-5 w-5 text-red-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fillRule="evenodd" 
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            Обнаружены несоответствия в данных
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p className="mb-3">
              Пожалуйста, исправьте следующие несоответствия между шагами:
            </p>
            
            {Object.entries(errorsByStep).map(([step, stepErrors]) => (
              <div key={step} className="mb-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-red-800">
                    {getStepName(parseInt(step))}
                  </h4>
                  {onNavigateToStep && parseInt(step) !== currentStep && (
                    <button
                      onClick={() => onNavigateToStep(parseInt(step))}
                      className="text-xs text-red-600 hover:text-red-800 underline"
                    >
                      Перейти к шагу
                    </button>
                  )}
                </div>
                <ul className="mt-1 list-disc list-inside space-y-1">
                  {stepErrors.map((error, index) => (
                    <li key={index} className="text-sm">
                      {error.message}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          
          <div className="mt-3 text-xs text-red-600">
            <p>
              💡 Совет: Убедитесь, что информация во всех шагах согласована. 
              Например, если вы состоите в браке, обязательно заполните информацию о супруге.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CrossStepValidationDisplay;