import React, { useState, useEffect, useRef } from 'react';
import { Field, ErrorMessage, FormikErrors, FormikTouched } from 'formik';
import { VisaFormData } from '../../utils/types';
import { COUNTRIES } from '../../constants/countries';
import { useAutocompleteDetection } from '../../hooks/useAutocompleteDetection';
import FileUpload from '../FileUpload';
import { ExtractedDocumentData } from '../../utils/ocr';

interface Step2FieldsProps {
  values: VisaFormData;
  setFieldValue: (field: string, value: any) => void;
  errors: FormikErrors<VisaFormData>;
  touched: FormikTouched<VisaFormData>;
  uploadedFiles?: Record<string, string>;
  setUploadedFiles?: (files: Record<string, string>) => void;
  onFormDataUpdate?: (data: Partial<VisaFormData>) => void;
}

const Step2Fields: React.FC<Step2FieldsProps> = ({
  values,
  setFieldValue,
  errors,
  touched,
  uploadedFiles = {},
  setUploadedFiles,
  onFormDataUpdate
}) => {
  const [isDocumentUploaded, setIsDocumentUploaded] = useState(false);
  const setFieldValueRef = useRef<((field: string, value: any) => void) | null>(null);

  // Fields that are commonly autocompleted
  const autocompleteFields = ['surname', 'name', 'passportNumber', 'iin', 'idNumber'];
  
  // Store setFieldValue in ref for access in callbacks
  setFieldValueRef.current = setFieldValue;

  // Check if any documents are uploaded
  useEffect(() => {
    const hasUploadedFiles = Object.keys(uploadedFiles).length > 0;
    setIsDocumentUploaded(hasUploadedFiles);
  }, [uploadedFiles]);

  // Handle document extraction from OCR
  const handleDocumentExtract = (documentType: string, data: ExtractedDocumentData, filePath?: string) => {
    console.log('Step2Fields: Document extracted:', { documentType, data, filePath });

    if (setFieldValueRef.current) {
      // Update form fields with extracted data
      Object.entries(data).forEach(([key, value]) => {
        setFieldValueRef.current!(key, value);
      });
    }

    // Update uploaded files
    if (setUploadedFiles) {
      const newUploadedFiles = {
        ...uploadedFiles,
        [documentType]: filePath || 'uploaded',
      };
      setUploadedFiles(newUploadedFiles);
    }

    // Notify parent about form data update
    if (onFormDataUpdate) {
      onFormDataUpdate(data);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Загрузите документы</h2>
      <p className="text-gray-600 mb-4">
        Пожалуйста, загрузите скан или фото вашего паспорта и удостоверения личности.
        Мы автоматически извлечем данные, которые вы сможете проверить и отредактировать.
      </p>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">Требования для продолжения</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Заполнить все обязательные поля (*)</li>
              <li>• Указать хотя бы один идентификатор: номер паспорта, ИИН или номер удостоверения</li>
              <li>• Загрузить хотя бы один документ (паспорт или удостоверение)</li>
              <li>• Можно указать несколько идентификаторов для более полной информации</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <FileUpload
          label="Загрузите скан/фото паспорта"
          name="passport"
          onExtract={(data, filePath) => handleDocumentExtract('passport', data, filePath)}
        />

        <FileUpload
          label="Загрузите скан/фото удостоверения личности"
          name="idCard"
          onExtract={(data, filePath) => handleDocumentExtract('idCard', data, filePath)}
        />
      </div>

      {!isDocumentUploaded && (
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700 font-medium">
                Совет: Загрузите документ для автоматического заполнения
              </p>
              <p className="text-sm text-blue-600 mt-1">
                Загрузите паспорт или удостоверение личности, чтобы мы автоматически извлекли данные. Или заполните поля вручную.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-800 mb-4">
          Проверьте и отредактируйте данные
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="mb-4">
            <label htmlFor="surname" className="block text-gray-700 font-medium mb-2">
              Фамилия (Surname) * <span className="text-sm text-gray-500">только латиница</span>
            </label>
            <Field
              type="text"
              id="surname"
              name="surname"
              className="form-input"
              placeholder="Ivanov"
            />
            {touched.surname && errors.surname && (
              <div className="text-red-500 text-sm mt-1">{errors.surname}</div>
            )}
          </div>

          <div className="mb-4">
            <label htmlFor="name" className="block text-gray-700 font-medium mb-2">
              Имя (Name) * <span className="text-sm text-gray-500">только латиница</span>
            </label>
            <Field
              type="text"
              id="name"
              name="name"
              className="form-input"
              placeholder="Ivan"
            />
            {touched.name && errors.name && (
              <div className="text-red-500 text-sm mt-1">{errors.name}</div>
            )}
          </div>

          <div className="mb-4">
            <label htmlFor="dateOfBirth" className="block text-gray-700 font-medium mb-2">
              Дата рождения *
            </label>
            <Field
              type="date"
              id="dateOfBirth"
              name="dateOfBirth"
              className="form-input"
            />
            {touched.dateOfBirth && errors.dateOfBirth && (
              <div className="text-red-500 text-sm mt-1">{errors.dateOfBirth}</div>
            )}
          </div>

          <div className="mb-4">
            <label htmlFor="citizenship" className="block text-gray-700 font-medium mb-2">
              Гражданство *
            </label>
            <Field
              as="select"
              id="citizenship"
              name="citizenship"
              className="form-input"
            >
              <option value="">Выберите страну...</option>
              {COUNTRIES.map((country) => (
                <option key={country.code} value={country.name}>
                  {country.name}
                </option>
              ))}
            </Field>
            {touched.citizenship && errors.citizenship && (
              <div className="text-red-500 text-sm mt-1">{errors.citizenship}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step2Fields;
