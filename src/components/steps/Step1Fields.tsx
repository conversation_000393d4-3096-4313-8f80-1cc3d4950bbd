import React, { useState, useEffect } from 'react';
import { Field, ErrorMessage, FormikErrors, FormikTouched } from 'formik';
import { VisaFormData } from '../../utils/types';

interface Step1FieldsProps {
  values: VisaFormData;
  setFieldValue: (field: string, value: any) => void;
  errors: FormikErrors<VisaFormData>;
  touched: FormikTouched<VisaFormData>;
}

const countryOptions = [
  { value: '', label: 'Выберите страну...' },
  { value: 'usa', label: 'США' },
  { value: 'canada', label: 'Канада' },
  { value: 'uk', label: 'Великобритания' },
  { value: 'australia', label: 'Австралия' },
  { value: 'schengen', label: 'Шенген' },
  { value: 'other', label: 'Другое' },
];

const Step1Fields: React.FC<Step1FieldsProps> = ({ values, setFieldValue }) => {
  const [selectedCountry, setSelectedCountry] = useState(values.visaDestination || '');

  // Update selectedCountry when values.visaDestination changes
  useEffect(() => {
    if (values.visaDestination !== selectedCountry) {
      setSelectedCountry(values.visaDestination);
    }
  }, [values.visaDestination, selectedCountry]);

  return (
    <div className="text-center">
      <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
        Добро пожаловать в сервис заполнения анкеты на визу
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Для начала выберите страну, для которой вы хотите получить визу.
      </p>

      <div className="max-w-md mx-auto">
        <div className="mb-6">
          <label htmlFor="visaDestination" className="block text-gray-700 font-medium mb-2">
            Страна назначения
          </label>
          <Field
            as="select"
            id="visaDestination"
            name="visaDestination"
            className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
              const value = e.target.value;
              setFieldValue('visaDestination', value);
              setSelectedCountry(value);
              
              // Clear otherVisaDestination if not selecting "other"
              if (value !== 'other') {
                setFieldValue('otherVisaDestination', '');
              }
            }}
          >
            {countryOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Field>
          <ErrorMessage
            name="visaDestination"
            component="div"
            className="mt-2 text-red-600 text-sm"
          />
        </div>

        {selectedCountry === 'other' && (
          <div className="mt-4">
            <Field
              type="text"
              id="otherVisaDestination"
              name="otherVisaDestination"
              placeholder="Введите название страны"
              className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
            />
            <ErrorMessage
              name="otherVisaDestination"
              component="div"
              className="mt-2 text-red-600 text-sm"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Step1Fields;
