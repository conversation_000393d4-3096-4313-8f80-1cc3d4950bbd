import React, { forwardRef, useImperativeHandle, useRef, useEffect, useState } from 'react';
import { Formik, Form, Field, ErrorMessage, FormikProps } from 'formik';
import { getStepValidation } from '../utils/validationHelpers';
import { Step3Data } from '../utils/types';
import { COUNTRIES, CITIES_BY_COUNTRY } from '../constants/countries';
import { useAutocompleteDetection } from '../hooks/useAutocompleteDetection';

export interface Step3Ref {
  submitForm: () => void;
  isValid: boolean;
}

interface Step3Props {
  initialValues: Step3Data;
  onSubmit: (values: Step3Data) => void;
}

const maritalStatusOptions = [
  { value: '', label: 'Выберите...' },
  { value: 'single', label: 'Холост/Не замужем' },
  { value: 'married', label: 'Женат/Замужем' },
  { value: 'divorced', label: 'В разводе' },
  { value: 'widowed', label: 'Вдовец/Вдова' },
];

const Step3_PersonalInfo = forwardRef<Step3Ref, Step3Props>(({ initialValues, onSubmit }, ref) => {
  const formikRef = useRef<FormikProps<Step3Data>>(null);
  const [forceUpdate, setForceUpdate] = useState(0);
  const validationSchema = getStepValidation(3);

  // Fields that are commonly autocompleted
  const autocompleteFields = [
    'fullNameCyrillic',
    'cityOfBirth',
    'countryOfBirth',
    'nationality'
  ];

  // Use autocomplete detection hook
  useAutocompleteDetection(formikRef, autocompleteFields);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      console.log('[Step3 Submit] submitForm called');
      if (formikRef.current) {
        formikRef.current.submitForm();
      }
    },
    get isValid() {
      const formikValid = formikRef.current?.isValid ?? false;
      console.log('[Step3 isValid] Real-time check:', {
        formikValid,
        formikValues: formikRef.current?.values,
        formikErrors: formikRef.current?.errors,
      });
      return formikValid;
    }
  }), [forceUpdate]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Личная информация</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, заполните информацию о себе. Все поля обязательны для заполнения.
      </p>

      <Formik
        innerRef={formikRef}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        validateOnMount={false}
        validateOnChange={true}
        validateOnBlur={true}
        enableReinitialize={true}
      >
        {({ values, isValid: formikIsValid, setFieldValue }) => {
          // Принудительно обновляем useImperativeHandle при изменении Formik
          useEffect(() => {
            console.log('[Step3 Formik] State changed:', {
              formikIsValid,
              values,
              errors: formikRef.current?.errors,
            });
            setForceUpdate(prev => prev + 1);
          }, [formikIsValid, values]);

          return (
          <Form>
            <div className="grid grid-cols-1 gap-4">
              <div className="mb-4">
                <label htmlFor="fullNameCyrillic" className="block text-gray-700 font-medium mb-2">
                  Полное имя на родном языке (кириллица) <span className="text-red-500">*</span>
                  <span className="text-sm text-gray-500 block">только кириллица</span>
                </label>
                <Field
                  type="text"
                  id="fullNameCyrillic"
                  name="fullNameCyrillic"
                  className="form-input"
                  placeholder="Иванов Иван Иванович"
                />
                <ErrorMessage
                  name="fullNameCyrillic"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Field
                    type="checkbox"
                    id="hasOtherNames"
                    name="hasOtherNames"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setFieldValue('hasOtherNames', e.target.checked);
                      if (!e.target.checked) {
                        setFieldValue('otherNames', '');
                      }
                    }}
                  />
                  <label htmlFor="hasOtherNames" className="ml-2 block text-gray-700 font-medium">
                    Использовали ли вы другие имена?
                  </label>
                </div>
                {values.hasOtherNames && (
                  <div className="mt-2">
                    <Field
                      type="text"
                      id="otherNames"
                      name="otherNames"
                      placeholder="Введите другие имена/фамилии"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="otherNames"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 font-medium mb-2">Пол <span className="text-red-500">*</span></label>
                <div className="flex space-x-4">
                  <div className="flex items-center">
                    <Field
                      type="radio"
                      id="male"
                      name="gender"
                      value="male"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <label htmlFor="male" className="ml-2 block text-gray-700">
                      Мужской
                    </label>
                  </div>
                  <div className="flex items-center">
                    <Field
                      type="radio"
                      id="female"
                      name="gender"
                      value="female"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <label htmlFor="female" className="ml-2 block text-gray-700">
                      Женский
                    </label>
                  </div>
                </div>
                <ErrorMessage
                  name="gender"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="maritalStatus" className="block text-gray-700 font-medium mb-2">
                  Семейное положение <span className="text-red-500">*</span>
                </label>
                <Field
                  as="select"
                  id="maritalStatus"
                  name="maritalStatus"
                  className="form-input"
                >
                  {maritalStatusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Field>
                <ErrorMessage
                  name="maritalStatus"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="countryOfBirth" className="block text-gray-700 font-medium mb-2">
                  Страна рождения <span className="text-red-500">*</span>
                </label>
                <Field
                  as="select"
                  id="countryOfBirth"
                  name="countryOfBirth"
                  className="form-input"
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    setFieldValue('countryOfBirth', e.target.value);
                    // Reset city when country changes
                    setFieldValue('cityOfBirth', '');
                  }}
                >
                  <option value="">Выберите страну...</option>
                  {COUNTRIES.map((country) => (
                    <option key={country} value={country}>
                      {country}
                    </option>
                  ))}
                </Field>
                <ErrorMessage
                  name="countryOfBirth"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="cityOfBirth" className="block text-gray-700 font-medium mb-2">
                  Город рождения <span className="text-red-500">*</span>
                </label>
                {values.countryOfBirth && CITIES_BY_COUNTRY[values.countryOfBirth as keyof typeof CITIES_BY_COUNTRY] ? (
                  <Field
                    as="select"
                    id="cityOfBirth"
                    name="cityOfBirth"
                    className="form-input"
                  >
                    <option value="">Выберите город...</option>
                    {CITIES_BY_COUNTRY[values.countryOfBirth as keyof typeof CITIES_BY_COUNTRY].map((city) => (
                      <option key={city} value={city}>
                        {city}
                      </option>
                    ))}
                  </Field>
                ) : (
                  <Field
                    type="text"
                    id="cityOfBirth"
                    name="cityOfBirth"
                    className="form-input"
                    placeholder="Введите город рождения"
                  />
                )}
                <ErrorMessage
                  name="cityOfBirth"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Field
                    type="checkbox"
                    id="hasOtherCitizenship"
                    name="hasOtherCitizenship"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setFieldValue('hasOtherCitizenship', e.target.checked);
                      if (!e.target.checked) {
                        setFieldValue('otherCitizenship', '');
                      }
                    }}
                  />
                  <label htmlFor="hasOtherCitizenship" className="ml-2 block text-gray-700 font-medium">
                    Есть ли другое гражданство?
                  </label>
                </div>
                {values.hasOtherCitizenship && (
                  <div className="mt-2">
                    <Field
                      as="select"
                      id="otherCitizenship"
                      name="otherCitizenship"
                      className="form-input"
                    >
                      <option value="">Выберите страну...</option>
                      {COUNTRIES.map((country) => (
                        <option key={country} value={country}>
                          {country}
                        </option>
                      ))}
                    </Field>
                    <ErrorMessage
                      name="otherCitizenship"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}
              </div>

              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Field
                    type="checkbox"
                    id="isPermanentResidentOtherCountry"
                    name="isPermanentResidentOtherCountry"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setFieldValue('isPermanentResidentOtherCountry', e.target.checked);
                      if (!e.target.checked) {
                        setFieldValue('permanentResidenceCountry', '');
                      }
                    }}
                  />
                  <label htmlFor="isPermanentResidentOtherCountry" className="ml-2 block text-gray-700 font-medium">
                    Являетесь ли вы постоянным жителем другой страны?
                  </label>
                </div>
                {values.isPermanentResidentOtherCountry && (
                  <div className="mt-2">
                    <Field
                      as="select"
                      id="permanentResidenceCountry"
                      name="permanentResidenceCountry"
                      className="form-input"
                    >
                      <option value="">Выберите страну...</option>
                      {COUNTRIES.map((country) => (
                        <option key={country} value={country}>
                          {country}
                        </option>
                      ))}
                    </Field>
                    <ErrorMessage
                      name="permanentResidenceCountry"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}
              </div>

              <div className="mb-4">
                <label htmlFor="nationality" className="block text-gray-700 font-medium mb-2">
                  Национальность (гражданство) <span className="text-red-500">*</span>
                </label>
                <Field
                  as="select"
                  id="nationality"
                  name="nationality"
                  className="form-input"
                >
                  <option value="">Выберите страну...</option>
                  {COUNTRIES.map((country) => (
                    <option key={country} value={country}>
                      {country}
                    </option>
                  ))}
                </Field>
                <ErrorMessage
                  name="nationality"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>


            </div>

            {/* Hidden submit button - will be triggered by StepWrapper */}
            <button type="submit" style={{ display: 'none' }} />
          </Form>
          );
        }}
      </Formik>
    </div>
  );
});

Step3_PersonalInfo.displayName = 'Step3_PersonalInfo';

export default Step3_PersonalInfo;