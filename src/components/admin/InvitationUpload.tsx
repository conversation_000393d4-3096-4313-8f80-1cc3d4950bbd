import React, { useState, useRef } from 'react';
import { useToast } from '../../hooks/useToast';

interface InvitationUploadProps {
  applicationId: string;
  currentFileUrl?: string;
  onUploadSuccess: (fileUrl: string) => void;
  disabled?: boolean;
}

const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword',
  'image/png',
  'image/jpeg',
  'image/jpg'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export default function InvitationUpload({ 
  applicationId, 
  currentFileUrl, 
  onUploadSuccess, 
  disabled = false 
}: InvitationUploadProps) {
  const { success, error } = useToast();
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return 'Неподдерживаемый формат файла. Разрешены: PDF, DOCX, DOC, PNG, JPG';
    }
    
    if (file.size > MAX_FILE_SIZE) {
      return 'Файл слишком большой. Максимальный размер: 10MB';
    }
    
    return null;
  };

  const uploadFile = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      error(validationError);
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('invitation', file);
      formData.append('applicationId', applicationId);

      const response = await fetch('/api/admin/upload-invitation', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        success('Приглашение успешно загружено');
        onUploadSuccess(result.fileUrl);
      } else {
        error(result.error || 'Ошибка при загрузке файла');
      }
    } catch (err) {
      console.error('Upload error:', err);
      error('Ошибка при загрузке файла');
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadFile(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled || uploading) return;
    
    const file = e.dataTransfer.files[0];
    if (file) {
      uploadFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !uploading) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const openFileDialog = () => {
    if (!disabled && !uploading) {
      fileInputRef.current?.click();
    }
  };

  const getFileIcon = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'png':
      case 'jpg':
      case 'jpeg':
        return '🖼️';
      default:
        return '📎';
    }
  };

  const getFileName = (url: string) => {
    return url.split('/').pop() || 'Приглашение';
  };

  return (
    <div className="space-y-3">
      {/* Current file display */}
      {currentFileUrl && (
        <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{getFileIcon(currentFileUrl)}</span>
            <span className="text-sm font-medium text-green-800">
              Приглашение загружено
            </span>
          </div>
          <a
            href={currentFileUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-3 py-1 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded-md hover:bg-green-200 transition-colors"
          >
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            Скачать
          </a>
        </div>
      )}

      {/* Upload area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${disabled 
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
            : dragOver 
              ? 'border-blue-400 bg-blue-50' 
              : 'border-gray-300 bg-gray-50 hover:bg-gray-100 cursor-pointer'
          }
          ${uploading ? 'opacity-50 pointer-events-none' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled || uploading}
        />

        {uploading ? (
          <div className="space-y-2">
            <div className="inline-flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="text-sm text-blue-600">Загрузка файла...</span>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-3xl">📎</div>
            <div className="text-sm font-medium text-gray-700">
              {disabled 
                ? 'Загрузка приглашения недоступна' 
                : currentFileUrl 
                  ? 'Нажмите чтобы заменить приглашение'
                  : 'Нажмите или перетащите файл приглашения'
              }
            </div>
            {!disabled && (
              <div className="text-xs text-gray-500">
                Поддерживаемые форматы: PDF, DOCX, DOC, PNG, JPG (до 10MB)
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 