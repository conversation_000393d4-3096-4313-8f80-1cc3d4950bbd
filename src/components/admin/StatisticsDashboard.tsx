import { useState, useEffect, useCallback } from 'react';
import { AnalyticsData, StatisticsDashboardProps } from '../../types/admin';
import { useTranslation } from '../../utils/localization';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer
} from 'recharts';
import { useDateFilter } from '../../contexts/DateFilterContext';

// Safe date formatting function
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) {
    return 'Дата не указана';
  }
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Неверная дата';
    }
    
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Ошибка даты';
  }
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#ff7300'];

// Enhanced country flags and icons mapping
const COUNTRY_FLAGS: { [key: string]: string } = {
  'USA': '🇺🇸',
  'US': '🇺🇸',
  'UK': '🇬🇧',
  'Schengen': '🇪🇺',
  'China': '🇨🇳',
  'Canada': '🇨🇦',
  'Australia': '🇦🇺',
  'Germany': '🇩🇪',
  'France': '🇫🇷',
  'Italy': '🇮🇹',
  'Spain': '🇪🇸',
  'Netherlands': '🇳🇱',
  'Belgium': '🇧🇪',
  'Austria': '🇦🇹',
  'Switzerland': '🇨🇭',
  'Portugal': '🇵🇹',
  'Poland': '🇵🇱',
  'Czech Republic': '🇨🇿',
  'Hungary': '🇭🇺',
  'Greece': '🇬🇷',
  'Sweden': '🇸🇪',
  'Norway': '🇳🇴',
  'Denmark': '🇩🇰',
  'Finland': '🇫🇮',
  'Japan': '🇯🇵',
  'South Korea': '🇰🇷',
  'Singapore': '🇸🇬',
  'New Zealand': '🇳🇿',
  'Brazil': '🇧🇷',
  'Mexico': '🇲🇽',
  'India': '🇮🇳',
  'Thailand': '🇹🇭',
  'UAE': '🇦🇪',
  'Turkey': '🇹🇷',
  'Israel': '🇮🇱',
  'South Africa': '🇿🇦',
  'EU': '🇪🇺',
  'CN': '🇨🇳'
};

// Country display names - using translation system

const StatisticsDashboard: React.FC<StatisticsDashboardProps> = ({ analytics: initialAnalytics, onRefresh, admin, selectedGlobalCompany, metricsLoading = false }) => {
  const { t } = useTranslation();
  const [analytics, setAnalytics] = useState<AnalyticsData>(initialAnalytics);
  const { dateRange } = useDateFilter();
  const [selectedCountry, setSelectedCountry] = useState<string>('');

  // Fetch analytics data with filters
  const fetchAnalytics = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (dateRange.from) params.append('dateFrom', dateRange.from);
      if (dateRange.to) params.append('dateTo', dateRange.to);
      if (selectedCountry) params.append('country', selectedCountry);
      if (admin?.role === 'super_admin' && selectedGlobalCompany) {
        params.append('companyId', selectedGlobalCompany);
      } else if (admin?.companyId) {
        params.append('companyId', admin.companyId);
      }
      const response = await fetch(`/api/admin/analytics?${params}`);
      const data = await response.json();
      if (response.ok) {
        setAnalytics(data);
      } else {
        console.error(t('statistics.apiError'), data.error);
      }
    } catch (error) {
      console.error('Ошибка при загрузке аналитики:', error);
    }
  }, [dateRange.from, dateRange.to, selectedCountry, selectedGlobalCompany, admin?.companyId, admin?.role]);

  // Update analytics when filters change
  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Calculate derived metrics
  // Fix: Use accepted/(accepted+declined) formula instead of accepted/totalApplications
  const finalizedApplications = analytics.acceptedApplications + analytics.rejectedApplications;
  const totalApprovalRate = finalizedApplications > 0 
    ? Math.round((analytics.acceptedApplications / finalizedApplications) * 100)
    : 0;

  const topCountry = Object.entries(analytics.countryBreakdown || {})
    .sort(([,a], [,b]) => b.total - a.total)[0];

  // Prepare chart data
  const statusData = [
    { name: t('visaStatus.pending'), value: analytics.pendingApplications, color: '#3B82F6' },
    { name: t('statistics.submitted'), value: analytics.metrics?.submittedApplications || 0, color: '#10B981' },
    { name: t('visaStatus.approved'), value: analytics.acceptedApplications, color: '#059669' },
    { name: t('visaStatus.rejected'), value: analytics.rejectedApplications, color: '#EF4444' }
  ].filter(item => item.value > 0);

  const professionData = Object.entries(analytics.professionSuccessRate || {})
    .filter(([profession]) => profession !== 'Не указана')
    .sort(([,a], [,b]) => b.total - a.total)
    .slice(0, 10)
    .map(([profession, data]) => {
      // Fix: Use accepted/(accepted+declined) formula for profession success rate
      const finalizedApps = data.approved + (data.rejected || 0);
      return {
        profession: profession.length > 15 ? profession.substring(0, 15) + '...' : profession,
        total: data.total,
        approved: data.approved,
        successRate: finalizedApps > 0 ? Math.round((data.approved / finalizedApps) * 100) : 0
      };
    });

  const ageData = Object.entries(analytics.ageBreakdown || {})
    .filter(([age]) => age !== 'Не указан')
    .map(([age, count]) => ({ age, count }))
    .sort((a, b) => {
      const order = ['18-25', '26-35', '36-45', '46-55', '56+'];
      return order.indexOf(a.age) - order.indexOf(b.age);
    });

  const incomeCorrelationData = Object.entries(analytics.incomeApprovalCorrelation || {})
    .filter(([income]) => income !== 'Не указана')
    .map(([income, data]) => {
      // Fix: Use accepted/(accepted+declined) formula for income correlation
      const finalizedApps = data.approved + data.rejected;
      return {
        income,
        approvalRate: finalizedApps > 0 ? Math.round((data.approved / finalizedApps) * 100) : 0,
        total: data.total
      };
    })
    .sort((a, b) => {
      const order = ['До 450,000 теңге', '450,000 - 1,350,000 теңге', '1,350,000 - 2,250,000 теңге', '2,250,000 - 4,500,000 теңге', 'Свыше 4,500,000 теңге'];
      return order.indexOf(a.income) - order.indexOf(b.income);
    });

  const rejectionReasonsData = Object.entries(analytics.rejectionReasons || {})
    .sort(([,a], [,b]) => b - a)
    .slice(0, 8)
    .map(([reason, count]) => ({
      reason: reason.length > 30 ? reason.substring(0, 30) + '...' : reason,
      count
    }));

  // Use filtered countryBreakdown when a country is selected, otherwise show all countries
  const countryDataToShow = selectedCountry 
    ? analytics.countryBreakdown || {}
    : analytics.allCountriesBreakdown || analytics.countryBreakdown || {};

  const countryStats = Object.entries(countryDataToShow)
    .map(([country, data]) => {
      // Fix: Use accepted/(accepted+declined) formula for country success rate
      const finalizedApps = data.approved + data.rejected;
      return {
        country,
        displayName: t(`countries.${country}`) || country,
        flag: COUNTRY_FLAGS[country] || '🏳️',
        total: data.total,
        approved: data.approved,
        rejected: data.rejected,
        pending: data.pending,
        revenue: data.revenue,
        successRate: finalizedApps > 0 ? Math.round((data.approved / finalizedApps) * 100) : 0
      };
    })
    .sort((a, b) => b.total - a.total);

  // Get available countries for dropdown (use API provided list or fallback to allCountriesBreakdown/countryBreakdown)
  const availableCountries = (
    analytics.availableCountries || 
    Object.keys(analytics.allCountriesBreakdown || {}) ||
    Object.keys(analytics.countryBreakdown || {})
  )
    .filter(country => country && country.trim() !== '') // Remove empty entries
    .sort((a: string, b: string) => {
      const displayA = t(`countries.${a}`) || a;
      const displayB = t(`countries.${b}`) || b;
      return displayA.localeCompare(displayB);
    });

  // Debug logging for analytics changes
  useEffect(() => {
    console.log('=== STATISTICS DASHBOARD analytics CHANGE ===');
    console.log('analytics.totalApplications:', analytics.totalApplications);
    console.log('analytics.filteredTotalApplications:', analytics.filteredTotalApplications);
    console.log('analytics.acceptedApplications:', analytics.acceptedApplications);
    console.log('analytics.rejectedApplications:', analytics.rejectedApplications);
    console.log('analytics.pendingApplications:', analytics.pendingApplications);
    console.log('analytics:', analytics);
    console.log('============================================');
  }, [analytics]);

  // UI: Всегда показывать оба счетчика
  const trueTotal = analytics.totalApplications;
  const filteredTotal = analytics.filteredTotalApplications;

  return (
    <div className="space-y-6 p-6">
      {/* Header with Country Filter */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div className="flex-1">
          <div className="text-sm text-gray-600">
            Период: {formatDate(dateRange.from)} — {formatDate(dateRange.to)}
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
              {t('statistics.country')}:
            </label>
            <div className="relative">
              <select
                value={selectedCountry}
                onChange={(e) => setSelectedCountry(e.target.value)}
                disabled={metricsLoading}
                className={`appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm min-w-[200px] transition-colors ${
                  metricsLoading 
                    ? 'cursor-not-allowed opacity-50' 
                    : 'cursor-pointer hover:border-gray-400'
                }`}
              >
                <option value="">{t('statistics.allCountries')}</option>
                {availableCountries.map(country => (
                  <option key={country} value={country}>
                    {COUNTRY_FLAGS[country] || ''} {country}
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                </svg>
              </div>
            </div>
            {selectedCountry && !metricsLoading && (
              <button
                onClick={() => setSelectedCountry('')}
                className="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                title="Clear country filter"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Clear
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-lg">📊</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {t('statistics.totalApplications')}
                    </dt>
                    <dd className="text-2xl font-bold text-gray-900">
                      {metricsLoading ? (
                        <span className="animate-pulse text-gray-400 blur-sm">{analytics.totalApplications}</span>
                      ) : (
                        analytics.totalApplications
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-lg">✅</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      % Одобрений
                    </dt>
                    <dd className="text-2xl font-bold text-gray-900">
                      {totalApprovalRate}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-lg">💰</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Общий доход
                    </dt>
                    <dd className="text-2xl font-bold text-gray-900">
                      {(analytics.metrics?.totalRevenue || 0).toLocaleString()} теңге
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-lg">🏆</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Топ страна
                    </dt>
                    <dd className="text-2xl font-bold text-gray-900">
                      {topCountry ? `${COUNTRY_FLAGS[topCountry[0]] || '🏳️'} ${t(`countries.${topCountry[0]}`) || topCountry[0]}` : t('statistics.noData')}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Country Overview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-gray-900">
              🌍 {selectedCountry ? t('statistics.countryStats') : t('statistics.countryOverview')}
            </h3>
            {selectedCountry && (
              <div className="flex items-center text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                <span className="mr-1">🔍</span>
                {COUNTRY_FLAGS[selectedCountry] || '🏳️'} {t(`countries.${selectedCountry}`) || selectedCountry}
            </div>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {countryStats.length > 0 ? (
              countryStats.map((country) => (
                <div key={country.country} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="text-2xl mr-2">{country.flag}</span>
                      <span className="font-bold text-gray-900">{country.displayName}</span>
                    </div>
                    <span className="text-sm text-gray-500">{country.successRate}%</span>
          </div>

                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="text-blue-600 font-bold">{country.total}</div>
                      <div className="text-gray-600">{t('statistics.total')}</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="text-green-600 font-bold">{country.approved}</div>
                      <div className="text-gray-600">{t('statistics.approved')}</div>
                    </div>
                    <div className="text-center p-2 bg-red-50 rounded">
                      <div className="text-red-600 font-bold">{country.rejected}</div>
                      <div className="text-gray-600">{t('statistics.rejected')}</div>
                    </div>
                    <div className="text-center p-2 bg-gray-50 rounded">
                      <div className="text-gray-600 font-bold">{country.revenue.toLocaleString()} теңге</div>
                      <div className="text-gray-600">{t('statistics.income')}</div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-8 text-gray-500">
                {selectedCountry 
                  ? t('statistics.noDataForPeriod')
                  : t('statistics.noDataToDisplay')
                }
              </div>
            )}
          </div>
        </div>

        {/* Charts Row 1: Status Distribution and Top Professions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Status Distribution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">📊 {t('statistics.statusDistribution')}</h3>
            {statusData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    labelLine={false}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px] text-gray-500">
                {t('statistics.noDataToDisplay')}
              </div>
            )}
        </div>

          {/* Top Professions with Success Rates */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">👔 {t('statistics.topProfessions')}</h3>
            {professionData.length > 0 ? (
              <div className="space-y-4">
                {professionData.slice(0, 6).map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center flex-1">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 mb-1">{item.profession}</div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${item.successRate}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right ml-4">
                      <div className="text-lg font-bold text-gray-900">{item.successRate}%</div>
                      <div className="text-sm text-gray-500">
                        {item.approved}/{item.total} заявок
                      </div>
                    </div>
                  </div>
                ))}
                {professionData.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    {t('statistics.noDataToDisplay')}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-[300px] text-gray-500">
                {t('statistics.noDataToDisplay')}
              </div>
            )}
          </div>
          </div>

        {/* Charts Row 2: Age Distribution */}
        <div className="grid grid-cols-1 gap-6">
          {/* Age Distribution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">👥 {t('statistics.ageDistribution')}</h3>
            {ageData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={ageData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="age" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#FFBB28" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px] text-gray-500">
                {t('statistics.noDataToDisplay')}
              </div>
            )}
          </div>
        </div>

        {/* Charts Row 3: Income Correlation (Full Width) */}
        <div className="grid grid-cols-1 gap-6">
          {/* Income vs Approval Rate */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">💰 {t('statistics.incomeVsApproval')}</h3>
            {incomeCorrelationData.length > 0 ? (
              <ResponsiveContainer width="100%" height={600}>
                <LineChart 
                  data={incomeCorrelationData}
                  margin={{ top: 30, right: 40, left: 30, bottom: 100 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="income" 
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    interval={0}
                  />
                  <YAxis 
                    label={{ value: t('statistics.approvalRateLabel'), angle: -90, position: 'insideLeft' }}
                    domain={[0, 100]}
                    tick={{ fontSize: 12 }}
                    width={70}
                  />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'approvalRate' ? `${value}%` : value,
                      name === 'approvalRate' ? t('statistics.approvalPercent') : t('statistics.totalApplications')
                    ]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="approvalRate" 
                    stroke="#8884d8" 
                    strokeWidth={4}
                    dot={{ fill: '#8884d8', strokeWidth: 3, r: 8 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[600px] text-gray-500">
                {t('statistics.noDataToDisplay')}
              </div>
            )}
          </div>
        </div>

        {/* Charts Row 4: Rejection Reasons */}
        <div className="grid grid-cols-1 gap-6">
          {/* Rejection Reasons */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">❌ {t('statistics.rejectionReasons')}</h3>
            {rejectionReasonsData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={rejectionReasonsData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                    labelLine={false}
                  >
                    {rejectionReasonsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, _name, props) => [value, props.payload.reason]} />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px] text-gray-500">
                {t('statistics.noDataToDisplay')}
              </div>
            )}
          </div>

        </div>

        {/* Monthly Submissions Trend */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">📈 {t('statistics.submissionDynamics')}</h3>
          {analytics.monthlySubmissions && analytics.monthlySubmissions.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analytics.monthlySubmissions}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#00C49F" 
                  strokeWidth={2}
                  dot={{ fill: '#00C49F' }}
                />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-[300px] text-gray-500">
              {t('statistics.noDataToDisplay')}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default StatisticsDashboard; 