import { useState } from 'react';
import { SearchFilters } from '../../types/admin';
import { useTranslation } from '../../utils/localization';

interface AdvancedFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onClose: () => void;
}

const professions = [
  'IT специалист',
  'Инженер',
  'Врач',
  'Учитель',
  'Менеджер',
  'Консультант',
  'Аналитик',
  'Дизайнер',
  'Бухгалтер',
  'Юрист',
  'Другое'
];

const maritalStatuses = [
  'Холост/Не замужем',
  'Женат/Замужем',
  'Разведен(а)',
  'Вдовец/Вдова'
];

const destinationCountries = [
  'США',
  'Канада',
  'Великобритания',
  'Германия',
  'Франция',
  'Австралия',
  'Япония',
  'Южная Корея',
  'Сингапур',
  'ОАЭ'
];

const propertyTypes = [
  'Квартира',
  'Дом',
  'Коммерческая недвижимость',
  'Земельный участок',
  'Другое'
];

export default function AdvancedFilters({ filters, onFiltersChange, onClose }: AdvancedFiltersProps) {
  const { t } = useTranslation();
  const [localFilters, setLocalFilters] = useState<SearchFilters>(filters);

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const handleResetFilters = () => {
    const resetFilters: SearchFilters = {};
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-gray-900">{t('filters.advancedFilters')}</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Name Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Имя клиента
          </label>
          <input
            type="text"
                            placeholder={t('filters.enterName')}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={localFilters.searchQuery || ''}
            onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
          />
        </div>

        {/* Profession Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Профессия
          </label>
          <select
            multiple
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={localFilters.profession?.split(',') || []}
            onChange={(e) => {
              const values = Array.from(e.target.selectedOptions, option => option.value);
              handleFilterChange('profession', values.join(','));
            }}
          >
            {professions.map(profession => (
              <option key={profession} value={profession}>
                {profession}
              </option>
            ))}
          </select>
        </div>

        {/* Income Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Доход (теңге)
          </label>
          <div className="flex space-x-2">
            <input
              type="number"
                              placeholder={t('filters.from')}
              min="0"
              max="1000000"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={localFilters.minIncome || ''}
              onChange={(e) => handleFilterChange('minIncome', e.target.value)}
            />
            <input
              type="number"
                              placeholder={t('filters.to')}
              min="0"
              max="1000000"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={localFilters.maxIncome || ''}
              onChange={(e) => handleFilterChange('maxIncome', e.target.value)}
            />
          </div>
        </div>

        {/* Removed Visa History and Property sections */}

        {/* Marital Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Семейное положение
          </label>
          <select
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={localFilters.maritalStatus || ''}
            onChange={(e) => handleFilterChange('maritalStatus', e.target.value)}
          >
            <option value="">Все</option>
            {maritalStatuses.map(status => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
        </div>

        {/* Destination Country */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Страна назначения
          </label>
          <select
            multiple
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={localFilters.destinationCountry?.split(',') || []}
            onChange={(e) => {
              const values = Array.from(e.target.selectedOptions, option => option.value);
              handleFilterChange('destinationCountry', values.join(','));
            }}
          >
            {destinationCountries.map(country => (
              <option key={country} value={country}>
                {country}
              </option>
            ))}
          </select>
        </div>

        {/* Sponsor Availability */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Дополнительные параметры
          </label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                checked={localFilters.hasSponsor === 'true'}
                onChange={(e) => handleFilterChange('hasSponsor', e.target.checked ? 'true' : '')}
              />
              <span className="ml-2 text-sm text-gray-700">Есть спонсор</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                checked={localFilters.hasInvitation === 'true'}
                onChange={(e) => handleFilterChange('hasInvitation', e.target.checked ? 'true' : '')}
              />
              <span className="ml-2 text-sm text-gray-700">Есть приглашение</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                checked={localFilters.hasPreviousRejections === 'true'}
                onChange={(e) => handleFilterChange('hasPreviousRejections', e.target.checked ? 'true' : '')}
              />
              <span className="ml-2 text-sm text-gray-700">Были отказы</span>
            </label>
          </div>
        </div>

        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Период подачи заявки
          </label>
          <div className="flex space-x-2">
            <input
              type="date"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={localFilters.dateFrom || ''}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            />
            <input
              type="date"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={localFilters.dateTo || ''}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
        <button
          onClick={handleResetFilters}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Сбросить
        </button>
        <button
          onClick={handleApplyFilters}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
        >
          Применить фильтры
        </button>
      </div>
    </div>
  );
} 