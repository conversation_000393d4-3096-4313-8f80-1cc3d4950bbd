import { useState, useEffect } from 'react';
import { VisaCompany, Admin } from '../../types/admin';
import ConfirmationModal from '../common/ConfirmationModal';
import { useToast } from '../../hooks/useToast';
import { useTranslation } from '../../utils/localization';

interface ExtendedVisaCompany extends VisaCompany {
  adminEmail?: string;
  slug?: string;
  phone_number?: string;
  email?: string;
  wapi_token?: string;
  wapi_profile_id?: string;
  wapi_webhook_url?: string;
}

interface CompanyManagementProps {
  admin: Omit<Admin, 'password'>;
  isSuperAdmin: boolean;
}

// Mock data for visa companies
const mockCompanies: VisaCompany[] = [
  {
    id: 'company_1',
    name: 'Global Visa Services',
    isBlocked: false,
    createdAt: '2024-01-15T10:00:00Z',
    adminId: 'visa_1'
  },
  {
    id: 'company_2',
    name: 'Premium Immigration Solutions',
    isBlocked: false,
    createdAt: '2024-02-20T14:30:00Z',
    adminId: 'visa_2'
  },
  {
    id: 'company_3',
    name: 'FastTrack Visa Processing',
    isBlocked: true,
    createdAt: '2024-03-10T09:15:00Z',
    adminId: 'visa_3'
  }
];

export default function CompanyManagement({ admin, isSuperAdmin }: CompanyManagementProps) {
  const { t } = useTranslation();
  const { success, error } = useToast();
  const [companies, setCompanies] = useState<ExtendedVisaCompany[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [newCompany, setNewCompany] = useState({
    name: '',
    adminEmail: '',
    adminPassword: '',
    slug: '',
    wapi_token: '',
    wapi_profile_id: '',
    wapi_webhook_url: ''
  });

  // Generate slug from company name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  };

  // Delete confirmation modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState<ExtendedVisaCompany | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Block confirmation modal state
  const [isBlockModalOpen, setIsBlockModalOpen] = useState(false);
  const [companyToBlock, setCompanyToBlock] = useState<ExtendedVisaCompany | null>(null);
  const [blockLoading, setBlockLoading] = useState(false);
  
  // Edit company modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingCompany, setEditingCompany] = useState<ExtendedVisaCompany | null>(null);
  const [editFormData, setEditFormData] = useState({
    name: '',
    phone_number: '',
    email: '',
    slug: '',
    wapi_token: '',
    wapi_profile_id: '',
    wapi_webhook_url: ''
  });
  const [editLoading, setEditLoading] = useState(false);
  
  // Local state to track blocked companies (workaround for demo)
  const [blockedCompanies, setBlockedCompanies] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/companies');
      const data = await response.json();
      
      if (response.ok) {
        // Map database fields to frontend interface
        let mappedCompanies = (data.companies || []).map((company: any) => ({
          ...company,
          isBlocked: company.is_blocked || false,
          adminEmail: company.employees?.[0]?.email || '',
          slug: company.slug || null,
          createdAt: company.created_at,
          updatedAt: company.updated_at
        }));

        // For visa admins, filter to show only their company
        if (!isSuperAdmin && admin.companyId) {
          mappedCompanies = mappedCompanies.filter((company: any) => company.id === admin.companyId);
        }

        setCompanies(mappedCompanies);
        
        // Initialize blockedCompanies Set based on database data
        const newBlockedCompanies = new Set<string>();
        mappedCompanies.forEach((company: any) => {
          if (company.is_blocked) {
            newBlockedCompanies.add(company.id);
          }
        });
        setBlockedCompanies(newBlockedCompanies);
      } else {
        error('Ошибка загрузки', 'Не удалось загрузить список компаний');
      }
    } catch (err) {
      error('Ошибка сети', 'Проверьте подключение к интернету');
      console.error('Error fetching companies:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) {
      return 'Дата не указана';
    }
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Неверная дата';
      }
      
      return date.toLocaleDateString('ru-RU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Ошибка даты';
    }
  };

  const handleToggleBlockClick = (companyId: string) => {
    const company = companies.find(c => c.id === companyId);
    if (!company) return;
    
    setCompanyToBlock(company);
    setIsBlockModalOpen(true);
  };

  const handleBlockConfirm = async () => {
    if (!companyToBlock) return;

    setBlockLoading(true);
    try {
      const isCurrentlyBlocked = blockedCompanies.has(companyToBlock.id);
      const newBlockedStatus = !isCurrentlyBlocked;

      const response = await fetch('/api/admin/companies', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyId: companyToBlock.id,
          isBlocked: newBlockedStatus
        }),
      });

      if (response.ok) {
        // Update local state to reflect the change
        const newBlockedCompanies = new Set(blockedCompanies);
        if (newBlockedStatus) {
          newBlockedCompanies.add(companyToBlock.id);
        } else {
          newBlockedCompanies.delete(companyToBlock.id);
        }
        setBlockedCompanies(newBlockedCompanies);

        success(
          'Статус обновлен',
          `Компания "${companyToBlock.name}" ${isCurrentlyBlocked ? 'разблокирована' : 'заблокирована'}`
        );
        fetchCompanies();
        setIsBlockModalOpen(false);
        setCompanyToBlock(null);
      } else {
        const data = await response.json();
        error('Ошибка обновления', data.error || 'Не удалось изменить статус компании');
      }
    } catch (err) {
      error('Ошибка сети', 'Проверьте подключение к интернету');
      console.error('Error toggling company block:', err);
    } finally {
      setBlockLoading(false);
    }
  };

  const handleBlockCancel = () => {
    setIsBlockModalOpen(false);
    setCompanyToBlock(null);
  };

  const handleDeleteClick = (company: ExtendedVisaCompany) => {
    setCompanyToDelete(company);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!companyToDelete) return;
    
    setDeleteLoading(true);
    try {
      const response = await fetch('/api/admin/companies', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ companyId: companyToDelete.id }),
      });

      if (response.ok) {
        success(
          'Компания удалена',
          `${companyToDelete.name} и связанный администратор удалены из системы`
        );
        fetchCompanies();
      } else {
        const data = await response.json();
        error('Ошибка удаления', data.error || 'Не удалось удалить компанию');
      }
    } catch (err) {
      error('Ошибка сети', 'Проверьте подключение к интернету');
      console.error('Error deleting company:', err);
    } finally {
      setDeleteLoading(false);
      setIsDeleteModalOpen(false);
      setCompanyToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
    setCompanyToDelete(null);
  };

  const handleEditClick = (company: ExtendedVisaCompany) => {
    setEditingCompany(company);
    setEditFormData({
      name: company.name || '',
      phone_number: company.phone_number || '',
      email: company.email || '',
      slug: company.slug || '',
      wapi_token: company.wapi_token || '',
      wapi_profile_id: company.wapi_profile_id || '',
      wapi_webhook_url: company.wapi_webhook_url || ''
    });
    setShowEditModal(true);
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingCompany) return;

    setEditLoading(true);
    try {
      const response = await fetch('/api/admin/companies', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyId: editingCompany.id,
          name: editFormData.name,
          phone_number: editFormData.phone_number,
          email: editFormData.email,
          slug: editFormData.slug,
          wapi_token: editFormData.wapi_token || null,
          wapi_profile_id: editFormData.wapi_profile_id || null,
          wapi_webhook_url: editFormData.wapi_webhook_url || null
        }),
      });

      const data = await response.json();

      if (response.ok) {
        success('Компания обновлена', 'Данные компании успешно обновлены');
        setShowEditModal(false);
        setEditingCompany(null);
        fetchCompanies();
      } else {
        error('Ошибка обновления', data.error || 'Не удалось обновить компанию');
      }
    } catch (err) {
      error('Ошибка сети', 'Проверьте подключение к интернету');
      console.error('Error updating company:', err);
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditCancel = () => {
    setShowEditModal(false);
    setEditingCompany(null);
  };

  const handleAddCompany = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!newCompany.name.trim()) {
      error('Ошибка валидации', 'Название компании обязательно для заполнения');
      return;
    }
    
    if (!newCompany.adminEmail.trim()) {
      error('Ошибка валидации', 'Email администратора обязателен для заполнения');
      return;
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newCompany.adminEmail)) {
      error('Ошибка валидации', 'Введите корректный email');
      return;
    }
    
    if (newCompany.adminPassword.length < 6) {
      error('Ошибка валидации', 'Пароль должен содержать минимум 6 символов');
      return;
    }
    
    if (!newCompany.slug.trim()) {
      error('Ошибка валидации', 'URL-слаг компании обязателен для заполнения');
      return;
    }
    
    if (!/^[a-z0-9-]+$/.test(newCompany.slug)) {
      error('Ошибка валидации', 'URL-слаг может содержать только буквы, цифры и дефисы');
      return;
    }

    setFormLoading(true);
    try {
      const response = await fetch('/api/admin/companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCompany.name,
          adminEmail: newCompany.adminEmail,
          adminPassword: newCompany.adminPassword,
          slug: newCompany.slug,
          wapi_token: newCompany.wapi_token || null,
          wapi_profile_id: newCompany.wapi_profile_id || null,
          wapi_webhook_url: newCompany.wapi_webhook_url || null
        }),
      });

      const data = await response.json();

      if (response.ok) {
        success(
          'Компания создана',
          'Новая компания и администратор успешно добавлены в систему'
        );
        setNewCompany({ name: '', adminEmail: '', adminPassword: '', slug: '', wapi_token: '', wapi_profile_id: '', wapi_webhook_url: '' });
        setShowAddForm(false);
        fetchCompanies();
      } else {
        error('Ошибка создания', data.error || 'Не удалось создать компанию');
      }
    } catch (err) {
      error('Ошибка сети', 'Проверьте подключение к интернету');
      console.error('Error creating company:', err);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-xl font-semibold text-gray-900">
              {isSuperAdmin ? t('companies.title') : 'Информация о компании'}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {isSuperAdmin 
                ? t('companies.description')
                : 'Просмотр информации о вашей компании и маршруте формы'
              }
            </p>
          </div>
          {isSuperAdmin && (
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg className="-ml-1 mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              {t('companies.addCompany')}
            </button>
          )}
        </div>
      </div>

      {/* Company Routes Overview */}
      {!loading && companies.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <h4 className="text-lg font-semibold text-gray-900">
              {isSuperAdmin ? 'Активные маршруты компаний' : 'Маршрут вашей компании'}
            </h4>
          </div>
          
          {companies.filter(company => company.slug && (!blockedCompanies.has(company.id) || !isSuperAdmin)).length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {companies
                .filter(company => company.slug && (!blockedCompanies.has(company.id) || !isSuperAdmin))
                .map((company) => (
                  <div key={company.id} className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">
                          {company.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{company.name}</p>
                        <a 
                          href={`/${company.slug}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs font-mono text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200"
                        >
                          {typeof window !== 'undefined' ? `${window.location.origin}/${company.slug}` : `/${company.slug}`}
                        </a>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => navigator.clipboard.writeText(`${typeof window !== 'undefined' ? window.location.origin : 'https://visaai.vercel.app'}/${company.slug}`)}
                        className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors duration-200"
                        title="Копировать ссылку"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                      <a 
                        href={`/${company.slug}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors duration-200"
                        title="Открыть форму"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              <p className="mt-2 text-sm text-gray-500">
                Нет активных маршрутов. Добавьте URL-слаги к компаниям для создания форм.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Add Company Form */}
      {isSuperAdmin && showAddForm && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h4 className="text-lg font-semibold text-gray-900">Добавить новую компанию</h4>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <form onSubmit={handleAddCompany} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Название компании <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  required
                  value={newCompany.name}
                  onChange={(e) => {
                    const name = e.target.value;
                    const autoSlug = generateSlug(name);
                    setNewCompany({ 
                      ...newCompany, 
                      name,
                      slug: newCompany.slug === '' || newCompany.slug === generateSlug(newCompany.name) ? autoSlug : newCompany.slug
                    });
                  }}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  placeholder="Введите название компании"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email администратора <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  required
                  value={newCompany.adminEmail}
                  onChange={(e) => setNewCompany({ ...newCompany, adminEmail: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  placeholder="<EMAIL>"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Будет создан аккаунт администратора в системе
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Пароль администратора <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  required
                  minLength={6}
                  value={newCompany.adminPassword}
                  onChange={(e) => setNewCompany({ ...newCompany, adminPassword: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  placeholder="Минимум 6 символов"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL-слаг компании <span className="text-red-500">*</span>
              </label>
              <div className="flex">
                <span className="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                  {typeof window !== 'undefined' ? `${window.location.origin}/` : 'https://visaai.vercel.app/'}
                </span>
                <input
                  type="text"
                  required
                  value={newCompany.slug}
                  onChange={(e) => {
                    const slug = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '-').replace(/-+/g, '-');
                    setNewCompany({ ...newCompany, slug });
                  }}
                  className="block w-full px-3 py-2 border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  placeholder="visa-company-name"
                />
                <button
                  type="button"
                  onClick={() => setNewCompany({ ...newCompany, slug: generateSlug(newCompany.name) })}
                  className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200"
                  title="Сгенерировать из названия"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Уникальный URL для формы компании. Только буквы, цифры и дефисы.
              </p>
            </div>
            
            {/* WhatsApp API Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6 border-t border-gray-200">
              <div className="md:col-span-3 mb-4">
                <h4 className="text-md font-medium text-gray-900 flex items-center">
                  <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.04 2c-5.46 0-9.91 4.45-9.91 9.91 0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21 5.46 0 9.91-4.45 9.91-9.91S17.5 2 12.04 2M8.53 7.33c.16 0 .31 0 .45.01.14 0 .29-.05.43.33l.69 1.67c.06.14.03.29-.07.42l-.4.41c-.06.06-.08.15-.04.23.32.75.78 1.47 1.32 2.01s1.26 1 2.01 1.32c.08.04.17.02.23-.04l.41-.4c.13-.1.28-.13.42-.07l1.67.69c.38.14.33.29.33.43.01.14.01.29.01.45-.01.19-.01.38-.12.55-.1.17-.26.31-.4.42-.3.24-.64.42-1.01.48-.32.05-.66.05-.98-.02-2.84-.65-5.39-2.29-7.35-4.25s-3.6-4.51-4.25-7.35c-.07-.32-.07-.66-.02-.98.06-.37.24-.71.48-1.01.11-.14.25-.3.42-.4.17-.11.36-.11.55-.12z"/>
                  </svg>
                  WhatsApp API настройки (опционально)
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  Данные для интеграции с WhatsApp API. Все поля опциональны и могут быть заполнены позже.
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Токен API
                </label>
                <input
                  type="text"
                  value={newCompany.wapi_token}
                  onChange={(e) => setNewCompany({ ...newCompany, wapi_token: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  placeholder="Введите токен API"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Токен для доступа к WhatsApp API
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ID профиля
                </label>
                <input
                  type="text"
                  value={newCompany.wapi_profile_id}
                  onChange={(e) => setNewCompany({ ...newCompany, wapi_profile_id: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  placeholder="Введите ID профиля"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Идентификатор профиля WhatsApp
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Webhook URL
                </label>
                <input
                  type="url"
                  value={newCompany.wapi_webhook_url}
                  onChange={(e) => setNewCompany({ ...newCompany, wapi_webhook_url: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  placeholder="https://example.com/webhook"
                />
                <p className="mt-1 text-xs text-gray-500">
                  URL для получения уведомлений
                </p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-6 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                Отмена
              </button>
              <button
                type="submit"
                disabled={formLoading}
                className={`inline-flex items-center px-6 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white ${
                  formLoading 
                    ? 'bg-blue-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                } transition-colors duration-200`}
              >
                {formLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Создание...
                  </>
                ) : (
                  'Создать компанию'
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Companies List */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {isSuperAdmin 
              ? `Компании (${companies.length})`
              : 'Информация о компании'
            }
          </h3>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="flex items-center space-x-3">
              <svg className="animate-spin h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="text-gray-600">Загрузка компаний...</span>
            </div>
          </div>
        ) : companies.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Компании не найдены</h3>
            <p className="mt-1 text-sm text-gray-500">Начните с добавления первой компании</p>
            <div className="mt-6">
              <button
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Добавить первую компанию
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {companies.map((company) => (
              <div key={company.id} className="px-6 py-4 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-gray-900">
                          {company.name}
                        </p>
                        <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          blockedCompanies.has(company.id)
                            ? 'bg-red-100 text-red-800 border border-red-200' 
                            : 'bg-green-100 text-green-800 border border-green-200'
                        }`}>
                          {blockedCompanies.has(company.id) ? 'Заблокирована' : 'Активна'}
                        </span>
                      </div>
                      <div className="mt-1 flex items-center text-sm text-gray-500 space-x-4">
                        <span>ID: {company.id}</span>
                        {company.adminEmail && (
                          <span>Администратор: {company.adminEmail}</span>
                        )}
                        <span>Создано: {formatDate(company.createdAt)}</span>
                      </div>
                      {company.slug && (
                        <div className="mt-2 flex items-center">
                          <div className="inline-flex items-center px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg">
                            <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                            <span className="text-sm font-medium text-blue-700">
                              Форма компании: 
                            </span>
                            <a 
                              href={`/${company.slug}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="ml-2 text-sm font-mono text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200"
                            >
                              {typeof window !== 'undefined' ? `${window.location.origin}/${company.slug}` : `/${company.slug}`}
                            </a>
                            <button
                              onClick={() => navigator.clipboard.writeText(`${typeof window !== 'undefined' ? window.location.origin : 'https://visaai.vercel.app'}/${company.slug}`)}
                              className="ml-2 p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors duration-200"
                              title="Копировать ссылку"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      )}
                      {!company.slug && (
                        <div className="mt-2 flex items-center">
                          <div className="inline-flex items-center px-3 py-1 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <svg className="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <span className="text-sm font-medium text-yellow-700">
                              URL-слаг не настроен - форма недоступна
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {isSuperAdmin && (
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleEditClick(company)}
                        className="inline-flex items-center p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md transition-colors duration-200"
                        title="Редактировать компанию"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>

                      <button
                        onClick={() => handleToggleBlockClick(company.id)}
                        className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md ${
                          blockedCompanies.has(company.id)
                            ? 'text-green-700 bg-green-100 hover:bg-green-200'
                            : 'text-red-700 bg-red-100 hover:bg-red-200'
                        } transition-colors duration-200`}
                      >
                        {blockedCompanies.has(company.id) ? 'Разблокировать' : 'Заблокировать'}
                      </button>
                      
                      <button
                        onClick={() => handleDeleteClick(company)}
                        className="inline-flex items-center p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md transition-colors duration-200"
                        title="Удалить компанию"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        title="Удалить компанию"
        message={
          companyToDelete 
            ? `Вы уверены, что хотите удалить компанию "${companyToDelete.name}"? Это действие также удалит связанного администратора из системы аутентификации. Это действие нельзя отменить.`
            : 'Вы уверены, что хотите удалить эту компанию?'
        }
        confirmText="Удалить"
        cancelText="Отменить"
        confirmButtonColor="red"
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        loading={deleteLoading}
      />

      {/* Block Confirmation Modal */}
      <ConfirmationModal
        isOpen={isBlockModalOpen}
        title={companyToBlock && blockedCompanies.has(companyToBlock.id) ? "Разблокировать компанию" : "Заблокировать компанию"}
        message={
          companyToBlock 
            ? blockedCompanies.has(companyToBlock.id)
              ? `Вы уверены, что хотите разблокировать компанию "${companyToBlock.name}"? После разблокировки администраторы компании смогут снова войти в систему.`
              : `Вы уверены, что хотите заблокировать компанию "${companyToBlock.name}"? Все администраторы этой компании потеряют доступ к системе до разблокировки.`
            : 'Вы уверены, что хотите изменить статус этой компании?'
        }
        confirmText={companyToBlock && blockedCompanies.has(companyToBlock.id) ? "Разблокировать" : "Заблокировать"}
        cancelText="Отменить"
        confirmButtonColor={companyToBlock && blockedCompanies.has(companyToBlock.id) ? "green" : "red"}
        onConfirm={handleBlockConfirm}
        onCancel={handleBlockCancel}
        loading={blockLoading}
      />

      {/* Edit Company Modal */}
      {showEditModal && editingCompany && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-gray-900">
                Редактировать компанию: {editingCompany.name}
              </h3>
              <button
                onClick={handleEditCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleEditSubmit} className="space-y-6">
              {/* Basic Company Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Название компании <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    required
                    value={editFormData.name}
                    onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Введите название компании"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    URL-слаг компании
                  </label>
                  <input
                    type="text"
                    value={editFormData.slug}
                    onChange={(e) => {
                      const slug = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '-').replace(/-+/g, '-');
                      setEditFormData({ ...editFormData, slug });
                    }}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="visa-company-name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Номер телефона
                  </label>
                  <input
                    type="text"
                    value={editFormData.phone_number}
                    onChange={(e) => setEditFormData({ ...editFormData, phone_number: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="+7 (xxx) xxx-xx-xx"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email компании
                  </label>
                  <input
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* WhatsApp API Settings */}
              <div className="pt-6 border-t border-gray-200">
                <div className="mb-4">
                  <h4 className="text-md font-medium text-gray-900 flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12.04 2c-5.46 0-9.91 4.45-9.91 9.91 0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21 5.46 0 9.91-4.45 9.91-9.91S17.5 2 12.04 2M8.53 7.33c.16 0 .31 0 .45.01.14 0 .29-.05.43.33l.69 1.67c.06.14.03.29-.07.42l-.4.41c-.06.06-.08.15-.04.23.32.75.78 1.47 1.32 2.01s1.26 1 2.01 1.32c.08.04.17.02.23-.04l.41-.4c.13-.1.28-.13.42-.07l1.67.69c.38.14.33.29.33.43.01.14.01.29.01.45-.01.19-.01.38-.12.55-.1.17-.26.31-.4.42-.3.24-.64.42-1.01.48-.32.05-.66.05-.98-.02-2.84-.65-5.39-2.29-7.35-4.25s-3.6-4.51-4.25-7.35c-.07-.32-.07-.66-.02-.98.06-.37.24-.71.48-1.01.11-.14.25-.3.42-.4.17-.11.36-.11.55-.12z"/>
                    </svg>
                    WhatsApp API настройки
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Настройки для интеграции с WhatsApp API
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Токен API
                    </label>
                    <input
                      type="text"
                      value={editFormData.wapi_token}
                      onChange={(e) => setEditFormData({ ...editFormData, wapi_token: e.target.value })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Введите токен API"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ID профиля
                    </label>
                    <input
                      type="text"
                      value={editFormData.wapi_profile_id}
                      onChange={(e) => setEditFormData({ ...editFormData, wapi_profile_id: e.target.value })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Введите ID профиля"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Webhook URL
                    </label>
                    <input
                      type="url"
                      value={editFormData.wapi_webhook_url}
                      onChange={(e) => setEditFormData({ ...editFormData, wapi_webhook_url: e.target.value })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://example.com/webhook"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleEditCancel}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  Отмена
                </button>
                <button
                  type="submit"
                  disabled={editLoading}
                  className={`inline-flex items-center px-6 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white ${
                    editLoading 
                      ? 'bg-blue-400 cursor-not-allowed' 
                      : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  } transition-colors duration-200`}
                >
                  {editLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Сохранение...
                    </>
                  ) : (
                    'Сохранить изменения'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
} 