import { useState } from 'react';
import { useTranslation } from '../../utils/localization';
import { VisaApplicationWithStatus, SearchFilters } from '../../types/admin';

interface ApplicationsListProps {
  applications: VisaApplicationWithStatus[];
  onUpdateStatus: (applicationId: string, status: string) => Promise<void>;
  searchFilters: SearchFilters;
  onSearchFiltersChange: (filters: SearchFilters) => void;
  isLoading: boolean;
}

export default function ApplicationsList({
  applications,
  onUpdateStatus,
  searchFilters,
  onSearchFiltersChange,
  isLoading
}: ApplicationsListProps) {
  const { t } = useTranslation();
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRowExpansion = (applicationId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(applicationId)) {
      newExpanded.delete(applicationId);
    } else {
      newExpanded.add(applicationId);
    }
    setExpandedRows(newExpanded);
  };

  const handleStatusChange = async (applicationId: string, newStatus: string) => {
    await onUpdateStatus(applicationId, newStatus);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'Одобрено';
      case 'rejected':
        return 'Отклонено';
      default:
        return 'На рассмотрении';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) {
      return 'Дата не указана';
    }
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Неверная дата';
      }
      
      return date.toLocaleDateString('ru-RU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Ошибка даты';
    }
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Поиск и фильтры</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Поиск
            </label>
            <input
              type="text"
              placeholder={t('filters.enterName')}
              value={searchFilters.searchQuery || ''}
              onChange={(e) => onSearchFiltersChange({
                ...searchFilters,
                searchQuery: e.target.value
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Статус
            </label>
            <select
              value={searchFilters.status || 'all'}
              onChange={(e) => onSearchFiltersChange({
                ...searchFilters,
                status: e.target.value === 'all' ? undefined : e.target.value
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Все статусы</option>
              <option value="pending">На рассмотрении</option>
              <option value="accepted">Одобрено</option>
              <option value="rejected">Отклонено</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Дата с
            </label>
            <input
              type="date"
              value={searchFilters.dateFrom || ''}
              onChange={(e) => onSearchFiltersChange({
                ...searchFilters,
                dateFrom: e.target.value
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Дата до
            </label>
            <input
              type="date"
              value={searchFilters.dateTo || ''}
              onChange={(e) => onSearchFiltersChange({
                ...searchFilters,
                dateTo: e.target.value
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Applications Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Визовые заявки ({applications.length})
          </h3>
        </div>
        
        {isLoading ? (
          <div className="text-center py-8">
            <div className="text-gray-500">Загрузка заявок...</div>
          </div>
        ) : applications.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500">Заявки не найдены</div>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {applications.map((application) => (
              <li key={application.id}>
                <div className="px-4 py-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    <button
                      onClick={() => toggleRowExpansion(application.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {expandedRows.has(application.id) ? '▼' : '▶'}
                    </button>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                                                    <p className="text-sm font-medium text-gray-900">
                            {(application.form_data?.fullNameCyrillic as string) || 
                             `${(application.form_data?.name as string) || ''} ${(application.form_data?.surname as string) || ''}`.trim() ||
                             'Неизвестный заявитель'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {(application.form_data?.email as string) || 'Email не указан'} • 
                            {(application.form_data?.occupation as string) || 'Профессия не указана'}
                          </p>
                          <p className="text-xs text-gray-400">
                            Подано: {formatDate(application.created_at)}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(application.visa_status)}`}>
                            {getStatusText(application.visa_status)}
                          </span>
                          
                          <select
                            value={application.visa_status}
                            onChange={(e) => handleStatusChange(application.id, e.target.value)}
                            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="pending">На рассмотрении</option>
                            <option value="accepted">Одобрить</option>
                            <option value="rejected">Отклонить</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {expandedRows.has(application.id) && (
                  <div className="px-4 py-4 bg-gray-50 border-t">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <strong>Номер паспорта:</strong><br />
                        {(application.form_data?.passportNumber as string) || 'Не указан'}
                      </div>
                      <div>
                        <strong>Гражданство:</strong><br />
                        {(application.form_data?.citizenship as string) || 'Не указано'}
                      </div>
                      <div>
                        <strong>Дата рождения:</strong><br />
                        {(application.form_data?.dateOfBirth as string) || 'Не указана'}
                      </div>
                      <div>
                        <strong>Телефон:</strong><br />
                        {(application.form_data?.phone as string) || 'Не указан'}
                      </div>
                      <div>
                        <strong>Назначение:</strong><br />
                        {(application.form_data?.visaCountry as string) || (application.form_data?.visaDestination as string) || (application.form_data?.destination as string) || 'Не указано'}
                      </div>
                      <div>
                        <strong>Цель поездки:</strong><br />
                        {(application.form_data?.travelPurposeDescription as string) || 'Не указана'}
                      </div>
                    </div>
                    
                    <div className="mt-4 flex justify-end space-x-2">
                      <button
                        onClick={() => handleStatusChange(application.id, 'accepted')}
                        className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                      >
                        Одобрить
                      </button>
                      <button
                        onClick={() => handleStatusChange(application.id, 'rejected')}
                        className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                      >
                        Отклонить
                      </button>
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
} 