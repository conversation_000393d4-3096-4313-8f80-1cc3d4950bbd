import { useState, useEffect } from 'react';
import { PriceListItem } from '../../types/admin';
import { useToast } from '../../hooks/useToast';

interface Company {
  id: string;
  name: string;
  phone_number?: string;
  email?: string;
  created_at: string;
  updated_at: string;
}

interface PriceListManagementProps {
  companyId?: string;
  isSuperAdmin?: boolean;
  isViewOnly?: boolean;
}

export default function PriceListManagement({ companyId, isSuperAdmin = false, isViewOnly = false }: PriceListManagementProps) {
  const { success, error } = useToast();
  const [priceList, setPriceList] = useState<PriceListItem[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingItem, setEditingItem] = useState<PriceListItem | null>(null);
  const [deletingItem, setDeletingItem] = useState<PriceListItem | null>(null);
  const [selectedCompanyId, setSelectedCompanyId] = useState<string>(companyId || 'all');
  const [countryFilter, setCountryFilter] = useState('');
  const [formData, setFormData] = useState({
    company_id: companyId || '',
    country: 'США',
    title: '',
    price: '',
    duration: '',
    description: ''
  });
  const [submitting, setSubmitting] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const countries = ['США', 'Канада', 'Великобритания', 'Германия', 'Франция', 'Австралия', 'Новая Зеландия', 'Испания', 'Италия', 'Швейцария'];

  useEffect(() => {
    if (isSuperAdmin) {
      fetchCompanies();
    }
    fetchPriceList();
  }, [selectedCompanyId, isSuperAdmin]);

  // Handle ESC key press for modals
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showAddModal) {
          resetForm();
        } else if (showDeleteModal) {
          handleDeleteCancel();
        }
      }
    };

    if (showAddModal || showDeleteModal) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [showAddModal, showDeleteModal]);

  const fetchCompanies = async () => {
    try {
      const response = await fetch('/api/admin/companies');
      const data = await response.json();
      if (response.ok) {
        setCompanies(data.companies);
      }
    } catch (err) {
      console.error('Error fetching companies:', err);
    }
  };

  const fetchPriceList = async () => {
    try {
      setLoading(true);
      const queryCompanyId = selectedCompanyId === 'all' ? '' : selectedCompanyId;
      const response = await fetch(`/api/admin/price-list?companyId=${queryCompanyId}`);
      const data = await response.json();
      
      if (response.ok) {
        setPriceList(data.packages || []);
      } else {
        error('Ошибка', 'Не удалось загрузить прайс-лист');
      }
    } catch (err) {
      error('Ошибка', 'Произошла ошибка при загрузке данных');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const url = '/api/admin/price-list';
      const method = editingItem ? 'PUT' : 'POST';
      const payload = editingItem 
        ? { id: editingItem.id, ...formData, price: parseFloat(formData.price) }
        : { ...formData, price: parseFloat(formData.price) };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok) {
        success('Успех', editingItem ? 'Пакет обновлен' : 'Пакет добавлен');
        resetForm();
        fetchPriceList();
      } else {
        error('Ошибка', data.error || 'Произошла ошибка');
      }
    } catch (err) {
      error('Ошибка', 'Произошла ошибка при сохранении');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (item: PriceListItem) => {
    setEditingItem(item);
    setFormData({
      company_id: item.company_id,
      country: item.country,
      title: item.title,
      price: item.price.toString(),
      duration: item.duration || '',
      description: item.description || ''
    });
    setShowAddModal(true);
  };

  const handleDeleteClick = (item: PriceListItem) => {
    setDeletingItem(item);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingItem) return;

    setDeleting(true);
    try {
      const response = await fetch('/api/admin/price-list', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: deletingItem.id }),
      });

      const data = await response.json();

      if (response.ok) {
        success('Успех', 'Пакет удален');
        fetchPriceList();
        setShowDeleteModal(false);
        setDeletingItem(null);
      } else {
        error('Ошибка', data.error || 'Не удалось удалить пакет');
      }
    } catch (err) {
      error('Ошибка', 'Произошла ошибка при удалении');
    } finally {
      setDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setDeletingItem(null);
  };

  const resetForm = () => {
    setFormData({ 
      company_id: selectedCompanyId === 'all' ? '' : selectedCompanyId,
      country: 'США', 
      title: '', 
      price: '', 
      duration: '', 
      description: '' 
    });
    setEditingItem(null);
    setShowAddModal(false);
  };

  const filteredPriceList = priceList.filter(item => {
    const companyMatch = selectedCompanyId === 'all' || item.company_id === selectedCompanyId;
    const countryMatch = !countryFilter || item.country === countryFilter;
    return companyMatch && countryMatch;
  });

  const groupedByCompany = filteredPriceList.reduce((acc, item) => {
    const companyName = companies.find(c => c.id === item.company_id)?.name || 'Неизвестная компания';
    if (!acc[companyName]) {
      acc[companyName] = {};
    }
    if (!acc[companyName][item.country]) {
      acc[companyName][item.country] = [];
    }
    acc[companyName][item.country].push(item);
    return acc;
  }, {} as Record<string, Record<string, PriceListItem[]>>);

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
        <p className="mt-2 text-gray-500">Загрузка прайс-листа...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Прайс-лист</h1>
          <p className="text-gray-600">
            {isViewOnly ? 'Просмотр пакетов услуг по странам и компаниям' : 'Управление пакетами услуг по странам и компаниям'}
          </p>
        </div>
        {!isViewOnly && (
          <button
            onClick={() => {
              setFormData({ 
                company_id: selectedCompanyId === 'all' ? '' : selectedCompanyId,
                country: 'США', 
                title: '', 
                price: '', 
                duration: '', 
                description: '' 
              });
              setShowAddModal(true);
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Добавить пакет
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {isSuperAdmin && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Компания:</label>
              <select
                value={selectedCompanyId}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Все компании</option>
                {companies.map(company => (
                  <option key={company.id} value={company.id}>{company.name}</option>
                ))}
              </select>
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Страна:</label>
            <select
              value={countryFilter}
              onChange={(e) => setCountryFilter(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Все страны</option>
              {countries.map(country => (
                <option key={country} value={country}>{country}</option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => {
                setSelectedCompanyId(companyId || 'all');
                setCountryFilter('');
              }}
              className="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50"
            >
              Сбросить фильтры
            </button>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{filteredPriceList.length}</div>
          <div className="text-sm text-gray-500">Всего пакетов</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">
            {new Set(filteredPriceList.map(item => item.country)).size}
          </div>
          <div className="text-sm text-gray-500">Стран</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">
            {Math.round(filteredPriceList.reduce((sum, item) => sum + item.price, 0) / Math.max(filteredPriceList.length, 1)).toLocaleString()} теңге
          </div>
          <div className="text-sm text-gray-500">Средняя цена</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">
            {isSuperAdmin ? Object.keys(groupedByCompany).length : 1}
          </div>
          <div className="text-sm text-gray-500">{isSuperAdmin ? 'Компаний' : 'Компания'}</div>
        </div>
      </div>

      {/* Price List */}
      {Object.keys(groupedByCompany).length === 0 ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Нет пакетов услуг</h3>
          <p className="mt-1 text-sm text-gray-500">
            {isViewOnly ? 'В данный момент нет доступных пакетов услуг.' : 'Начните с создания первого пакета услуг.'}
          </p>
          {!isViewOnly && (
            <button
              onClick={() => setShowAddModal(true)}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Добавить пакет
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedByCompany).map(([companyName, countries]) => (
            <div key={companyName} className="bg-white rounded-lg shadow overflow-hidden">
              <div className="bg-gray-50 px-6 py-4 border-b">
                <h2 className="text-xl font-semibold text-gray-900">{companyName}</h2>
              </div>
              <div className="p-6">
                {Object.entries(countries).map(([country, items]) => (
                  <div key={country} className="mb-6 last:mb-0">
                    <h3 className="text-lg font-medium text-gray-800 mb-3 flex items-center">
                      <span className="mr-2">{country}</span>
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                        {items.length} пакет{items.length === 1 ? '' : items.length < 5 ? 'а' : 'ов'}
                      </span>
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {items.map((item) => (
                        <div 
                          key={item.id} 
                          className={`border border-gray-200 rounded-lg p-4 hover:shadow-md hover:border-blue-300 transition-all ${!isViewOnly ? 'cursor-pointer' : ''} group`}
                          onClick={!isViewOnly ? () => handleEdit(item) : undefined}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <h4 className={`font-semibold text-gray-900 text-sm transition-colors ${!isViewOnly ? 'group-hover:text-blue-600' : ''}`}>{item.title}</h4>
                            {!isViewOnly && (
                              <div className="flex space-x-2">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEdit(item);
                                  }}
                                  className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors opacity-0 group-hover:opacity-100"
                                  title="Редактировать"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteClick(item);
                                  }}
                                  className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors opacity-0 group-hover:opacity-100"
                                  title="Удалить"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </button>
                              </div>
                            )}
                          </div>
                          <div className="space-y-1 text-sm text-gray-600">
                            <div className="flex justify-between">
                              <span>Цена:</span>
                              <span className="font-semibold text-green-600">{item.price.toLocaleString()} теңге</span>
                            </div>
                            {item.duration && (
                              <div className="flex justify-between">
                                <span>Тип:</span>
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  item.duration === 'С приглашением' 
                                    ? 'bg-blue-100 text-blue-800' 
                                    : 'bg-green-100 text-green-800'
                                }`}>
                                  {item.duration}
                                </span>
                              </div>
                            )}
                            {item.description && (
                              <div className="mt-2">
                                <span className="text-xs text-gray-500">{item.description}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            <div className="px-6 py-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">
                {editingItem ? 'Редактировать пакет' : 'Добавить пакет'}
              </h3>
            </div>
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {isSuperAdmin && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Компания *
                  </label>
                  <select
                    value={formData.company_id}
                    onChange={(e) => setFormData({ ...formData, company_id: e.target.value })}
                    required
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Выберите компанию</option>
                    {companies.map(company => (
                      <option key={company.id} value={company.id}>{company.name}</option>
                    ))}
                  </select>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Страна *
                </label>
                <select
                  value={formData.country}
                  onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                  required
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {countries.map(country => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Название пакета *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Например: Туристическая виза"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Цена (теңге) *
                </label>
                <input
                  type="number"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  required
                  min="0"
                  step="0.01"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Тип обработки
                </label>
                <select
                  value={formData.duration}
                  onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Выберите тип</option>
                  <option value="С приглашением">С приглашением</option>
                  <option value="Без приглашения">Без приглашения</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Описание
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Дополнительная информация о пакете"
                />
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Отмена
                </button>
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {submitting ? 'Сохранение...' : (editingItem ? 'Обновить' : 'Добавить')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && deletingItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            <div className="px-6 py-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">
                Подтвердите удаление
              </h3>
            </div>
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">
                    Вы уверены, что хотите удалить пакет услуг?
                  </p>
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    <p className="font-semibold text-gray-900">{deletingItem.title}</p>
                    <p className="text-sm text-gray-600">{deletingItem.country} • {deletingItem.price.toLocaleString()} теңге</p>
                    {deletingItem.description && (
                      <p className="text-xs text-gray-500 mt-1">{deletingItem.description}</p>
                    )}
                  </div>
                  <p className="text-xs text-red-600 mt-2">
                    Это действие нельзя отменить.
                  </p>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleDeleteCancel}
                  disabled={deleting}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  Отмена
                </button>
                <button
                  type="button"
                  onClick={handleDeleteConfirm}
                  disabled={deleting}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                >
                  {deleting ? 'Удаление...' : 'Удалить'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 