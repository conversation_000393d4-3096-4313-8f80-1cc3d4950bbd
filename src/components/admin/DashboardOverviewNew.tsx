import { useState, useEffect } from 'react';
import { Admin, AnalyticsData, AddClientFormData, PriceListItem, DashboardOverviewProps } from '../../types/admin';
import { useToast } from '../../hooks/useToast';
import { getAllStepsForCountry, getStepLabel } from '../../config/workflows';
import { useTranslation } from '../../utils/localization';
import * as XLSX from 'xlsx';
import { useRouter } from 'next/router';
// Custom phone mask implementation for React 19 compatibility
import HelpModal from './HelpModal';
import { useDateFilter } from '../../contexts/DateFilterContext';

// Country mapping for normalization
const COUNTRY_MAPPING: { [key: string]: string } = {
  'USA': 'US',
  'United States': 'US',
  'US': 'US',
  'UK': 'UK',
  'United Kingdom': 'UK',
  'EU': 'EU',
  'Europe': 'EU',
  'European Union': 'EU',
  'China': 'CN',
  'CN': 'CN'
};

// Safe date formatting function
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) {
    return 'Дата не указана';
  }
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Неверная дата';
    }
    
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Ошибка даты';
  }
};

// Function to normalize country names
const normalizeCountry = (country: string): string => {
  return COUNTRY_MAPPING[country] || country;
};

// Component for skeleton loading with beautiful shimmer effect
const SkeletonCard = () => (
  <div className="bg-white rounded-lg shadow p-6 relative overflow-hidden">
    <div className="animate-pulse">
      <div className="flex items-center">
        <div className="p-2 bg-gray-200 rounded-lg w-10 h-10"></div>
        <div className="ml-4 flex-1">
          <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
          <div className="h-8 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>
    {/* Shimmer overlay */}
    <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
  </div>
);

const SkeletonClientList = () => (
  <div className="bg-white overflow-hidden shadow rounded-lg relative">
    <div className="px-4 py-5 sm:p-6">
      <div className="animate-pulse">
        <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="relative">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-24 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-40"></div>
                </div>
                <div className="flex flex-col items-end space-y-1">
                  <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                  <div className="h-4 bg-gray-200 rounded w-12"></div>
                </div>
              </div>
              {/* Individual shimmer for each item */}
              <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/30 to-transparent" style={{animationDelay: `${i * 0.2}s`}}></div>
            </div>
          ))}
        </div>
      </div>
    </div>
    {/* Main shimmer overlay */}
    <div className="absolute inset-0 -translate-x-full animate-[shimmer_3s_infinite] bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>
  </div>
);

const DashboardOverview: React.FC<DashboardOverviewProps> = ({ admin, analytics: initialAnalytics, onRefresh, selectedGlobalCompany, metricsLoading = false }) => {
  const { success, error } = useToast();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(initialAnalytics);
  const { dateRange, setDateRange, resetDateRange } = useDateFilter();
  const [showAddClientModal, setShowAddClientModal] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [priceList, setPriceList] = useState<PriceListItem[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [addClientForm, setAddClientForm] = useState<AddClientFormData>({
    name: '',
    surname: '',
    phone: '',
    country: 'США',
    service_package_id: '',
    consular_fee_paid: false
  });
  const router = useRouter();
  const { t } = useTranslation();

  // Fetch price list when modal opens
  useEffect(() => {
    if (showAddClientModal && admin.companyId) {
      fetchPriceList();
    }
  }, [showAddClientModal, admin.companyId]);

  // Handle ESC key press for modal
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showAddClientModal) {
        setShowAddClientModal(false);
      }
    };

    if (showAddClientModal) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [showAddClientModal]);

  const fetchPriceList = async () => {
    try {
      const response = await fetch(`/api/admin/price-list?companyId=${admin.companyId}`);
      const data = await response.json();
      
      if (response.ok) {
        setPriceList(data.packages || []);
      }
    } catch (err) {
      console.error('Error fetching price list:', err);
    }
  };

  // Filter price list by selected country
  const availablePackages = priceList.filter(item => item.country === addClientForm.country);

  const handleAddClient = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/admin/add-client', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(addClientForm),
      });

      const data = await response.json();

      if (response.ok) {
        success('Успех', 'Клиент добавлен успешно');
        setShowAddClientModal(false);
        setAddClientForm({
          name: '',
          surname: '',
          phone: '',
          country: 'США',
          service_package_id: '',
          consular_fee_paid: false
        });
        onRefresh();
      } else {
        if (data.error === 'phoneAlreadyExists') {
          error(t('common.error'), t('clients.modal.phoneAlreadyExists'));
        } else if (data.error === 'User already exists') {
          error(t('common.error'), 'Пользователь уже существует');
        } else {
          error(t('common.error'), data.message || data.error || 'Произошла ошибка при добавлении клиента');
        }
      }
    } catch (err) {
      error('Ошибка', 'Произошла ошибка при добавлении клиента');
    }
  };

  // Handle refresh with loading state and user feedback
  const handleRefresh = async () => {
    try {
      // Fetch fresh analytics data with current filters and show loading
      if (dateRange) {
        await fetchAnalytics(dateRange.from, dateRange.to, true);
      } else {
        await fetchAnalytics(undefined, undefined, true);
      }
      
      // Also trigger the parent onRefresh callback if provided
      if (onRefresh) {
        await onRefresh();
      }
      
      // Show success feedback
      success(t('common.success'), t('dashboard.dataRefreshed'));
    } catch (err) {
      console.error('Error refreshing dashboard data:', err);
      error(t('common.error'), t('dashboard.refreshError'));
    }
  };

  const handleExportData = () => {
    try {
      if (!analytics || !analytics.pendingPayments) {
        alert('Нет данных для экспорта');
        return;
      }

      // Prepare export data with proper formatting
      const exportData = analytics.pendingPayments.map((client: any, index: number) => {
        const formData = client.form_data as Record<string, any> || {};
        const name = formData.name || client.name || '';
        const surname = formData.surname || client.surname || '';
        const phone = formData.phone || client.phone_number || '';
        const email = formData.email || client.email || '';
        const visaCountry = formData.visaCountry || client.visa_country || '';
        const profession = formData.profession || formData.occupation || '';
        const income = formData.income || formData.salary || '';
        const age = formData.age || '';
        const servicePackage = client.service_package?.title || '';
        const packagePrice = client.service_package?.price ? Number(client.service_package.price).toLocaleString() : '';
        const createdDate = formatDate(client.created_at);
        
        return {
          '№': index + 1,
          'ID клиента': client.id,
          'Имя': name,
          'Фамилия': surname,
          'Телефон': phone,
          'Email': email,
          'Возраст': age,
          'Страна визы': visaCountry,
          'Профессия': profession,
          'Доход': income,
          'Пакет услуг': servicePackage,
          'Стоимость (тенге)': packagePrice,
          'Дата создания': createdDate,
          'Статус': 'Ожидает оплаты',
          'Приоритет': 'Высокий'
        };
      });

      if (exportData.length === 0) {
        alert('Нет данных для экспорта');
        return;
      }

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // Set column widths for better readability
      const colWidths = [
        { wch: 5 },   // №
        { wch: 35 },  // ID клиента
        { wch: 15 },  // Имя
        { wch: 15 },  // Фамилия
        { wch: 15 },  // Телефон
        { wch: 25 },  // Email
        { wch: 8 },   // Возраст
        { wch: 12 },  // Страна визы
        { wch: 20 },  // Профессия
        { wch: 12 },  // Доход
        { wch: 30 },  // Пакет услуг
        { wch: 15 },  // Стоимость
        { wch: 15 },  // Дата создания
        { wch: 18 },  // Статус
        { wch: 12 }   // Приоритет
      ];
      ws['!cols'] = colWidths;

      // Style the header row
      const headerRange = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      
      // Apply header styles
      for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        
        ws[cellAddress].s = {
          fill: { 
            fgColor: { rgb: "DC2626" } // Red background for pending payments
          },
          font: { 
            color: { rgb: "FFFFFF" }, // White text
            bold: true,
            sz: 12
          },
          alignment: { 
            horizontal: "center",
            vertical: "center",
            wrapText: true
          },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
          }
        };
      }

      // Apply alternating row colors and formatting
      for (let row = 1; row <= headerRange.e.r; row++) {
        const isEvenRow = row % 2 === 0;
        const bgColor = isEvenRow ? "FEF2F2" : "FFFFFF"; // Light red for even rows
        
        for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) continue;
          
          // Get column header to apply specific colors
          const headerCell = XLSX.utils.encode_cell({ r: 0, c: col });
          const columnHeader = ws[headerCell]?.v || '';
          
          let cellBgColor = bgColor;
          let fontColor = "000000";
          
          // Apply specific colors based on content
          if (columnHeader.includes('Статус')) {
            cellBgColor = "FED7AA"; // Orange for pending
            fontColor = "C2410C"; // Dark orange
          } else if (columnHeader.includes('Приоритет')) {
            cellBgColor = "FEE2E2"; // Light red
            fontColor = "991B1B"; // Dark red
          } else if (columnHeader.includes('Доход')) {
            const value = ws[cellAddress].v;
            if (value && Number(value) > 50000) {
              cellBgColor = "D1FAE5"; // Light green for high income
              fontColor = "065F46"; // Dark green
            }
          }
          
          ws[cellAddress].s = {
            fill: { fgColor: { rgb: cellBgColor } },
            font: { 
              color: { rgb: fontColor },
              sz: 10
            },
            alignment: { 
              horizontal: columnHeader.includes('№') || columnHeader.includes('Возраст') || columnHeader.includes('Доход') ? "center" : "left",
              vertical: "center",
              wrapText: true
            },
            border: {
              top: { style: "thin", color: { rgb: "E5E7EB" } },
              bottom: { style: "thin", color: { rgb: "E5E7EB" } },
              left: { style: "thin", color: { rgb: "E5E7EB" } },
              right: { style: "thin", color: { rgb: "E5E7EB" } }
            }
          };
        }
      }

      // Add the worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Ожидают оплаты');

      // Generate filename with date
      const dateStr = new Date().toISOString().split('T')[0];
      const filename = `pending_payments_${dateStr}.xlsx`;

      // Write the file
      XLSX.writeFile(wb, filename);
      
      alert(`Файл ${filename} создан с цветовым кодированием`);
    } catch (error) {
      console.error('Export error:', error);
      alert('Ошибка при экспорте данных');
    }
  };

  // Fetch analytics for selected period
  const fetchAnalytics = async (from?: string, to?: string, showLoading = false) => {
    try {
      if (showLoading) {
        setIsRefreshing(true);
      }
      
      const params = new URLSearchParams();
      if (from && to) {
        params.append('dateFrom', from);
        params.append('dateTo', to);
      }
      // Apply company filter for super_admin with selected company or regular admin with their company
      if (admin.role === 'super_admin' && selectedGlobalCompany && selectedGlobalCompany !== 'all') {
        params.append('companyId', selectedGlobalCompany);
      } else if (admin.role === 'super_admin' && selectedGlobalCompany === 'all') {
        // For "all companies", don't add companyId filter
        params.append('companyId', 'all');
      } else if (admin.companyId) {
        params.append('companyId', admin.companyId);
      }
      const response = await fetch(`/api/admin/analytics?${params}`);
      const data = await response.json();
      if (response.ok) {
        setAnalytics(data);
      }
    } catch (err) {
      console.error('Ошибка при загрузке аналитики:', err);
    } finally {
      if (showLoading) {
        setIsRefreshing(false);
      }
    }
  };

  // On mount and when dateRange or selectedGlobalCompany changes, fetch analytics
  useEffect(() => {
    if (dateRange) {
      fetchAnalytics(dateRange.from, dateRange.to);
    } else {
      fetchAnalytics(); // Без периода — все заявки
    }
  }, [dateRange, admin.companyId, selectedGlobalCompany]);

  // Обработчик сброса фильтра
  const handleResetFilter = () => {
    resetDateRange();
  };

  if (!analytics) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">{t('dashboard.title')}</h1>
        </div>
        <div className="text-center py-12">
          <div className="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
          <p className="mt-2 text-gray-500">Загрузка данных...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Action Buttons Row */}
      <div className="flex items-center justify-end space-x-4 mb-6">
        {admin.role !== 'super_admin' && (
          <button
            onClick={() => setShowAddClientModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors shadow-sm"
            title={t('dashboard.addClient')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {t('dashboard.addClient')}
          </button>
        )}
        
        <button
          onClick={handleRefresh}
          disabled={isRefreshing || metricsLoading}
          className={`inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg transition-colors shadow-sm ${
            isRefreshing || metricsLoading
              ? 'text-gray-400 bg-gray-100 cursor-not-allowed' 
              : 'text-gray-700 bg-white hover:bg-gray-50'
          }`}
          title={t('dashboard.refreshData')}
        >
          {isRefreshing || metricsLoading ? (
            <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
          )}
          {isRefreshing || metricsLoading ? t('common.loading') : t('dashboard.refresh')}
        </button>

        <button
          onClick={handleExportData}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors shadow-sm"
          title={t('dashboard.exportExcel')}
        >
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          {t('dashboard.exportExcel')}
        </button>

        <button
          onClick={() => setShowHelpModal(true)}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors shadow-sm"
          title={t('dashboard.help')}
        >
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          {t('dashboard.help')}
        </button>
      </div>

      {/* Display current date range */}
      <div className="text-sm text-gray-600 mb-6">
        Период: {formatDate(dateRange.from)} — {formatDate(dateRange.to)}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {!analytics ? (
          // Show skeleton loading when no data
          <>
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
          </>
        ) : (
          // Show actual data
          <>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">{t('dashboard.totalClients')}</p>
                  <div className="text-2xl font-semibold text-gray-900">
                    {metricsLoading || isRefreshing ? (
                      <span className="animate-pulse text-gray-400 blur-sm">{analytics.metrics.filteredTotalClients ?? analytics.metrics.totalClients}</span>
                    ) : (
                      analytics.metrics.filteredTotalClients ?? analytics.metrics.totalClients
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">{t('dashboard.purchased')}</p>
                  <div className="text-2xl font-semibold text-gray-900">
                    {metricsLoading || isRefreshing ? (
                      <span className="animate-pulse text-gray-400 blur-sm">{analytics.metrics.filteredPaidClients ?? analytics.metrics.paidClients}</span>
                    ) : (
                      analytics.metrics.filteredPaidClients ?? analytics.metrics.paidClients
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                    <path fillRule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100-2H7a1 1 0 000 2zm3 0a1 1 0 100-2h3a1 1 0 000 2h-3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">{t('dashboard.submitted')}</p>
                  <div className="text-2xl font-semibold text-gray-900">
                    {metricsLoading || isRefreshing ? (
                      <span className="animate-pulse text-gray-400 blur-sm">{analytics.metrics.filteredSubmittedApplications ?? analytics.metrics.submittedApplications}</span>
                    ) : (
                      analytics.metrics.filteredSubmittedApplications ?? analytics.metrics.submittedApplications
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <svg className="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">{t('dashboard.income')}</p>
                  <div className="text-2xl font-semibold text-gray-900">
                    {metricsLoading || isRefreshing ? (
                      <span className="animate-pulse text-gray-400 blur-sm">{(analytics.metrics.filteredTotalRevenue ?? analytics.metrics.totalRevenue).toLocaleString()} ₸</span>
                    ) : (
                      (analytics.metrics.filteredTotalRevenue ?? analytics.metrics.totalRevenue).toLocaleString() + ' ₸'
                    )}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {!analytics ? (
          // Show skeleton loading when no data
          <>
            <SkeletonClientList />
            <SkeletonClientList />
          </>
        ) : (
          // Show actual data
          <>
            {/* Waiting for Payment */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {t('dashboard.awaitingPayment')} ({analytics.pendingPayments.length})
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {t('clients.unpaidClientsDescription')}
                </p>
                <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar" style={{ scrollbarWidth: 'thin', scrollbarColor: '#f97316 transparent' }}>
                  {analytics.pendingPayments.map((client: any) => (
                    <div key={client.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {(() => {
                            const formData = client.form_data as Record<string, any> || {};
                            const name = formData.name || client.name || '';
                            const surname = formData.surname || client.surname || '';
                            return name && surname ? `${name} ${surname}` : 'Имя не указано';
                          })()}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(() => {
                            const formData = client.form_data as Record<string, any> || {};
                            return formData.phone || client.phone || 'Телефон не указан';
                          })()}
                        </p>
                        <p className="text-xs text-orange-600">
                          💳 Требуется оплата пакета услуг
                        </p>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          Не оплачено
                        </span>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            
                            // Redirect to clients page with focus on this client
                            if (typeof window !== 'undefined') {
                              console.log('Redirecting to client:', client);
                              
                              // Try to get country from different possible fields
                              let visaCountry = '';
                              if (client.form_data) {
                                const formData = client.form_data as Record<string, any>;
                                visaCountry = formData.visaCountry || formData.country || '';
                              } else {
                                // Fallback to direct properties
                                visaCountry = client.visaCountry || client.country || client.visa_country || '';
                              }
                              
                              const normalizedCountry = normalizeCountry(visaCountry) || 'US'; // Default to US if no country
                              const redirectUrl = `/admin/dashboard?tab=clients&country=${normalizedCountry}&focus=${client.id}`;
                              
                              console.log('Redirect URL:', redirectUrl);
                              router.push(redirectUrl);
                            }
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Изменить
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Requires Fixes */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {t('dashboard.requiresFixes')} ({analytics.requiresFixes.length})
                </h3>
                <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar" style={{ scrollbarWidth: 'thin', scrollbarColor: '#ef4444 transparent' }}>
                  {analytics.requiresFixes.map((client: any) => (
                    <div key={client.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {(() => {
                            const formData = client.form_data as Record<string, any> || {};
                            const name = formData.name || client.name || '';
                            const surname = formData.surname || client.surname || '';
                            return name && surname ? `${name} ${surname}` : 'Имя не указано';
                          })()}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(() => {
                            const formData = client.form_data as Record<string, any> || {};
                            return formData.phone || client.phone || 'Телефон не указан';
                          })()}
                        </p>
                        <p className="text-xs text-red-600">
                          {client.fix_comment || client.comment || t('dashboard.requiresFixesComment')}
                        </p>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Правки
                        </span>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            
                            // Redirect to clients page with focus on this client
                            if (typeof window !== 'undefined') {
                              console.log('Redirecting to client:', client);
                              
                              // Try to get country from different possible fields
                              let visaCountry = '';
                              if (client.form_data) {
                                const formData = client.form_data as Record<string, any>;
                                visaCountry = formData.visaCountry || formData.country || '';
                              } else {
                                // Fallback to direct properties
                                visaCountry = client.visaCountry || client.country || client.visa_country || '';
                              }
                              
                              const normalizedCountry = normalizeCountry(visaCountry) || 'US'; // Default to US if no country
                              const redirectUrl = `/admin/dashboard?tab=clients&country=${normalizedCountry}&focus=${client.id}`;
                              
                              console.log('Redirect URL:', redirectUrl);
                              router.push(redirectUrl);
                            }
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Изменить
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Add Client Modal - Only show for visa admins */}
      {admin.role !== 'super_admin' && showAddClientModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-6 border w-full max-w-4xl shadow-lg rounded-lg bg-white min-h-[600px]">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Добавить оплаченного клиента
              </h3>
              <button
                onClick={() => setShowAddClientModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleAddClient} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Имя *
                  </label>
                  <input
                    type="text"
                    required
                    value={addClientForm.name}
                    onChange={(e) => setAddClientForm(prev => ({ ...prev, name: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Введите имя"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Фамилия *
                  </label>
                  <input
                    type="text"
                    required
                    value={addClientForm.surname}
                    onChange={(e) => setAddClientForm(prev => ({ ...prev, surname: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Введите фамилию"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Номер телефона *
                </label>
                <input
                  type="tel"
                  value={addClientForm.phone}
                  onChange={(e) => {
                    const formatPhoneValue = (value: string): string => {
                      // Remove all non-digits except +
                      let cleaned = value.replace(/[^\d+]/g, '');
                      
                      // Ensure it starts with +7
                      if (!cleaned.startsWith('+7')) {
                        if (cleaned.startsWith('7')) {
                          cleaned = '+' + cleaned;
                        } else if (cleaned.startsWith('8')) {
                          cleaned = '+7' + cleaned.slice(1);
                        } else {
                          cleaned = '+7' + cleaned.replace(/^\+/, '');
                        }
                      }
                      
                      // Extract digits after +7
                      const digits = cleaned.slice(2);
                      
                      // Limit to 10 digits and format with parentheses
                      if (digits.length === 0) return '+7';
                      if (digits.length <= 3) return `+7 (${digits}`;
                      if (digits.length <= 6) return `+7 (${digits.slice(0, 3)}) ${digits.slice(3)}`;
                      if (digits.length <= 8) return `+7 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
                      return `+7 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 8)}-${digits.slice(8, 10)}`;
                    };
                    
                    const formatted = formatPhoneValue(e.target.value);
                    setAddClientForm(prev => ({ ...prev, phone: formatted }));
                  }}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="+7 (777) 123-45-67"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Страна визы *
                </label>
                <select
                  value={addClientForm.country}
                  onChange={(e) => {
                    setAddClientForm(prev => ({ 
                      ...prev, 
                      country: e.target.value,
                      service_package_id: '' // Reset package when country changes
                    }));
                  }}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="США">🇺🇸 США</option>
                  <option value="Великобритания">🇬🇧 Великобритания</option>
                  <option value="Германия">🇩🇪 Германия</option>
                  <option value="Китай">🇨🇳 Китай</option>
                  <option value="Канада">🇨🇦 Канада</option>
                  <option value="Франция">🇫🇷 Франция</option>
                  <option value="Австралия">🇦🇺 Австралия</option>
                  <option value="Шенген">🇪🇺 Шенген</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Пакет услуг *
                </label>
                <select
                  value={addClientForm.service_package_id}
                  onChange={(e) => setAddClientForm(prev => ({ ...prev, service_package_id: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">Выберите пакет</option>
                  {availablePackages.map(pkg => (
                    <option key={pkg.id} value={pkg.id}>
                      {pkg.country} — {pkg.title} ({pkg.price.toLocaleString()} теңге)
                    </option>
                  ))}
                </select>
                {availablePackages.length === 0 && (
                  <div className="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-md">
                    <div className="flex items-center justify-between">
                      <div className="flex">
                        <svg className="h-5 w-5 text-orange-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <div className="ml-3">
                          <p className="text-sm text-orange-800 font-medium">
                            Нету прайлиста для этой страны
                          </p>
                          <p className="text-xs text-orange-600 mt-1">
                            Создайте пакеты услуг для выбранной страны в разделе "Прайс-лист"
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          router.push('/admin/dashboard?tab=price-list');
                        }}
                        className="ml-4 px-3 py-1.5 bg-orange-600 text-white text-xs font-medium rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
                      >
                        Добавить
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="consular_fee_paid"
                  checked={addClientForm.consular_fee_paid}
                  onChange={(e) => setAddClientForm(prev => ({ ...prev, consular_fee_paid: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="consular_fee_paid" className="ml-2 block text-sm text-gray-900">
                  Консульский сбор оплачен
                </label>
              </div>

              {addClientForm.consular_fee_paid && (
                <div className="bg-green-50 border border-green-200 rounded-md p-3">
                  <div className="flex">
                    <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div className="ml-3">
                      <p className="text-sm text-green-800">
                        Клиент сможет записаться на прием в консульство после прохождения опросника
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <div className="flex">
                  <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <div className="ml-3">
                    <p className="text-sm text-blue-800">
                      После добавления клиента бот автоматически отправит ему ссылку на опросник для выбранной страны
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddClientModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Отмена
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {metricsLoading ? 'Добавление...' : 'Добавить клиента'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Help Modal */}
      <HelpModal 
        isOpen={showHelpModal} 
        onClose={() => setShowHelpModal(false)} 
      />
    </div>
  );
}

export default DashboardOverview; 