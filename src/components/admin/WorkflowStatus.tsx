interface WorkflowStatusProps {
  currentStep: string;
  steps: string[];
  onStepChange: (step: string) => void;
}

export default function WorkflowStatus({ currentStep, steps, onStepChange }: WorkflowStatusProps) {
  const currentStepIndex = steps.indexOf(currentStep);
  const stepIndex = currentStepIndex >= 0 ? currentStepIndex : 0;

  return (
    <div className="flex items-center space-x-2">
      <div className="relative">
        <select
          value={currentStep}
          onChange={(e) => onStepChange(e.target.value)}
          className="block w-48 pl-3 pr-10 py-1 text-xs border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
        >
          {steps.map((step, index) => (
            <option key={step} value={step}>
              {index + 1}. {step}
            </option>
          ))}
        </select>
      </div>
      
      {/* Progress Indicator */}
      <div className="flex items-center space-x-1">
        <div className="w-16 bg-gray-200 rounded-full h-1.5">
          <div 
            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
            style={{ width: `${((stepIndex + 1) / steps.length) * 100}%` }}
          />
        </div>
        <span className="text-xs text-gray-500 ml-1">
          {stepIndex + 1}/{steps.length}
        </span>
      </div>
    </div>
  );
} 