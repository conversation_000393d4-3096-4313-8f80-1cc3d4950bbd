import { useState, useEffect, useCallback, useRef } from 'react';
// Custom phone mask implementation for React 19 compatibility
import { AddClientFormData, PriceListItem } from '../../types/admin';
import { useToast } from '../../hooks/useToast';
import { useTranslation } from '../../utils/localization';
import { 
  getCountryDisplayName, 
  getCountryVariations,
  COUNTRY_MAPPINGS 
} from '../../types/client-status';

interface AddClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  companyId?: string;
  selectedCountry?: string;
  userRole?: string;
}

export default function AddClientModal({ isOpen, onClose, onSave, companyId, selectedCountry, userRole }: AddClientModalProps) {
  const { t } = useTranslation();
  const { success, error } = useToast();
  const [formData, setFormData] = useState<AddClientFormData>({
    name: '',
    surname: '',
    phone: '',
    country: selectedCountry || 'US',
    service_package_id: '',
    consular_fee_paid: false
  });
  const [packages, setPackages] = useState<PriceListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const fetchPackages = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (companyId) {
        params.append('companyId', companyId);
      }
      
      const response = await fetch(`/api/admin/price-list?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        let filteredPackages = data.packages;
        
        // Filter packages by selected country if available
        if (selectedCountry) {
          const countryVariations = getCountryVariations(selectedCountry);
          filteredPackages = data.packages.filter((pkg: PriceListItem) => 
            countryVariations.some(variation => 
              pkg.country.toLowerCase().includes(variation.toLowerCase()) ||
              variation.toLowerCase().includes(pkg.country.toLowerCase())
            )
          );
          
          // If no packages found with variations, show all packages to avoid empty dropdown
          if (filteredPackages.length === 0) {
            console.warn(`No packages found for country ${selectedCountry}, showing all packages`);
            filteredPackages = data.packages;
          }
        }
        
        setPackages(filteredPackages);
        
        // Auto-select first package if only one available
        if (filteredPackages.length === 1) {
          setFormData(prev => ({ ...prev, service_package_id: filteredPackages[0].id }));
        }
      }
    } catch {
      console.error('Ошибка при загрузке пакетов');
    }
  }, [companyId, selectedCountry]);

  useEffect(() => {
    if (isOpen) {
      fetchPackages();
      // Reset form when modal opens
      setFormData({
        name: '',
        surname: '',
        phone: '',
        country: selectedCountry || 'US',
        service_package_id: '',
        consular_fee_paid: false
      });
      setValidationErrors({});
    }
  }, [isOpen, selectedCountry, fetchPackages]);

  // Handle ESC key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [isOpen, onClose]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Имя обязательно для заполнения';
    }
    if (!formData.surname.trim()) {
      errors.surname = 'Фамилия обязательна для заполнения';
    }
    if (!formData.phone.trim()) {
      errors.phone = 'Номер телефона обязателен для заполнения';
    } else if (!/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      errors.phone = 'Введите корректный номер телефона';
    }
    if (!formData.service_package_id) {
      errors.service_package_id = 'Выберите пакет услуг';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Debounced phone check
  const checkPhoneTimeout = useRef<NodeJS.Timeout | null>(null);

  const checkPhoneExists = async (phone: string) => {
    if (!phone || phone.length < 10) return;
    
    try {
      const response = await fetch(`/api/admin/check-client?phone=${encodeURIComponent(phone)}`);
      const data = await response.json();
      
      if (response.ok && data.exists) {
        setValidationErrors(prev => ({
          ...prev,
          phone: data.message || 'Клиент с таким номером уже существует'
        }));
      }
    } catch (error) {
      console.error('Error checking phone:', error);
    }
  };

  const handleInputChange = (field: keyof AddClientFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Check phone number availability when phone field changes
    if (field === 'phone' && typeof value === 'string') {
      // Clear previous timeout
      if (checkPhoneTimeout.current) {
        clearTimeout(checkPhoneTimeout.current);
      }
      
      // Set new timeout to check phone after user stops typing
      checkPhoneTimeout.current = setTimeout(() => {
        checkPhoneExists(value);
      }, 800);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // First check if client already exists
      const checkResponse = await fetch(`/api/admin/check-client?phone=${encodeURIComponent(formData.phone)}`);
      const checkData = await checkResponse.json();
      
      if (checkResponse.ok && checkData.exists) {
        error(t('clients.modal.phoneAlreadyExists'), checkData.translationKey ? 
          t(checkData.translationKey) : 
          `Клиент с номером ${formData.phone} уже зарегистрирован в системе`);
        setLoading(false);
        return;
      }

      // Convert country code to Russian name for consistency with filtering

      // Create new client
      const response = await fetch('/api/admin/add-client', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          country: getCountryDisplayName(formData.country), // Ensure consistent country format
          company_id: companyId,
          userRole: userRole
        }),
      });

      const data = await response.json();

      if (response.ok) {
        success('Клиент успешно добавлен', 'WhatsApp уведомление отправлено клиенту о покупке пакета');
        // Immediately trigger refresh without delay
        onSave();
        onClose();
      } else {
        // Handle specific phone number conflict error
        if (response.status === 409) {
          if (data.error === 'phoneAlreadyExists') {
            error(t('clients.modal.phoneAlreadyExists'), data.details || t('clients.modal.phoneNumberTaken'));
          } else {
            error(t('clients.modal.phoneNumberTaken'), data.details || data.message);
          }
        } else {
          error(t('common.error'), data.message || data.error || 'Неизвестная ошибка');
        }
      }
    } catch {
      error('Ошибка при добавлении клиента', 'Произошла ошибка при отправке данных');
    } finally {
      setLoading(false);
    }
  };

  const selectedPackage = packages.find(pkg => pkg.id === formData.service_package_id);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-6 border w-full max-w-4xl shadow-lg rounded-lg bg-white min-h-[600px]">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
{t('clients.addPaidClient')}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Имя *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  validationErrors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Введите имя"
              />
              {validationErrors.name && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Фамилия *
              </label>
              <input
                type="text"
                value={formData.surname}
                onChange={(e) => handleInputChange('surname', e.target.value)}
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  validationErrors.surname ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Введите фамилию"
              />
              {validationErrors.surname && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.surname}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Номер телефона *
            </label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => {
                const formatPhoneValue = (value: string): string => {
                  // Remove all non-digits except +
                  let cleaned = value.replace(/[^\d+]/g, '');
                  
                  // Ensure it starts with +7
                  if (!cleaned.startsWith('+7')) {
                    if (cleaned.startsWith('7')) {
                      cleaned = '+' + cleaned;
                    } else if (cleaned.startsWith('8')) {
                      cleaned = '+7' + cleaned.slice(1);
                    } else {
                      cleaned = '+7' + cleaned.replace(/^\+/, '');
                    }
                  }
                  
                  // Extract digits after +7
                  const digits = cleaned.slice(2);
                  
                  // Limit to 10 digits and format with parentheses
                  if (digits.length === 0) return '+7';
                  if (digits.length <= 3) return `+7 (${digits}`;
                  if (digits.length <= 6) return `+7 (${digits.slice(0, 3)}) ${digits.slice(3)}`;
                  if (digits.length <= 8) return `+7 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
                  return `+7 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 8)}-${digits.slice(8, 10)}`;
                };
                
                const formatted = formatPhoneValue(e.target.value);
                handleInputChange('phone', formatted);
              }}
              className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                validationErrors.phone ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="+7 (777) 123-45-67"
            />
            {validationErrors.phone && (
              <p className="mt-1 text-sm text-red-600">{validationErrors.phone}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Пакет услуг *
            </label>
            <select
              value={formData.service_package_id}
              onChange={(e) => handleInputChange('service_package_id', e.target.value)}
              className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                validationErrors.service_package_id ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Выберите пакет</option>
              {packages.map((pkg) => (
                <option key={pkg.id} value={pkg.id}>
                  {pkg.country} — {pkg.title} ({pkg.price.toLocaleString()} теңге)
                </option>
              ))}
            </select>
            {validationErrors.service_package_id && (
              <p className="mt-1 text-sm text-red-600">{validationErrors.service_package_id}</p>
            )}
            {selectedPackage && (
              <p className="mt-1 text-sm text-gray-600">
                {selectedPackage.description}
              </p>
            )}
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="consular_fee_paid"
              checked={formData.consular_fee_paid}
              onChange={(e) => handleInputChange('consular_fee_paid', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="consular_fee_paid" className="ml-2 block text-sm text-gray-900">
              Консульский сбор оплачен
            </label>
          </div>

          {formData.consular_fee_paid && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <div className="flex">
                <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-green-800">
                    Клиент сможет записаться на прием в консульство после прохождения опросника
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex">
              <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-blue-800">
                  После добавления клиента будет отправлено WhatsApp уведомление с подтверждением покупки пакета
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Отмена
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Добавление...' : 'Добавить клиента'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 