import React, { useEffect } from 'react';
import { useTranslation } from '../../utils/localization';

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function HelpModal({ isOpen, onClose }: HelpModalProps) {
  const { t } = useTranslation();

  // Handle ESC key press
  useEffect(() => {
    if (!isOpen) return;
    
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const helpSections = [
    {
      title: t('clients.help.mainFunctions.title'),
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
        </svg>
      ),
      items: [
        t('clients.help.mainFunctions.dashboard'),
        t('clients.help.mainFunctions.clients'),
        t('clients.help.mainFunctions.statistics'),
        t('clients.help.mainFunctions.priceList')
      ]
    },
    {
      title: t('clients.help.clientWork.title'),
      icon: (
        <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
        </svg>
      ),
      items: [
        t('clients.help.clientWork.addClient'),
        t('clients.help.clientWork.filters'),
        t('clients.help.clientWork.processStages'),
        t('clients.help.clientWork.invitations')
      ]
    },
    {
      title: t('clients.help.applicationStatuses.title'),
      icon: (
        <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
        </svg>
      ),
      items: [
        t('clients.help.applicationStatuses.pendingPayment'),
        t('clients.help.applicationStatuses.requiresFixes'),
        t('clients.help.applicationStatuses.waitingInvitation'),
        t('clients.help.applicationStatuses.submitted')
      ]
    },
    {
      title: t('clients.help.exportReports.title'),
      icon: (
        <svg className="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      ),
      items: [
        t('clients.help.exportReports.excelExport'),
        t('clients.help.exportReports.filterExport'),
        t('clients.help.exportReports.periodStatistics'),
        t('clients.help.exportReports.countryAnalytics')
      ]
    },
    {
      title: t('clients.help.hotkeys.title'),
      icon: (
        <svg className="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      ),
      items: [
        t('clients.help.hotkeys.escape'),
        t('clients.help.hotkeys.search'),
        t('clients.help.hotkeys.refresh'),
        t('clients.help.hotkeys.navigation')
      ]
    },
    {
      title: t('clients.help.rolesAccess.title'),
      icon: (
        <svg className="w-6 h-6 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
        </svg>
      ),
      items: [
        t('clients.help.rolesAccess.superAdmin'),
        t('clients.help.rolesAccess.admin'),
        t('clients.help.rolesAccess.manager'),
        t('clients.help.rolesAccess.stageRestrictions')
      ]
    }
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-0 border w-11/12 max-w-4xl shadow-2xl rounded-xl bg-white mb-8">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-t-xl px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white">
                  {t('clients.help.title')}
                </h3>
                <p className="text-blue-100 text-sm">
                  {t('clients.help.subtitle')}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors p-2 hover:bg-white hover:bg-opacity-20 rounded-lg"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[70vh] overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {helpSections.map((section, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg mr-3">
                    {section.icon}
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900">
                    {section.title}
                  </h4>
                </div>
                <ul className="space-y-3">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <div className="flex-shrink-0 w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2 mr-3"></div>
                      <span className="text-sm text-gray-700 leading-relaxed">
                        {item}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Additional Info Section */}
          <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg mr-3">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900">
                {t('clients.help.importantInfo.title')}
              </h4>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <h5 className="font-medium text-gray-900 mb-2">{t('clients.help.importantInfo.security.title')}</h5>
                <p className="text-sm text-gray-700">
                  {t('clients.help.importantInfo.security.description')}
                </p>
              </div>
              <div className="bg-white rounded-lg p-4 border border-purple-200">
                <h5 className="font-medium text-gray-900 mb-2">{t('clients.help.importantInfo.backup.title')}</h5>
                <p className="text-sm text-gray-700">
                  {t('clients.help.importantInfo.backup.description')}
                </p>
              </div>
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <h5 className="font-medium text-gray-900 mb-2">{t('clients.help.importantInfo.support.title')}</h5>
                <p className="text-sm text-gray-700">
                  {t('clients.help.importantInfo.support.description')}
                </p>
              </div>
              <div className="bg-white rounded-lg p-4 border border-orange-200">
                <h5 className="font-medium text-gray-900 mb-2">{t('clients.help.importantInfo.updates.title')}</h5>
                <p className="text-sm text-gray-700">
                  {t('clients.help.importantInfo.updates.description')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 px-6 py-4 bg-gray-50 rounded-b-xl">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Visa AI Admin Panel</span> - {t('clients.help.footer')}
            </div>
            <button
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors shadow-sm"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              {t('clients.help.close')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 