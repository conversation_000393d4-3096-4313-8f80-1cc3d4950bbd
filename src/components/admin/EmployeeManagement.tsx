import React, { useState, useEffect } from 'react';
import { useToast } from '../../hooks/useToast';
import { useTranslation } from '../../utils/localization';

// Safe date formatting function
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) {
    return 'Дата не указана';
  }
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Неверная дата';
    }
    
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Ошибка даты';
  }
};

interface Employee {
  id: string;
  email: string;
  password_hash?: string;
  password_plain?: string; // Plain text password for visa admin visibility
  role: 'visa_admin' | 'manager';
  company_id?: string;
  full_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface EmployeeManagementProps {
  companyId: string;
  userRole: string; // Current user's role to determine permissions
}

export default function EmployeeManagement({ companyId, userRole }: EmployeeManagementProps) {
  const { t } = useTranslation();
  const { success, error } = useToast();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showCredentials, setShowCredentials] = useState<string | null>(null);
  const [showEditModal, setShowEditModal] = useState<string | null>(null);
  const [newEmployee, setNewEmployee] = useState({
    email: '',
    password: '',
    role: 'manager',
    full_name: ''
  });
  const [editEmployee, setEditEmployee] = useState({
    id: '',
    email: '',
    password: '',
    full_name: '',
    is_active: true
  });

  useEffect(() => {
    fetchEmployees();
  }, [companyId]);

  // Handle ESC key press for modals
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowAddModal(false);
        setShowCredentials(null);
        setShowEditModal(null);
      }
    };

    if (showAddModal || showCredentials || showEditModal) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [showAddModal, showCredentials, showEditModal]);

  const fetchEmployees = async () => {
    try {
      const response = await fetch(`/api/admin/employees?companyId=${companyId}`);
      const data = await response.json();
      if (response.ok) {
        // Filter only managers
        const managers = (data.employees || []).filter((emp: Employee) => emp.role === 'manager');
        setEmployees(managers);
      } else {
        error('Ошибка загрузки', 'Не удалось загрузить список менеджеров');
      }
    } catch (err) {
      error('Ошибка сети', 'Проверьте подключение к интернету');
      console.error('Ошибка при загрузке менеджеров:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddEmployee = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      console.log('Sending employee data:', {
        ...newEmployee,
        company_id: companyId
      });

      const response = await fetch('/api/admin/employees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newEmployee,
          company_id: companyId,
          role: 'manager' // Force manager role
        }),
      });

      const data = await response.json();
      console.log('API Response:', { status: response.status, data });

      if (response.ok) {
        success('Успех', 'Менеджер успешно добавлен');
        setShowAddModal(false);
        setNewEmployee({ email: '', password: '', role: 'manager', full_name: '' });
        fetchEmployees();
      } else {
        error('Ошибка', data.error || 'Не удалось добавить менеджера');
      }
    } catch (err) {
      error('Ошибка сети', 'Произошла ошибка при добавлении менеджера');
      console.error('Ошибка при добавлении менеджера:', err);
    }
  };

  const handleEditEmployee = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const updateData: any = {
        email: editEmployee.email,
        full_name: editEmployee.full_name,
        is_active: editEmployee.is_active
      };

      // Only include password if it's provided
      if (editEmployee.password.trim()) {
        updateData.password = editEmployee.password;
      }

      const response = await fetch(`/api/admin/employees?id=${editEmployee.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (response.ok) {
        success('Успех', 'Менеджер успешно обновлен');
        setShowEditModal(null);
        fetchEmployees();
      } else {
        error('Ошибка', data.error || 'Не удалось обновить менеджера');
      }
    } catch (err) {
      error('Ошибка сети', 'Произошла ошибка при обновлении менеджера');
      console.error('Ошибка при обновлении менеджера:', err);
    }
  };

  const openEditModal = (employee: Employee) => {
    setEditEmployee({
      id: employee.id,
      email: employee.email,
      password: '', // Don't pre-fill password for security
      full_name: employee.full_name || '',
      is_active: employee.is_active
    });
    setShowEditModal(employee.id);
  };

  const handleDeleteEmployee = async (employeeId: string) => {
    if (!confirm('Вы уверены, что хотите удалить этого менеджера?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/employees?id=${employeeId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok) {
        success('Успех', 'Менеджер успешно удален');
        fetchEmployees();
      } else {
        error('Ошибка', data.error || 'Не удалось удалить менеджера');
      }
    } catch (err) {
      error('Ошибка сети', 'Произошла ошибка при удалении менеджера');
      console.error('Ошибка при удалении менеджера:', err);
    }
  };

  const generatePassword = (isEdit = false) => {
    const length = 8;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    if (isEdit) {
      setEditEmployee({ ...editEmployee, password });
    } else {
      setNewEmployee({ ...newEmployee, password });
    }
  };

  // Check if current user can manage employees (only visa_admin can)
  const canManageEmployees = userRole === 'visa_admin';

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="mt-2 text-gray-600">Загрузка...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('employees.title')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            {t('employees.description')}
          </p>
        </div>
        {canManageEmployees && (
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <svg className="-ml-1 mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            {t('employees.addManager')}
          </button>
        )}
      </div>

      {/* Managers Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {employees.map((employee) => (
            <li key={employee.id}>
              <div className="px-4 py-4 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-700">
                        {(employee.full_name || employee.email).charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">
                      {employee.full_name || 'Без имени'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {employee.email}
                    </p>
                    <p className="text-xs text-gray-400">
                      Роль: Менеджер • Создан: {formatDate(employee.created_at)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    employee.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {employee.is_active ? 'Активен' : 'Отключен'}
                  </span>
                  
                  <button
                    onClick={() => setShowCredentials(employee.id)}
                    className="text-sm text-blue-600 hover:text-blue-900"
                  >
                    Показать пароль
                  </button>
                  
                  {canManageEmployees && (
                    <>
                      <button
                        onClick={() => openEditModal(employee)}
                        className="text-sm text-green-600 hover:text-green-900"
                      >
                        Редактировать
                      </button>
                      
                      <button
                        onClick={() => handleDeleteEmployee(employee.id)}
                        className="text-sm text-red-600 hover:text-red-900"
                      >
                        Удалить
                      </button>
                    </>
                  )}
                </div>
              </div>
            </li>
          ))}
        </ul>
        
        {employees.length === 0 && (
          <div className="text-center py-8">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 48 48">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M8 20a6 6 0 1112 0v3.764a7.711 7.711 0 01-2.544 5.736C16.384 30.44 16 31.696 16 33v1a2 2 0 002 2h8a2 2 0 002-2v-1c0-1.304-.384-2.56-1.456-3.5A7.711 7.711 0 0124 23.764V20a6 6 0 1112 0" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Нет менеджеров</h3>
            <p className="mt-1 text-sm text-gray-500">Добавьте первого менеджера в вашу команду</p>
          </div>
        )}
      </div>

      {/* Add Manager Modal */}
      {showAddModal && canManageEmployees && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Добавить менеджера</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleAddEmployee} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Полное имя
                </label>
                <input
                  type="text"
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={newEmployee.full_name}
                  onChange={(e) => setNewEmployee({...newEmployee, full_name: e.target.value})}
                  placeholder="Иван Иванов"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={newEmployee.email}
                  onChange={(e) => setNewEmployee({...newEmployee, email: e.target.value})}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Пароль
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    required
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={newEmployee.password}
                    onChange={(e) => setNewEmployee({...newEmployee, password: e.target.value})}
                    placeholder="Введите пароль"
                  />
                  <button
                    type="button"
                    onClick={() => generatePassword(false)}
                    className="px-3 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50"
                  >
                    Генерировать
                  </button>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Отменить
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                >
                  Добавить менеджера
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Manager Modal */}
      {showEditModal && canManageEmployees && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Редактировать менеджера</h3>
              <button
                onClick={() => setShowEditModal(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleEditEmployee} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Полное имя
                </label>
                <input
                  type="text"
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={editEmployee.full_name}
                  onChange={(e) => setEditEmployee({...editEmployee, full_name: e.target.value})}
                  placeholder="Иван Иванов"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={editEmployee.email}
                  onChange={(e) => setEditEmployee({...editEmployee, email: e.target.value})}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Новый пароль (оставьте пустым, чтобы не менять)
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={editEmployee.password}
                    onChange={(e) => setEditEmployee({...editEmployee, password: e.target.value})}
                    placeholder="Новый пароль"
                  />
                  <button
                    type="button"
                    onClick={() => generatePassword(true)}
                    className="px-3 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50"
                  >
                    Генерировать
                  </button>
                </div>
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editEmployee.is_active}
                    onChange={(e) => setEditEmployee({...editEmployee, is_active: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">Активен</span>
                </label>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowEditModal(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Отменить
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                >
                  Сохранить изменения
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Show Credentials Modal */}
      {showCredentials && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Данные для входа</h3>
              <button
                onClick={() => setShowCredentials(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {(() => {
              const employee = employees.find(emp => emp.id === showCredentials);
              return employee ? (
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h4 className="font-medium text-blue-900 mb-2">{employee.full_name}</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-blue-800">Логин (Email):</span>
                        <div className="mt-1 p-2 bg-white border rounded text-gray-900 font-mono">
                          {employee.email}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">Пароль:</span>
                        <div className="mt-1 p-2 bg-white border rounded text-gray-900 font-mono">
                          {employee.password_plain || 'password'}
                        </div>
                      </div>
                      <div className="text-xs text-blue-600 mt-3">
                        💡 Менеджер должен использовать эти данные для входа в систему
                      </div>
                    </div>
                  </div>
                </div>
              ) : null;
            })()}

            <div className="flex justify-end pt-4">
              <button
                onClick={() => setShowCredentials(null)}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
              >
                Закрыть
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 