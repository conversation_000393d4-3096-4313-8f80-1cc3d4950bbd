import React, { useState, useEffect, useRef } from 'react';
import { VisaApplicationWithStatus } from '../../types/admin';
import { useToast } from '../../hooks/useToast';
import { getAllStepsForCountry, getStepLabel, WorkflowStep } from '../../config/workflows';
import { useTranslation } from '../../utils/localization';
import { formatPhoneInput, formatPhoneForDisplay, normalizePhoneNumber, isValidKazakhstanPhone } from '../../utils/validations';
import { UserRole, ClientStatusType, PaymentStatus } from '../../types/workflow';
import InvitationUpload from './InvitationUpload';

// Safe date formatting function
const createFormatDate = (t: (key: string) => string) => 
  (dateString: string | null | undefined): string => {
    if (!dateString) {
      return t('clients.modal.dateNotSpecified');
    }
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return t('clients.modal.invalidDate');
      }
      
      return date.toLocaleString('ru-RU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return t('clients.modal.dateError');
    }
  };

interface ClientModalProps {
  client: VisaApplicationWithStatus | null;
  isNew: boolean;
  similarCases: VisaApplicationWithStatus[];
  onClose: () => void;
  onSave: () => void;
  userRole?: string; // Add user role to determine permissions
}

interface ClientFormData {
  // Personal Information
  name: string;
  surname: string;
  age: number;
  email: string;
  phone: string;
  profession: string;
  income: number;
  
  // Personal Details
  maritalStatus: string;
  
  // Application Status
  step_status: number;
  visa_status: string;
  service_package_id: string;
  consular_fee_paid: boolean;
  requires_fixes: boolean;
  whatsapp_redirected: boolean;
  
  // Agent Information
  agent_id: string;
}

const maritalStatuses = [
  'single', 'married', 'divorced', 'widowed'
];

const createMaritalStatusLabels = (t: (key: string) => string): Record<string, string> => ({
  'single': t('clients.filters.single'),
  'married': t('clients.filters.married'),
  'divorced': t('clients.filters.divorced'),
  'widowed': t('clients.filters.widowed')
});

const createVisaStatuses = (t: (key: string) => string) => [
  t('clients.modal.visaStatuses.pending'),
  t('clients.modal.visaStatuses.submitted'),
  t('clients.modal.visaStatuses.approved'),
  t('clients.modal.visaStatuses.rejected')
];

const createPaymentStatuses = (t: (key: string) => string) => [
  t('clients.modal.paymentStatuses.unpaid'),
  t('clients.modal.paymentStatuses.paid'),
  t('clients.modal.paymentStatuses.partial')
];

const createStepStatuses = (t: (key: string) => string) => [
  { value: 1, label: t('clients.modal.stepStatuses.1') },
  { value: 2, label: t('clients.modal.stepStatuses.2') },
  { value: 3, label: t('clients.modal.stepStatuses.3') },
  { value: 4, label: t('clients.modal.stepStatuses.4') },
  { value: 5, label: t('clients.modal.stepStatuses.5') },
  { value: 6, label: t('clients.modal.stepStatuses.6') },
  { value: 7, label: t('clients.modal.stepStatuses.7') },
  { value: 8, label: t('clients.modal.stepStatuses.8') },
  { value: 9, label: t('clients.modal.stepStatuses.9') },
  { value: 10, label: t('clients.modal.stepStatuses.10') }
];

export default function ClientModal({ client, isNew, similarCases, onClose, onSave, userRole }: ClientModalProps) {
  const { success, error } = useToast();
  const { t } = useTranslation();
  
  // Initialize helper functions with translations
  const formatDate = createFormatDate(t);
  const maritalStatusLabels = createMaritalStatusLabels(t);
  const visaStatuses = createVisaStatuses(t);
  const paymentStatuses = createPaymentStatuses(t);
  const stepStatuses = createStepStatuses(t);
  const [formData, setFormData] = useState<ClientFormData>({
    name: '',
    surname: '',
    age: 0,
    email: '',
    phone: '',
    profession: '',
    income: 0,
    maritalStatus: '',
    step_status: 1,
    visa_status: 'в_процессе',
    service_package_id: '',
    consular_fee_paid: false,
    requires_fixes: false,
    whatsapp_redirected: false,
    agent_id: ''
  });

  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [servicePackages, setServicePackages] = useState<any[]>([]);
  const [invitationFileUrl, setInvitationFileUrl] = useState<string | undefined>(client?.invitation_file_url);

  // Fetch service packages on component mount
  useEffect(() => {
    const fetchServicePackages = async () => {
      try {
        const response = await fetch('/api/admin/price-list');
        if (response.ok) {
          const data = await response.json();
          setServicePackages(data.packages || []);
        }
      } catch (err) {
        console.error('Error fetching service packages:', err);
      }
    };
    
    fetchServicePackages();
  }, []);

  useEffect(() => {
    if (client && !isNew) {
      const clientData = client.form_data as Record<string, any>;
      
      setFormData({
        name: clientData?.name || '',
        surname: clientData?.surname || '',
        age: clientData?.age || 0,
        email: clientData?.email || '',
        phone: client.phone_number ? formatPhoneForDisplay(client.phone_number) : '',
        profession: clientData?.profession || '',
        income: clientData?.income || 0,
        maritalStatus: clientData?.maritalStatus || '',
        step_status: client.step_status ?? 1,
        visa_status: client.visa_status || 'в_процессе',
        service_package_id: client.service_package_id || '',
        consular_fee_paid: client.consular_fee_paid || false,
        requires_fixes: client.requires_fixes || false,
        whatsapp_redirected: client.whatsapp_redirected || false,
        agent_id: client.agent_id || ''
      });
      
      setInvitationFileUrl(client.invitation_file_url);
    }
  }, [client, isNew]);

  // Check if user can edit step status 
  const canEditStepStatus = () => {
    // Super admin CANNOT edit step status - only visa companies can
    if (userRole === UserRole.SUPER_ADMIN) return false;
    
    // Visa admins can only edit if client is waiting for invitation OR invitation has been uploaded
    if (userRole === UserRole.VISA_ADMIN) {
      if (!client) return false;
      
      const clientData = client.form_data as Record<string, any>;
      const clientStatus = clientData?.client_status || '';
      
      // Can edit if client is waiting for invitation OR invitation file exists
      return (
        clientStatus === ClientStatusType.WAITING_INVITATION || 
        !!invitationFileUrl
      );
    }
    
    // Managers can never edit step status
    if (userRole === UserRole.MANAGER) {
      return false;
    }
    
    // Default case (should not happen, but safe fallback)
    return false;
  };

  // Check if user can edit service package
  const canEditServicePackage = () => {
    // Super admin CANNOT edit service package - only visa companies can
    if (userRole === UserRole.SUPER_ADMIN) return false;
    
    // Visa admins and managers can edit service package
    if (userRole === UserRole.VISA_ADMIN || userRole === UserRole.MANAGER) {
      return true;
    }
    
    // Default case (should not happen, but safe fallback)
    return false;
  };

  // Check if invitation upload should be shown
  const shouldShowInvitationUpload = () => {
    // Show for managers and admins when step is 6 (waiting for invitation) or invitation already exists
    return (userRole === 'manager' || userRole === 'admin') && (formData.step_status === 6 || !!invitationFileUrl);
  };

  // Handle ESC key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [onClose]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = t('clients.modal.required');
    }
    if (!formData.surname.trim()) {
      errors.surname = t('clients.modal.required');
    }
    if (!formData.phone.trim()) {
      errors.phone = t('clients.modal.required');
    } else if (!isValidKazakhstanPhone(formData.phone)) {
      errors.phone = t('clients.modal.phoneFormatError');
    }
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Введите корректный email';
    }
    if (formData.income < 0) {
      errors.income = 'Доход должен быть положительным числом';
    }
    if (formData.age < 16 || formData.age > 100) {
      errors.age = 'Возраст должен быть от 16 до 100 лет';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof ClientFormData, value: any) => {
    // Special handling for phone number formatting
    if (field === 'phone') {
      const formattedPhone = formatPhoneInput(value);
      setFormData(prev => ({ ...prev, [field]: formattedPhone }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleInvitationUploadSuccess = (fileUrl: string) => {
    setInvitationFileUrl(fileUrl);
    // Auto-advance step status if currently waiting for invitation
    if (formData.step_status === 6) {
      setFormData(prev => ({ ...prev, step_status: 7 })); // Move to "Координация кейса"
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      error('Ошибка валидации', 'Пожалуйста, исправьте ошибки в форме');
      return;
    }

    setLoading(true);

    if (!client) {
      console.error('No client data available');
      return;
    }

    try {
      const url = isNew ? '/api/admin/applications' : `/api/admin/applications/${client?.id}`;
      const method = isNew ? 'POST' : 'PUT';

      // Prepare the data structure to match the database
      // Get existing form_data and update it with new values
      const existingFormData = (client.form_data as Record<string, any>) || {};
      
      const updatedFormData = {
        ...existingFormData,
        name: formData.name,
        surname: formData.surname,
        age: formData.age,
        email: formData.email,
        profession: formData.profession,
        income: formData.income,
        maritalStatus: formData.maritalStatus,
      };

      const updateData = {
        form_data: updatedFormData,
        phone_number: normalizePhoneNumber(formData.phone),
        step_status: formData.step_status,
        visa_status: formData.visa_status,
        service_package_id: formData.service_package_id,
        consular_fee_paid: formData.consular_fee_paid,
        requires_fixes: formData.requires_fixes,
        whatsapp_redirected: formData.whatsapp_redirected,
        agent_id: formData.agent_id,
        invitation_file_url: invitationFileUrl
      };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (response.ok) {
        success(
          isNew ? 'Клиент успешно создан' : 'Клиент успешно обновлен',
          isNew ? 'Новый клиент добавлен в систему' : 'Изменения сохранены'
        );
        onSave();
      } else {
        error('Ошибка при сохранении', data.error || 'Попробуйте еще раз');
      }
    } catch (err) {
      error('Ошибка сети', 'Проверьте подключение к интернету');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-25 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-0 border w-11/12 max-w-6xl shadow-2xl rounded-xl bg-white mb-8">
        {/* Fixed Header with Save Button */}
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200 rounded-t-xl px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                {isNew ? 'Добавить новую заявку' : 'Редактировать заявку'}
              </h3>
              <p className="mt-1 text-sm text-gray-600">
                {isNew ? 'Заполните информацию о новой заявке' : 'Обновите информацию о заявке'}
              </p>
              {client && !isNew && (
                <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                  <span>ID: {client.id}</span>
                  <span>{t('clients.createdDate')}: {formatDate(client.created_at)}</span>
                  {client.last_updated && (
                    <span>{t('clients.updatedDate')}: {formatDate(client.last_updated)}</span>
                  )}
                </div>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {/* Save Button - Always Visible */}
              <button
                onClick={handleSubmit}
                disabled={loading}
                className={`inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white ${
                  loading 
                    ? 'bg-blue-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                } shadow-sm transition-colors duration-200`}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('clients.modal.loading')}
                  </>
                ) : (
                  <>
                    <svg className="-ml-1 mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {isNew ? t('clients.addClient') : t('clients.modal.save')}
                  </>
                )}
              </button>
              
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 p-6">
          {/* Main Form */}
          <div className="lg:col-span-3">
            <form className="space-y-8">
              {/* Personal Information */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
                <h4 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <svg className="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                  {t('clients.modal.personalInformation')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.firstName')} *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                        validationErrors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder={t('clients.firstName')}
                    />
                    {validationErrors.name && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.lastName')} *
                    </label>
                    <input
                      type="text"
                      value={formData.surname}
                      onChange={(e) => handleInputChange('surname', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                        validationErrors.surname ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder={t('clients.lastName')}
                    />
                    {validationErrors.surname && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.surname}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.age')} *
                    </label>
                    <input
                      type="number"
                      min="16"
                      max="100"
                      value={formData.age || ''}
                      onChange={(e) => handleInputChange('age', e.target.value === '' ? 0 : parseInt(e.target.value))}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                        validationErrors.age ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder={t('clients.age')}
                    />
                    {validationErrors.age && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.age}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.maritalStatus')}
                    </label>
                    <select
                      value={formData.maritalStatus}
                      onChange={(e) => handleInputChange('maritalStatus', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    >
                      <option value="">{t('clients.maritalStatus')}</option>
                      {maritalStatuses.map((status) => (
                        <option key={status} value={status}>
                          {maritalStatusLabels[status] || status}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.email')}
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                        validationErrors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder="<EMAIL>"
                    />
                    {validationErrors.email && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.email}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.phone')} *
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                        validationErrors.phone ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder="****** 123 45 67"
                    />
                    {validationErrors.phone && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.phone}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.profession')}
                    </label>
                    <input
                      type="text"
                      value={formData.profession}
                      onChange={(e) => handleInputChange('profession', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                      placeholder={t('clients.profession')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.income')}
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={formData.income}
                      onChange={(e) => handleInputChange('income', parseInt(e.target.value) || 0)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
                        validationErrors.income ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      placeholder="500000"
                    />
                    {validationErrors.income && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.income}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Application Status */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
                <h4 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <svg className="w-5 h-5 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                  {t('clients.modal.applicationStatus')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.currentStep')}
                      {!canEditStepStatus() && (
                        <span className="ml-2 text-xs text-red-600 bg-red-100 px-2 py-1 rounded flex items-center">
                          🔒 {t('clients.modal.locked')}
                        </span>
                      )}
                    </label>
                    <div className="relative">
                      <select
                        value={formData.step_status}
                        onChange={(e) => handleInputChange('step_status', parseInt(e.target.value))}
                        disabled={!canEditStepStatus()}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors ${
                          !canEditStepStatus() 
                            ? 'border-gray-200 bg-gray-100 text-gray-500 cursor-not-allowed' 
                            : 'border-gray-300'
                        }`}
                      >
                        {/* Get workflow steps based on client's visa country */}
                        {(() => {
                          const clientData = client?.form_data as Record<string, any>;
                          // Check both camelCase and snake_case formats for visa country
                          let visaCountry = clientData?.visaCountry || clientData?.visa_country || 'US';
                          
                          // Normalize country codes to match workflow configuration
                          const countryMapping: { [key: string]: string } = {
                            'США': 'US',
                            'United States': 'US',
                            'USA': 'US',
                            'US': 'US',
                            'Великобритания': 'UK',
                            'United Kingdom': 'UK',
                            'UK': 'UK',
                            'Шенгенские страны': 'EU',
                            'Europe': 'EU',
                            'EU': 'EU',
                            'Китай': 'CN',
                            'China': 'CN',
                            'CN': 'CN'
                          };
                          
                          visaCountry = countryMapping[visaCountry] || visaCountry;
                          const steps = getAllStepsForCountry(visaCountry);
                          
                          // If no steps found, show default message
                          if (steps.length === 0) {
                            return (
                              <option value="">Этапы не найдены для страны: {visaCountry}</option>
                            );
                          }
                          
                          return steps.map((step: WorkflowStep) => (
                            <option key={step.id} value={step.id}>
                              {step.id}. {step.label}
                            </option>
                          ));
                        })()}
                      </select>
                      {!canEditStepStatus() && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    {!canEditStepStatus() && (
                      <p className="mt-1 text-xs text-red-500">
                        {userRole === UserRole.VISA_ADMIN 
                          ? t('clients.modal.stepEditRestrictedInvitation')
                          : t('clients.modal.stepEditRestricted')
                        }
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.visaStatus')}
                    </label>
                    <select
                      value={formData.visa_status}
                      onChange={(e) => handleInputChange('visa_status', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                    >
                      {visaStatuses.map((status) => (
                        <option key={status} value={status}>
                          {status}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.servicePackage')}
                    </label>
                    <div className="relative">
                      <select
                        value={formData.service_package_id}
                        onChange={(e) => handleInputChange('service_package_id', e.target.value)}
                        disabled={!canEditServicePackage()}
                        className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors ${
                          !canEditServicePackage() ? 'bg-gray-100 cursor-not-allowed opacity-60' : ''
                        }`}
                      >
                        <option value="">{t('clients.servicePackage')}</option>
                        {servicePackages.map((pkg) => (
                          <option key={pkg.id} value={pkg.id}>
                            {pkg.country} — {pkg.title} ({Number(pkg.price).toLocaleString()} тенге)
                          </option>
                        ))}
                      </select>
                      {!canEditServicePackage() && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    {!canEditServicePackage() && (
                      <p className="mt-1 text-xs text-red-500">
                        {t('clients.modal.servicePackageEditRestricted')}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('clients.modal.agentInformation')}
                    </label>
                    <input
                      type="text"
                      value={formData.agent_id}
                      onChange={(e) => handleInputChange('agent_id', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                      placeholder="agent_001"
                    />
                  </div>
                </div>

                {/* Status Checkboxes */}
                <div className="mt-6 space-y-4">
                  <div className="flex items-center">
                    <input
                      id="consular_fee_paid"
                      type="checkbox"
                      checked={formData.consular_fee_paid}
                      onChange={(e) => handleInputChange('consular_fee_paid', e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label htmlFor="consular_fee_paid" className="ml-2 block text-sm text-gray-900">
                      {t('clients.paidConsularFee')}
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="requires_fixes"
                      type="checkbox"
                      checked={formData.requires_fixes}
                      onChange={(e) => handleInputChange('requires_fixes', e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label htmlFor="requires_fixes" className="ml-2 block text-sm text-gray-900">
                      {t('clients.requiresFixes')}
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="whatsapp_redirected"
                      type="checkbox"
                      checked={formData.whatsapp_redirected}
                      onChange={(e) => handleInputChange('whatsapp_redirected', e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label htmlFor="whatsapp_redirected" className="ml-2 block text-sm text-gray-900">
                      WhatsApp
                    </label>
                  </div>
                </div>
              </div>

              {/* Invitation Upload Section */}
              {shouldShowInvitationUpload() && client && (
                <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-6 rounded-xl border border-amber-100">
                  <h4 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <svg className="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6zm2 2v4h8V8H6z" clipRule="evenodd" />
                    </svg>
                    {t('clients.modal.invitationUpload')}
                  </h4>
                  <InvitationUpload
                    applicationId={client.id}
                    currentFileUrl={invitationFileUrl}
                    onUploadSuccess={handleInvitationUploadSuccess}
                    disabled={loading}
                  />
                </div>
              )}
            </form>
          </div>

          {/* Sidebar with Similar Cases */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 p-6 rounded-xl border border-gray-200 sticky top-24">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                Похожие кейсы
              </h4>
              
              {similarCases.length > 0 ? (
                <div className="space-y-3">
                  {similarCases.slice(0, 5).map((similarCase) => {
                    const caseData = similarCase.form_data as Record<string, any>;
                    return (
                      <div key={similarCase.id} className="p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors">
                        <div className="text-sm font-medium text-gray-900">
                          {caseData?.name} {caseData?.surname}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {caseData?.profession} • {caseData?.visaCountry}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          Доход: {caseData?.income?.toLocaleString()} ₸
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-sm text-gray-500">Похожих кейсов не найдено</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 