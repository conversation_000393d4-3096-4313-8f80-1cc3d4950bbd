import React, { useState } from 'react';
import { useDateFilter } from '../../contexts/DateFilterContext';

const GlobalDateFilter: React.FC = () => {
  const { dateRange, setDateRange, resetDateRange, isDefaultRange, disableDateFilter, isDateFilterDisabled } = useDateFilter();
  const [showPresets, setShowPresets] = useState(false);

  // Validate and format date range
  const validateDateRange = (newRange: { from: string; to: string }) => {
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    
    const fromDate = new Date(newRange.from);
    const toDate = new Date(newRange.to);
    
    // Prevent future dates
    if (fromDate > today) {
      fromDate.setTime(today.getTime());
    }
    if (toDate > today) {
      toDate.setTime(today.getTime());
    }
    
    // Ensure 'from' is not after 'to'
    if (fromDate > toDate) {
      fromDate.setTime(toDate.getTime());
    }
    
    return {
      from: fromDate.toISOString().split('T')[0],
      to: toDate.toISOString().split('T')[0]
    };
  };

  const presets = [
    {
      label: 'Последние 30 дней',
      getValue: () => {
        const to = new Date();
        const from = new Date();
        from.setDate(to.getDate() - 30);
        return validateDateRange({
          from: from.toISOString().split('T')[0],
          to: to.toISOString().split('T')[0]
        });
      }
    },
    {
      label: 'Последние 3 месяца',
      getValue: () => {
        const to = new Date();
        const from = new Date();
        from.setMonth(to.getMonth() - 3);
        return validateDateRange({
          from: from.toISOString().split('T')[0],
          to: to.toISOString().split('T')[0]
        });
      }
    },
    {
      label: 'Последний год',
      getValue: () => {
        const to = new Date();
        const from = new Date();
        from.setFullYear(to.getFullYear() - 1);
        return validateDateRange({
          from: from.toISOString().split('T')[0],
          to: to.toISOString().split('T')[0]
        });
      }
    },
    {
      label: 'Текущий год',
      getValue: () => {
        const now = new Date();
        const from = new Date(now.getFullYear(), 0, 1);
        return validateDateRange({
          from: from.toISOString().split('T')[0],
          to: now.toISOString().split('T')[0]
        });
      }
    }
  ];

  const handlePresetSelect = (preset: typeof presets[0]) => {
    setDateRange(preset.getValue());
    setShowPresets(false);
  };

  const handleDateChange = (field: 'from' | 'to', value: string) => {
    if (!dateRange) {
      // If no date range is set, create a new one
      const today = new Date().toISOString().split('T')[0];
      const newRange = validateDateRange({
        from: field === 'from' ? value : today,
        to: field === 'to' ? value : today,
      });
      setDateRange(newRange);
    } else {
      const newRange = validateDateRange({
        ...dateRange,
        [field]: value
      });
      setDateRange(newRange);
    }
  };

  return (
    <div className="flex items-center space-x-2 relative">
      <span className="text-sm text-gray-600">Период:</span>
      
      {/* Date inputs or "All applications" display */}
      {isDateFilterDisabled ? (
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            🌍 Все заявки
          </span>
        </div>
      ) : (
        <div className="flex items-center space-x-1">
          <input
            type="date"
            value={dateRange?.from || ''}
            onChange={(e) => handleDateChange('from', e.target.value)}
            max={new Date().toISOString().split('T')[0]}
            className="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <span className="text-gray-500">—</span>
          <input
            type="date"
            value={dateRange?.to || ''}
            onChange={(e) => handleDateChange('to', e.target.value)}
            max={new Date().toISOString().split('T')[0]}
            className="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      )}

      {/* Presets dropdown */}
      <div className="relative">
        <button
          onClick={() => setShowPresets(!showPresets)}
          className="px-2 py-1 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
        >
          📅
        </button>
        
        {showPresets && (
          <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px]">
            <div className="py-1">
              {presets.map((preset, index) => (
                <button
                  key={index}
                  onClick={() => handlePresetSelect(preset)}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  {preset.label}
                </button>
              ))}
              <hr className="my-1" />
              <button
                onClick={() => {
                  disableDateFilter();
                  setShowPresets(false);
                }}
                className="block w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 transition-colors font-medium"
              >
                🌍 Показать все заявки
              </button>
              <button
                onClick={() => {
                  resetDateRange();
                  setShowPresets(false);
                }}
                className="block w-full text-left px-4 py-2 text-sm text-gray-500 hover:bg-gray-100 transition-colors"
              >
                Сбросить фильтр
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {showPresets && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowPresets(false)}
        />
      )}
    </div>
  );
};

export default GlobalDateFilter; 