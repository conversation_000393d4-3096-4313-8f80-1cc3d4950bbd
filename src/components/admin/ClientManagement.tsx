import React, { useState, useEffect, useRef, useMemo } from 'react';
import { VisaApplicationWithStatus, SearchFilters } from '../../types/admin';
import ClientModal from './ClientModal';
import AddClientModal from './AddClientModal';
import { useToast } from '../../hooks/useToast';
import { getAllStepsForCountry, getStepLabel, WorkflowStep } from '../../config/workflows';
import * as XLSX from 'xlsx';
import { useTranslation } from '../../utils/localization';
import HelpModal from './HelpModal';
import { useDateFilter } from '../../contexts/DateFilterContext';
import { 
  normalizeCountryName, 
  CLIENT_PROGRESS_STATUS,
  SERVICE_PAYMENT_STATUS,
  VISA_STATUS
} from '../../types/client-status';

// View mode constants
const VIEW_MODE = {
  BOARD: 'board',
  TABLE: 'table'
} as const;

type ViewMode = typeof VIEW_MODE[keyof typeof VIEW_MODE];

// Safe date formatting function
// Move formatDate inside component to access translation function
const createFormatDate = (t: (key: string) => string) => 
  (dateString: string | null | undefined): string => {
    if (!dateString) {
      return t('common.dateNotSpecified');
    }
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return t('common.invalidDate');
      }
      
      return date.toLocaleDateString('ru-RU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return t('common.dateError');
    }
  };

// Calculate age from date of birth
const calculateAge = (dateOfBirth: string | null | undefined): number | null => {
  if (!dateOfBirth) return null;
  
  try {
    const dob = new Date(dateOfBirth);
    if (isNaN(dob.getTime())) return null;
    
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    
    return age;
  } catch (error) {
    console.error('Error calculating age:', error);
    return null;
  }
};

interface ClientManagementProps {
  applications: VisaApplicationWithStatus[];
  onUpdateStatus: (applicationId: string, status: string) => void;
  searchFilters: SearchFilters;
  onSearchFiltersChange: (filters: SearchFilters) => void;
  isLoading: boolean;
  isSuperAdmin: boolean;
  companyId?: string;
  onRefresh?: () => void;
  focusClientId?: string | null;
  selectedCountryFromUrl?: string;
  userRole?: string;
  totalApplications?: number;
}

interface Country {
  code: string;
  name: string;
  flag: string;
  active: boolean;
  count?: number;
}

// baseCountries moved inside component to access translations

// Function to normalize country names (using centralized function)
const normalizeCountry = normalizeCountryName;

// Removed old workflowStatuses - now using centralized types from workflow.ts

// Skeleton loading components with beautiful shimmer effects
const SkeletonTrelloCard = () => (
  <div className="bg-white p-4 rounded-lg shadow-sm relative overflow-hidden">
    <div className="animate-pulse">
      <div className="flex items-center justify-between mb-2">
        <div className="h-4 bg-gray-200 rounded w-32"></div>
        <div className="w-4 h-4 bg-gray-200 rounded"></div>
      </div>
      <div className="space-y-2 mb-3">
        <div className="h-3 bg-gray-200 rounded w-24"></div>
        <div className="h-3 bg-gray-200 rounded w-36"></div>
      </div>
      <div className="h-6 bg-gray-200 rounded-full w-20"></div>
    </div>
    {/* Shimmer overlay */}
    <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>
  </div>
);

const SkeletonTrelloColumn = () => (
  <div className="bg-gray-100 rounded-lg p-4 min-h-[500px] relative overflow-hidden">
    <div className="animate-pulse">
      <div className="flex items-center justify-between mb-4">
        <div className="h-5 bg-gray-200 rounded w-24"></div>
        <div className="bg-white text-gray-600 text-sm px-2 py-1 rounded-full w-8 h-6 bg-gray-200"></div>
      </div>
      <div className="space-y-3">
        <SkeletonTrelloCard />
        <SkeletonTrelloCard />
        <SkeletonTrelloCard />
      </div>
    </div>
    {/* Column shimmer overlay */}
    <div className="absolute inset-0 -translate-x-full animate-[shimmer_3s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style={{animationDelay: '0.5s'}}></div>
  </div>
);

const SkeletonTableRow = () => (
  <tr className="animate-pulse relative">
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="h-4 bg-gray-200 rounded w-20"></div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="h-4 bg-gray-200 rounded w-24"></div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="h-4 bg-gray-200 rounded w-28"></div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="h-4 bg-gray-200 rounded w-32"></div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="h-6 bg-gray-200 rounded-full w-16"></div>
    </td>
    <td className="px-6 py-4 whitespace-nowrap">
      <div className="h-4 bg-gray-200 rounded w-12"></div>
    </td>
  </tr>
);

export default function ClientManagement({
  applications,
  onUpdateStatus: _onUpdateStatus,
  searchFilters: _searchFilters,
  onSearchFiltersChange: _onSearchFiltersChange,
  isLoading: _isLoading,
  isSuperAdmin: _isSuperAdmin,
  companyId,
  onRefresh,
  focusClientId,
  selectedCountryFromUrl,
  userRole,
  totalApplications,
}: ClientManagementProps) {
  const { t } = useTranslation();
  const { success, error } = useToast();
  const { dateRange } = useDateFilter();
  
  // Create formatDate function with translation support
  const formatDate = createFormatDate(t);
  
  // Define countries with translations
  const baseCountries: Omit<Country, 'active'>[] = [
    { code: 'US', name: t('countries.US'), flag: '🇺🇸' },
    { code: 'UK', name: t('countries.UK'), flag: '🇬🇧' },
    { code: 'EU', name: t('countries.EU'), flag: '🇪🇺' },
    { code: 'CN', name: t('countries.CN'), flag: '🇨🇳' },
  ];
  
  const [selectedCountry, setSelectedCountry] = useState<string | null>(selectedCountryFromUrl || null);
  const [showCountrySelection, setShowCountrySelection] = useState(!selectedCountryFromUrl); // Show country selection if no country from URL
  const [selectedClient, setSelectedClient] = useState<VisaApplicationWithStatus | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAddClientModalOpen, setIsAddClientModalOpen] = useState(false);
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>(VIEW_MODE.BOARD); // Default to board (trello) view
  const [showFilters, setShowFilters] = useState(false);
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [showLoadMore, setShowLoadMore] = useState(false);

  const focusHandledRef = useRef<string | null>(null); // Track which client focus was already handled

  // Calculate countries with active status and counts based on applications
  const countries: Country[] = baseCountries.map(country => {
    const countryApplications = applications.filter(app => {
      const formData = app.form_data as Record<string, any>;
      // Check visaCountry, country, and visaDestination fields, normalize the values
      let visaCountry = formData.visaCountry || formData.country || '';
      const visaDestination = formData.visaDestination || '';
      
      // For paid package clients, also check service package country if visaCountry is not set
      if (!visaCountry && app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE && app.service_package) {
        visaCountry = app.service_package.country || '';
      }
      
      const normalizedCountry = normalizeCountry(visaCountry);
      const normalizedDestination = normalizeCountry(visaDestination);
      
      return normalizedCountry === country.code || normalizedDestination === country.code;
    });
    return { 
      ...country, 
      active: countryApplications.length > 0,
      count: countryApplications.length
    };
  });
  
  // Filter state
  const [filters, setFilters] = useState({
    name: '',
    activity: '',
    minIncome: '',
    maxIncome: '',
    visaHistory: '',
    hasProperty: '',
    maritalStatus: '',
    hasSponsor: '',
    dateFrom: '', // Temporarily disable date filtering
    dateTo: '' // Temporarily disable date filtering
  });

  // Filter applications based on search filters and selected country
  const filteredApplications = useMemo(() => {
    let filtered = [...applications];

    // Filter by country if selected, but skip if backend has already filtered
    // (to avoid double filtering when URL contains country parameter)
    const countryFromUrl = selectedCountryFromUrl;
    console.log('Country filtering logic:', {
      selectedCountry,
      countryFromUrl,
      willApplyFrontendFiltering: selectedCountry && selectedCountry !== countryFromUrl,
      applicationsBeforeFiltering: filtered.length
    });
    
    if (selectedCountry && selectedCountry !== countryFromUrl) {
      // Only apply frontend filtering if different from URL country (backend hasn't filtered)
      filtered = filtered.filter(app => {
        const formData = app.form_data as Record<string, any> || {};
        let visaCountry = formData.visaCountry || formData.country || '';
        
        // For paid package clients, also check service package country if visaCountry is not set
        if (!visaCountry && app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE && app.service_package) {
          visaCountry = app.service_package.country || '';
        }
        
        // Apply country filter to ALL clients, including manager-added ones
        return normalizeCountry(visaCountry) === selectedCountry;
      });
    }

    // Apply search filters
    if (filters.name) {
      const searchTerm = filters.name.toLowerCase();
      filtered = filtered.filter(app => {
        const formData = app.form_data as Record<string, any> || {};
        const name = String(formData.name || '').toLowerCase();
        const surname = String(formData.surname || '').toLowerCase();
        return name.includes(searchTerm) || surname.includes(searchTerm);
      });
    }

    if (filters.activity) {
      const searchTerm = filters.activity.toLowerCase();
      filtered = filtered.filter(app => {
        // Skip filter for manager-added clients (they don't have profession field yet)
        if (app.added_by_manager) return true;
        
        const formData = app.form_data as Record<string, any> || {};
        const profession = String(formData.occupation || formData.profession || '').toLowerCase();
        return profession.includes(searchTerm);
      });
    }

    if (filters.minIncome) {
      const minIncome = parseFloat(filters.minIncome);
      filtered = filtered.filter(app => {
        // Skip filter for manager-added clients (they don't have income field yet)
        if (app.added_by_manager) return true;
        
        const formData = app.form_data as Record<string, any> || {};
        const income = parseFloat(String(formData.income || formData.salary || '0'));
        return !isNaN(income) && income >= minIncome;
      });
    }

    if (filters.maxIncome) {
      const maxIncome = parseFloat(filters.maxIncome);
      filtered = filtered.filter(app => {
        // Skip filter for manager-added clients (they don't have income field yet)
        if (app.added_by_manager) return true;
        
        const formData = app.form_data as Record<string, any> || {};
        const income = parseFloat(String(formData.income || formData.salary || '0'));
        return !isNaN(income) && income <= maxIncome;
      });
    }

    if (filters.visaHistory) {
      filtered = filtered.filter(app => {
        // Skip filter for manager-added clients (they don't have visaHistory field yet)
        if (app.added_by_manager) return true;
        
        const formData = app.form_data as Record<string, any> || {};
        return formData.visaHistory === filters.visaHistory;
      });
    }

    if (filters.hasProperty) {
      filtered = filtered.filter(app => {
        // Skip filter for manager-added clients (they don't have hasProperty field yet)
        if (app.added_by_manager) return true;
        
        const formData = app.form_data as Record<string, any> || {};
        return formData.hasProperty === (filters.hasProperty === 'true');
      });
    }

    if (filters.maritalStatus) {
      filtered = filtered.filter(app => {
        // Skip filter for manager-added clients (they don't have maritalStatus field yet)
        if (app.added_by_manager) return true;
        
        const formData = app.form_data as Record<string, any> || {};
        return formData.maritalStatus === filters.maritalStatus;
      });
    }

    if (filters.hasSponsor) {
      filtered = filtered.filter(app => {
        // Skip filter for manager-added clients (they don't have hasSponsor field yet)
        if (app.added_by_manager) return true;
        
        const formData = app.form_data as Record<string, any> || {};
        return formData.hasSponsor === (filters.hasSponsor === 'true');
      });
    }

    // Global date filter is now handled at the API level in dashboard.tsx
    // No need for client-side date filtering

    return filtered;
  }, [applications, selectedCountry, filters]);

  // Update filtered applications when date range changes
  useEffect(() => {
    const fetchData = async () => {
      if (onRefresh) {
        await onRefresh();
      }
    };
    fetchData();
  }, [dateRange.from, dateRange.to, companyId, onRefresh]);

  // Debug logging for totalApplications changes
  useEffect(() => {
    console.log('=== CLIENT MANAGEMENT totalApplications CHANGE ===');
    console.log('totalApplications prop:', totalApplications);
    console.log('applications.length:', applications.length);
    console.log('filteredApplications.length:', filteredApplications.length);
    
    // Debug: Check step_status distribution
    const stepStatusDistribution = applications.reduce((acc, app) => {
      const step = app.step_status || 'undefined';
      acc[step] = (acc[step] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    console.log('Step status distribution (all applications):', stepStatusDistribution);
    
    // Debug: Check service_payment_status distribution
    const paymentStatusDistribution = applications.reduce((acc, app) => {
      const status = app.service_payment_status || 'undefined';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    console.log('Payment status distribution:', paymentStatusDistribution);
    
    // Debug: Check clients with paid package status specifically
    const paidPackageClients = applications.filter(app => app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE);
    const filteredPaidPackageClients = filteredApplications.filter(app => app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE);
    
    // Debug: Check added_by_manager clients specifically
    const managerAddedClients = applications.filter(app => app.added_by_manager === true);
    const filteredManagerAddedClients = filteredApplications.filter(app => app.added_by_manager === true);
    
    console.log(`Clients with client_progress_status='${CLIENT_PROGRESS_STATUS.PAID_PACKAGE}' (all):`, paidPackageClients.length);
    console.log(`Clients with client_progress_status='${CLIENT_PROGRESS_STATUS.PAID_PACKAGE}' (filtered):`, filteredPaidPackageClients.length);
    console.log(`🔍 Manager-added clients (all):`, managerAddedClients.length);
    console.log(`🔍 Manager-added clients (filtered):`, filteredManagerAddedClients.length);
    
    if (paidPackageClients.length > 0) {
      console.log('All paid package clients details:', paidPackageClients.map(client => ({
        id: client.id,
        step_status: client.step_status,
        client_progress_status: client.client_progress_status,
        service_payment_status: client.service_payment_status,
        name: `${(client.form_data as any)?.surname || ''} ${(client.form_data as any)?.name || ''}`.trim(),
        visaCountry: (client.form_data as any)?.visaCountry || 'not set',
        normalizedCountry: normalizeCountry((client.form_data as any)?.visaCountry || ''),
        selectedCountry: selectedCountry,
        filteredOut: !filteredApplications.some(filtered => filtered.id === client.id)
      })));
    }
    
    // Debug: Check filtered applications step distribution
    const filteredStepDistribution = filteredApplications.reduce((acc, app) => {
      const step = app.step_status || 'undefined';
      acc[step] = (acc[step] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    console.log('Step status distribution (filtered):', filteredStepDistribution);
    
    // Debug country filtering issue for paid package clients
    if (paidPackageClients.length > filteredPaidPackageClients.length) {
      console.log('❌ ISSUE FOUND: Some paid package clients are being filtered out!');
      const filteredOutPaidPackage = paidPackageClients.filter(client => 
        !filteredApplications.some(filtered => filtered.id === client.id)
      );
      console.log('Filtered out paid package clients:', filteredOutPaidPackage.map(client => ({
        id: client.id,
        visaCountry: (client.form_data as any)?.visaCountry,
        normalizedCountry: normalizeCountry((client.form_data as any)?.visaCountry || ''),
        selectedCountry: selectedCountry,
        reason: normalizeCountry((client.form_data as any)?.visaCountry || '') !== selectedCountry ? 'Country mismatch' : 'Other filter'
      })));
    } else if (filteredPaidPackageClients.length > 0) {
      console.log('✅ Paid package clients are visible in filtered applications');
    }
    
    console.log('===================================================');
  }, [totalApplications, applications.length, filteredApplications.length, applications, filteredApplications]);

  // Effect to handle focus on specific client from URL - ONE TIME ONLY
  useEffect(() => {
    if (focusClientId && filteredApplications.length > 0) {
      // Check if this focus has already been handled in this session
      const focusedClientsKey = 'visa_form_focused_clients';
      const focusedClients = JSON.parse(sessionStorage.getItem(focusedClientsKey) || '[]');
      
      if (!focusedClients.includes(focusClientId) && focusHandledRef.current !== focusClientId) {
        const targetClient = filteredApplications.find(app => app.id === focusClientId);
        if (targetClient) {
          // Mark this focus as handled to prevent re-execution
          focusHandledRef.current = focusClientId;
          focusedClients.push(focusClientId);
          sessionStorage.setItem(focusedClientsKey, JSON.stringify(focusedClients));
          
          setSelectedClient(targetClient);
          setIsModalOpen(true);
          
          // IMMEDIATELY clear focus from URL to prevent reopening
          if (typeof window !== 'undefined') {
            const url = new URL(window.location.href);
            url.searchParams.delete('focus');
            window.history.replaceState({}, '', url.toString());
          }
        }
      } else {
        // Focus has already been handled, just clear the URL
        if (typeof window !== 'undefined') {
          const url = new URL(window.location.href);
          url.searchParams.delete('focus');
          window.history.replaceState({}, '', url.toString());
        }
      }
    }
  }, [focusClientId, filteredApplications]);

  // Effect to clean up URL parameters on page load/refresh when not in specific views
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      let shouldUpdateUrl = false;
      
      // If not in a focused modal and not viewing a specific country, clean up URL parameters
      if (!isModalOpen && !selectedCountry) {
        if (url.searchParams.has('country')) {
          url.searchParams.delete('country');
          shouldUpdateUrl = true;
        }
        if (url.searchParams.has('focus')) {
          url.searchParams.delete('focus');
          shouldUpdateUrl = true;
        }
      }
      
      if (shouldUpdateUrl) {
        window.history.replaceState({}, '', url.toString());
      }
    }
  }, [isModalOpen, selectedCountry]);

  const handleCountrySelect = (countryCode: string) => {
    setSelectedCountry(countryCode);
    setShowCountrySelection(false);
  };

  const handleBackToCountries = () => {
    // Clear URL parameters without page reload
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.delete('focus');
      url.searchParams.delete('country');
      window.history.replaceState({}, '', url.toString());
    }
    
    // Show country selection within current tab
    setSelectedCountry(null);
    setShowCountrySelection(true);
  };

  const handleClientClick = (client: VisaApplicationWithStatus) => {
    setSelectedClient(client);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedClient(null);
    
    // Clear URL parameters without forcing page reload (stay on current view)
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.delete('focus');
      url.searchParams.delete('country'); // Remove country parameter as well
      
      // Always just update URL without reload to stay on current view
      window.history.replaceState({}, '', url.toString());
      
      // Reset focus flag without reload
      if (focusHandledRef.current) {
        focusHandledRef.current = null;
      }
    }
  };

  const handleModalSave = () => {
    setIsModalOpen(false);
    setSelectedClient(null);
    
    // Clear URL parameters without forcing page reload (stay on current view)
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.delete('focus');
      url.searchParams.delete('country'); // Remove country parameter as well
      
      // Always just update URL without reload to stay on current view
      window.history.replaceState({}, '', url.toString());
      
      // Reset focus flag and refresh data
      if (focusHandledRef.current) {
        focusHandledRef.current = null;
      }
      
      // Refresh data to reflect any changes made in modal
      onRefresh?.();
    }
  };

  const handleAddClient = () => {
    setIsAddClientModalOpen(true);
  };

  const handleAddClientModalClose = () => {
    setIsAddClientModalOpen(false);
  };

  const handleAddClientModalSave = async () => {
    setIsAddClientModalOpen(false);
    // Immediately refresh data without delay
    if (onRefresh) {
      try {
        await onRefresh();
        console.log('✅ Data refreshed successfully after adding client');
      } catch (error) {
        console.error('❌ Error refreshing data after adding client:', error);
        // Retry once after a short delay if first attempt fails
        setTimeout(() => {
          onRefresh();
        }, 1000);
      }
    }
  };

  const handleHelpClick = () => {
    setIsHelpModalOpen(true);
  };

  const handleExportData = () => {
    try {
      // Prepare data for export
      const exportData = filteredApplications.map((app, index) => {
        const formData = app.form_data as Record<string, any>;
        const servicePackage = app.service_package;
        
        return {
          [t('clients.excel.clientNumber')]: index + 1,
          [t('clients.excel.clientId')]: app.id,
          [t('clients.excel.firstName')]: formData.name || '',
          [t('clients.excel.lastName')]: formData.surname || '',
          [t('clients.excel.phone')]: app.phone_number || '',
          [t('clients.excel.email')]: formData.email || '',
          [t('clients.excel.age')]: calculateAge(formData.dateOfBirth) || '',
          [t('clients.excel.visaCountry')]: formData.visaCountry || '',
          [t('clients.excel.profession')]: formData.occupation || formData.profession || '',
          [t('clients.excel.income')]: formData.income || formData.salary || '',
          [t('clients.excel.maritalStatus')]: formData.maritalStatus || '',
          [t('clients.excel.visaStatus')]: app.visa_status,
          [t('clients.excel.paymentStatus')]: app.service_payment_status,
          [t('clients.excel.servicePackage')]: servicePackage?.title || '',
          [t('clients.excel.packagePrice')]: servicePackage?.price ? Number(servicePackage.price).toLocaleString() : '',
          [t('clients.excel.consularFee')]: app.consular_fee_paid ? t('clients.excel.paid') : t('clients.excel.unpaid'),
          [t('clients.excel.requiresFixes')]: app.requires_fixes ? t('clients.excel.yes') : t('clients.excel.no'),
          [t('clients.excel.comment')]: app.fix_comment || '',
          [t('clients.excel.currentStep')]: `${app.step_status}. ${getStepLabel(selectedCountry || 'US', app.step_status)}`,
          [t('clients.excel.createdDate')]: formatDate(app.created_at),
          [t('clients.excel.updatedDate')]: formatDate(app.updated_at || app.created_at),
          [t('clients.excel.visaHistory')]: formData.hasVisaHistory ? t('clients.excel.has') : t('clients.excel.hasNot'),
          [t('clients.excel.property')]: formData.hasProperty ? t('clients.excel.has') : t('clients.excel.hasNot'),
          [t('clients.excel.sponsor')]: formData.hasSponsor ? t('clients.excel.has') : t('clients.excel.hasNot')
        };
      });

      if (exportData.length === 0) {
        error(t('clients.noDataForExport'));
        return;
      }

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // Set column widths for better readability
      const colWidths = [
        { wch: 5 },   // №
        { wch: 35 },  // ID клиента
        { wch: 15 },  // Имя
        { wch: 15 },  // Фамилия
        { wch: 15 },  // Телефон
        { wch: 25 },  // Email
        { wch: 8 },   // Возраст
        { wch: 12 },  // Страна визы
        { wch: 20 },  // Профессия
        { wch: 12 },  // Доход
        { wch: 18 },  // Семейное положение
        { wch: 15 },  // Статус визы
        { wch: 15 },  // Статус оплаты
        { wch: 30 },  // Пакет услуг
        { wch: 18 },  // Стоимость пакета
        { wch: 15 },  // Консульский сбор
        { wch: 15 },  // Требуются правки
        { wch: 30 },  // Комментарий
        { wch: 25 },  // Текущий шаг
        { wch: 15 },  // Дата создания
        { wch: 15 },  // Дата обновления
        { wch: 15 },  // Визовая история
        { wch: 12 },  // Имущество
        { wch: 10 }   // Спонсор
      ];
      ws['!cols'] = colWidths;

      // Style the header row
      const headerRange = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      
      // Apply header styles
      for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        
        ws[cellAddress].s = {
          fill: { 
            fgColor: { rgb: "4F46E5" } // Indigo background
          },
          font: { 
            color: { rgb: "FFFFFF" }, // White text
            bold: true,
            sz: 12
          },
          alignment: { 
            horizontal: "center",
            vertical: "center",
            wrapText: true
          },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
          }
        };
      }

      // Apply alternating row colors and formatting
      for (let row = 1; row <= headerRange.e.r; row++) {
        const isEvenRow = row % 2 === 0;
        const bgColor = isEvenRow ? "F8FAFC" : "FFFFFF"; // Light gray for even rows
        
        for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) continue;
          
          // Get column header to apply specific colors
          const headerCell = XLSX.utils.encode_cell({ r: 0, c: col });
          const columnHeader = ws[headerCell]?.v || '';
          
          let cellBgColor = bgColor;
          let fontColor = "000000";
          
          // Apply specific colors based on content
          if (columnHeader.includes('Статус оплаты')) {
            const value = ws[cellAddress].v;
            if (value === SERVICE_PAYMENT_STATUS.PAID) {
              cellBgColor = "D1FAE5"; // Light green
              fontColor = "065F46"; // Dark green
            } else if (value === SERVICE_PAYMENT_STATUS.UNPAID) {
              cellBgColor = "FEE2E2"; // Light red
              fontColor = "991B1B"; // Dark red
            }
          } else if (columnHeader.includes('Требуются правки')) {
            const value = ws[cellAddress].v;
            if (value === 'Да') { // TODO: Create constant for boolean yes/no values
              cellBgColor = "FED7AA"; // Light orange
              fontColor = "C2410C"; // Dark orange
            }
          } else if (columnHeader.includes('Консульский сбор')) {
            const value = ws[cellAddress].v;
            if (value === 'Оплачен') { // TODO: Create constant for consular fee status
              cellBgColor = "DBEAFE"; // Light blue
              fontColor = "1E40AF"; // Dark blue
            }
          } else if (columnHeader.includes('Статус визы')) {
            const value = ws[cellAddress].v;
            if (value === VISA_STATUS.APPROVED) {
              cellBgColor = "D1FAE5"; // Light green
              fontColor = "065F46"; // Dark green
            } else if (value === VISA_STATUS.REJECTED) {
              cellBgColor = "FEE2E2"; // Light red
              fontColor = "991B1B"; // Dark red
            } else if (value === VISA_STATUS.SUBMITTED) {
              cellBgColor = "E0E7FF"; // Light indigo
              fontColor = "3730A3"; // Dark indigo
            }
          }
          
          ws[cellAddress].s = {
            fill: { fgColor: { rgb: cellBgColor } },
            font: { 
              color: { rgb: fontColor },
              sz: 10
            },
            alignment: { 
              horizontal: columnHeader.includes('№') || columnHeader.includes('Возраст') || columnHeader.includes('Доход') ? "center" : "left",
              vertical: "center",
              wrapText: true
            },
            border: {
              top: { style: "thin", color: { rgb: "E5E7EB" } },
              bottom: { style: "thin", color: { rgb: "E5E7EB" } },
              left: { style: "thin", color: { rgb: "E5E7EB" } },
              right: { style: "thin", color: { rgb: "E5E7EB" } }
            }
          };
        }
      }

      // Add the worksheet to workbook
      const countryName = countries.find(c => c.code === selectedCountry)?.name || selectedCountry;
      XLSX.utils.book_append_sheet(wb, ws, `${t('clients.excel.sheetName')} ${countryName}`);

      // Generate filename with country and date
      const dateStr = new Date().toISOString().split('T')[0];
      const filename = `clients_${countryName}_${dateStr}.xlsx`;

      // Write the file
      XLSX.writeFile(wb, filename);
      
      success(t('clients.dataExportedSuccess'), `${t('clients.fileCreatedWithCoding')} ${filename}`);
    } catch (err) {
      console.error('Export error:', err);
      error(t('clients.exportError'), t('clients.tryAgainOrContact'));
    }
  };

  // Pagination functions
  const handleLoadMore = () => {
    setCurrentPage(prevPage => prevPage + 1);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const getPaginatedApplications = (apps: VisaApplicationWithStatus[]): VisaApplicationWithStatus[] => {
    if (viewMode === VIEW_MODE.BOARD) {
      // For board view, show more items initially but still allow Load More
      const startIndex = 0;
      const endIndex = currentPage * itemsPerPage;
      setShowLoadMore(endIndex < apps.length);
      return apps.slice(startIndex, endIndex);
    } else {
      // For table view, use traditional pagination
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      setShowLoadMore(endIndex < apps.length);
      return apps.slice(startIndex, endIndex);
    }
  };

  const handleRefreshData = async () => {
    try {
      await onRefresh?.();
      success(t('clients.dataRefreshed') || 'Данные успешно обновлены');
    } catch (err) {
      console.error('Error refreshing data:', err);
      error(t('common.error'), 'Ошибка при обновлении данных');
    }
  };

  const handleRemoveDuplicates = async () => {
    try {
      // First, check for duplicates
      const checkResponse = await fetch('/api/admin/remove-duplicates');
      const checkData = await checkResponse.json();
      
      if (!checkResponse.ok) {
        error(t('clients.duplicateCheckError'), checkData.error);
        return;
      }

      const duplicates = checkData.duplicates || [];
      
      if (duplicates.length === 0) {
        success(t('clients.duplicatesNotFound'), t('clients.allRecordsUnique'));
        return;
      }

      const duplicatesToDelete = duplicates.filter((d: any) => d.action === 'DELETE');
      
      if (duplicatesToDelete.length === 0) {
        success(t('clients.duplicatesNotFound'), t('clients.allRecordsUnique'));
        return;
      }

      // Show confirmation dialog
      const duplicateSuffix = duplicatesToDelete.length === 1 ? '' : duplicatesToDelete.length < 5 ? t('clients.clientsPlural') : t('clients.duplicatesFoundPlural');
      const confirmed = window.confirm(
        `${t('clients.duplicatesFoundCount')} ${duplicatesToDelete.length} ${t('clients.duplicatesFoundSuffix')}${duplicateSuffix}.\n\n` +
        `${t('clients.duplicatesDeleteConfirm')}\n\n` +
        `${t('clients.continueRemoval')}`
      );

      if (!confirmed) {
        return;
      }

      // Remove duplicates
      const removeResponse = await fetch('/api/admin/remove-duplicates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const removeData = await removeResponse.json();

      if (removeResponse.ok) {
        const removedSuffix = removeData.removed === 1 ? '' : removeData.removed < 5 ? t('clients.clientsPlural') : t('clients.duplicatesFoundPlural');
        success(
          t('clients.duplicatesRemoved'), 
          `${t('clients.successfullyRemoved')} ${removeData.removed} ${t('clients.duplicatesFoundSuffix')}${removedSuffix}`
        );
        onRefresh?.(); // Refresh the data
      } else {
        error(t('clients.removeDuplicatesError'), removeData.error);
      }
    } catch (err) {
      console.error('Error removing duplicates:', err);
      error(t('clients.removeDuplicatesError'), t('clients.operationError'));
    }
  };

  const handleClearFilters = () => {
    setFilters({
      name: '',
      activity: '',
      minIncome: '',
      maxIncome: '',
      visaHistory: '',
      hasProperty: '',
      maritalStatus: '',
      hasSponsor: '',
      dateFrom: '',
      dateTo: ''
    });
  };

  // Clear URL parameters and return to all countries view
  const handleShowAllCountries = () => {
    if (typeof window !== 'undefined') {
      // Get current URL without focus and country parameters
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.delete('focus');
      currentUrl.searchParams.delete('country');
      
      // Force page reload to reset all state and show all countries
      window.location.href = currentUrl.toString();
    }
  };

  const groupApplicationsByStatus = (apps: VisaApplicationWithStatus[]) => {
    const normalizedCountryCode = normalizeCountry(selectedCountry || 'US');
    const currentStatuses = getAllStepsForCountry(normalizedCountryCode);
    const grouped: { [key: string]: VisaApplicationWithStatus[] } = {};
    
    // Debug logging for workflow steps
    console.log('=== GROUP APPLICATIONS BY STATUS DEBUG ===');
    console.log('Selected country (raw):', selectedCountry);
    console.log('Normalized country code:', normalizedCountryCode);
    console.log('Current workflow steps:', currentStatuses);
    console.log('Total apps to group:', apps.length);
    
    // Debug: Count clients by progress status
    const statusCounts = apps.reduce((acc, app) => {
      const status = app.client_progress_status || 'undefined';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    console.log('Client progress status distribution:', statusCounts);
    
    currentStatuses.forEach((status: WorkflowStep) => {
      grouped[status.id.toString()] = [];
    });

    // Map client_progress_status to workflow step IDs
    const progressToStepMap: { [key: string]: number } = {
      [CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE]: 1,
      [CLIENT_PROGRESS_STATUS.PAID_PACKAGE]: 2,
      [CLIENT_PROGRESS_STATUS.BOT_COLLECTING]: 3,
      [CLIENT_PROGRESS_STATUS.WAITING_INVITATION]: 4,
      [CLIENT_PROGRESS_STATUS.CASE_COORDINATION]: 5,
      [CLIENT_PROGRESS_STATUS.FORM_FILLING]: 6,
      [CLIENT_PROGRESS_STATUS.SUBMITTED]: 7
    };
    
    // Also create a reverse mapping from workflow step labels to step IDs
    const labelToStepMap: { [key: string]: number } = {};
    currentStatuses.forEach(step => {
      labelToStepMap[step.label] = step.id;
    });
    
    console.log('Progress to Step mapping:', progressToStepMap);
    console.log('Label to Step mapping:', labelToStepMap);

    // Sort all applications by updated_at (newest first), fallback to created_at if updated_at is null
    const sortedApps = [...apps].sort((a, b) => {
      const aTime = a.updated_at ? new Date(a.updated_at).getTime() : new Date(a.created_at).getTime();
      const bTime = b.updated_at ? new Date(b.updated_at).getTime() : new Date(b.created_at).getTime();
      return bTime - aTime;
    });
    
    sortedApps.forEach(app => {
      // Normalize client_progress_status to handle case sensitivity issues
      if (app.client_progress_status) {
        // Normalize case variations of "Прошли опросник"
        if (app.client_progress_status.toLowerCase() === 'прошли опросник') {
          app.client_progress_status = CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE;
        }
      }
      
      // Determine step ID based on client_progress_status
      let stepId = 1; // Default to step 1
      
      // Debug logging for clients with null/undefined status
      if (!app.client_progress_status) {
        console.log('Client with null/undefined progress status:', {
          id: app.id,
          client_progress_status: app.client_progress_status,
          step_status: app.step_status,
          service_payment_status: app.service_payment_status,
          name: `${(app.form_data as any)?.surname || ''} ${(app.form_data as any)?.name || ''}`.trim()
        });
      }
      
      // Debug logging for all clients
      if (app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE) {
        console.log('BOARD GROUPING DEBUG - Paid Package Client:', {
          id: app.id,
          client_progress_status: app.client_progress_status,
          client_progress_status_type: typeof app.client_progress_status,
          client_progress_status_length: app.client_progress_status?.length,
          expected_value: CLIENT_PROGRESS_STATUS.PAID_PACKAGE,
          expected_type: typeof CLIENT_PROGRESS_STATUS.PAID_PACKAGE,
          expected_length: CLIENT_PROGRESS_STATUS.PAID_PACKAGE.length,
          values_match: app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE,
          mapping_exists: !!progressToStepMap[app.client_progress_status],
          mapping_value: progressToStepMap[app.client_progress_status],
          all_mapping_keys: Object.keys(progressToStepMap)
        });
      }
      
      // PRIORITY 1: Check payment status first - if they paid, they should be in step 2 regardless of client_progress_status
      if (app.service_payment_status === SERVICE_PAYMENT_STATUS.PAID || app.is_paid) {
        stepId = 2; // Paid package step
        console.log('🟢 PAID CLIENT -> Step 2:', {
          id: app.id,
          name: `${(app.form_data as any)?.surname || ''} ${(app.form_data as any)?.name || ''}`.trim(),
          service_payment_status: app.service_payment_status,
          client_progress_status: app.client_progress_status,
          assigned_stepId: stepId
        });
      } 
      // PRIORITY 2: Use client_progress_status for business workflow grouping
      else if (app.client_progress_status && progressToStepMap[app.client_progress_status]) {
        stepId = progressToStepMap[app.client_progress_status];
      } else if (app.client_progress_status && labelToStepMap[app.client_progress_status]) {
        // Fallback: Try to match by workflow step label directly
        stepId = labelToStepMap[app.client_progress_status];
        console.log('Using label mapping for client:', app.id, 'status:', app.client_progress_status, 'stepId:', stepId);
      } else if (!app.client_progress_status) {
        // Fallback for clients with no progress status
        if (app.step_status > 0) {
          stepId = 1; // They're at "Прошли опросник" stage
          console.log('Assigning unpaid client with step_status to step 1:', app.id);
        } else {
          stepId = 1; // Default to questionnaire completed
          console.log('Assigning unpaid client without progress status to step 1:', app.id);
        }
      }
      
      // Debug logging for paid package clients
      if (app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE) {
        console.log('Processing paid package client:', {
          id: app.id,
          step_status: app.step_status,
          client_progress_status: app.client_progress_status,
          mapped_stepId: stepId,
          service_payment_status: app.service_payment_status,
          grouped_key_exists: !!grouped[stepId.toString()],
          name: `${(app.form_data as any)?.surname || ''} ${(app.form_data as any)?.name || ''}`.trim()
        });
      }
      
      if (grouped[stepId.toString()]) {
        grouped[stepId.toString()].push(app);
      } else {
        // If step doesn't exist in current workflow, add to step 1
        console.log(`Step ${stepId} not found in workflow, moving to step 1:`, {
          id: app.id,
          client_progress_status: app.client_progress_status,
          mapped_stepId: stepId,
          available_steps: Object.keys(grouped)
        });
        if (grouped['1']) {
          grouped['1'].push(app);
        }
      }
    });

    // Debug the final grouped result
    console.log('=== FINAL GROUPING RESULT ===');
    let totalGrouped = 0;
    Object.entries(grouped).forEach(([stepId, clients]) => {
      totalGrouped += clients.length;
      console.log(`Step ${stepId} (${currentStatuses.find(s => s.id.toString() === stepId)?.label || 'Unknown'}):`, clients.length, 'clients');
      if (clients.length > 0 && stepId === '2') {
        console.log('Step 2 clients details:', clients.map(c => ({
          id: c.id,
          client_progress_status: c.client_progress_status,
          name: `${(c.form_data as any)?.surname || ''} ${(c.form_data as any)?.name || ''}`.trim()
        })));
      }
    });
    console.log('Total clients in groups:', totalGrouped);
    console.log('Total clients passed to function:', apps.length);
    console.log('Missing clients count:', apps.length - totalGrouped);
    console.log('==========================================');

    return grouped;
  };

  const currentStatuses = getAllStepsForCountry(normalizeCountry(selectedCountry || 'US'));
  const groupedApplications = groupApplicationsByStatus(filteredApplications);
  
  // Debug: Log board view summary with detailed step 1 info
  console.log('=== BOARD VIEW SUMMARY ===');
  console.log('Selected country:', selectedCountry);
  console.log('Normalized country:', normalizeCountry(selectedCountry || 'US'));
  console.log('Filtered applications total:', filteredApplications.length);
  console.log('Paid package clients in filtered apps:', filteredApplications.filter(app => app.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE).length);
  
  // Detailed step 1 (Прошли опросник) analysis
  const step1Apps = groupedApplications['1'] || [];
  console.log('Step 1 (Прошли опросник) detailed analysis:');
  console.log('  Total in step 1:', step1Apps.length);
  console.log('  Unpaid in step 1:', step1Apps.filter(app => app.service_payment_status === SERVICE_PAYMENT_STATUS.UNPAID).length);
  if (step1Apps.length > 0) {
    console.log('  First 5 step 1 clients:', step1Apps.slice(0, 5).map(app => ({
      name: `${app.form_data?.surname || ''} ${app.form_data?.name || ''}`.trim(),
      payment: app.service_payment_status,
      company: app.company_id
    })));
  }
  
  console.log('Board groups:', Object.fromEntries(Object.entries(groupedApplications).map(([key, apps]) => [key, apps.length])));
  console.log('=========================');
  
  if (showCountrySelection) {
    return (
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('clients.title')}</h1>
                      <p className="text-lg text-gray-600">{t('clients.selectCountry')}</p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
          {countries.map((country) => (
            <div
              key={country.code}
              onClick={() => handleCountrySelect(country.code)}
              className={`relative cursor-pointer group bg-white rounded-xl shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 p-8 text-center border-2 border-transparent hover:border-blue-200 ${
                country.active ? 'ring-2 ring-blue-500 shadow-blue-100' : 'opacity-75 hover:opacity-100'
              }`}
            >
              <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">{country.flag}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">{country.code}</h3>
              <p className="text-sm text-gray-600 mb-2 group-hover:text-gray-800 transition-colors duration-300">{country.name}</p>
              
              {country.count !== undefined && (
                <div className="text-lg font-bold text-blue-600 group-hover:text-blue-700 group-hover:scale-105 transition-all duration-300">
                  {country.count} {t('clients.clientsCount')}
                </div>
              )}
              
              {country.active && (
                <div className="absolute top-2 right-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse group-hover:bg-blue-600"></div>
                </div>
              )}
              
              {/* Gradient overlay on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-indigo-50/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              {/* Subtle glow effect */}
              <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-lg shadow-blue-200/50"></div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            {t('clients.totalApplicationsLabel')}: <span className="font-semibold">{totalApplications}</span>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 relative">
      {/* No full-page loading overlay. Only shimmer/blur on table/list below. */}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBackToCountries}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            {t('clients.backToCountries')}
          </button>
          
          <div className="flex items-center space-x-2">
            <span className="text-3xl">{countries.find(c => c.code === selectedCountry)?.flag}</span>
            <h1 className="text-2xl font-bold text-gray-900">
              {countries.find(c => c.code === selectedCountry)?.name}
            </h1>
          </div>

          <div className="text-sm text-gray-600 flex items-center space-x-4">
            <div>
              {t('clients.totalApplicationsLabel')}: <span className="font-semibold">{totalApplications}</span>
            </div>
            {filteredApplications.length !== totalApplications && (
              <div>
                {t('clients.filteredApplicationsLabel')}: <span className="font-semibold">{filteredApplications.length}</span>
              </div>
            )}
          </div>
        </div>

        {/* View Controls */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode(VIEW_MODE.BOARD)}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                viewMode === VIEW_MODE.BOARD 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('clients.boardView')}
            </button>
            <button
              onClick={() => setViewMode(VIEW_MODE.TABLE)}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                viewMode === VIEW_MODE.TABLE 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('clients.tableView')}
            </button>
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            {t('clients.filters.advancedFilters')}
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('clients.quickActions')}</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Add Client Button - Only for visa companies, not superadmin */}
          {userRole !== 'super_admin' && (
            <button
              onClick={handleAddClient}
              className="inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {t('clients.addClient')}
            </button>
          )}
          
          <button
            onClick={handleExportData}
            disabled={filteredApplications.length === 0}
            className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            {t('clients.exportData')}
          </button>
          
          <button
            onClick={handleRefreshData}
            disabled={_isLoading}
            className={`inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              _isLoading 
                ? 'text-gray-400 bg-gray-100 cursor-not-allowed' 
                : 'text-gray-700 bg-white hover:bg-gray-50'
            }`}
          >
            {_isLoading ? (
              <svg className="mr-2 h-5 w-5 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            )}
            {_isLoading ? t('common.loading') : t('clients.refreshData')}
          </button>
          
          <button
            onClick={handleRemoveDuplicates}
            className="inline-flex items-center justify-center px-4 py-3 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
          >
            <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            {t('clients.removeDuplicates')}
          </button>
          
          <button
            onClick={handleHelpClick}
            className="text-sm text-gray-500 flex items-center justify-center hover:text-gray-700 transition-colors duration-200 px-4 py-3 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {t('clients.helpButton')}
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.name')}</label>
              <input
                type="text"
                value={filters.name}
                onChange={(e) => setFilters(prev => ({ ...prev, name: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={t('clients.filters.searchByName')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.activity')}</label>
              <input
                type="text"
                value={filters.activity}
                onChange={(e) => setFilters(prev => ({ ...prev, activity: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={t('clients.filters.profession')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.incomeFrom')}</label>
              <input
                type="number"
                min="0"
                step="1"
                value={filters.minIncome}
                onChange={(e) => {
                  const value = e.target.value;
                  // Prevent negative numbers by checking if value is negative
                  if (value === '' || parseFloat(value) >= 0) {
                    setFilters(prev => ({ ...prev, minIncome: value }));
                  }
                }}
                onKeyDown={(e) => {
                  // Prevent minus key
                  if (e.key === '-' || e.key === 'e' || e.key === 'E') {
                    e.preventDefault();
                  }
                }}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={t('clients.filters.minimum')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.incomeTo')}</label>
              <input
                type="number"
                min="0"
                step="1"
                value={filters.maxIncome}
                onChange={(e) => {
                  const value = e.target.value;
                  // Prevent negative numbers by checking if value is negative
                  if (value === '' || parseFloat(value) >= 0) {
                    setFilters(prev => ({ ...prev, maxIncome: value }));
                  }
                }}
                onKeyDown={(e) => {
                  // Prevent minus key
                  if (e.key === '-' || e.key === 'e' || e.key === 'E') {
                    e.preventDefault();
                  }
                }}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={t('clients.filters.maximum')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.visaHistory')}</label>
              <select
                value={filters.visaHistory}
                onChange={(e) => setFilters(prev => ({ ...prev, visaHistory: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">{t('clients.filters.all')}</option>
                <option value="yes">{t('clients.filters.hasVisaHistory')}</option>
                <option value="no">{t('clients.filters.noVisaHistory')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.property')}</label>
              <select
                value={filters.hasProperty}
                onChange={(e) => setFilters(prev => ({ ...prev, hasProperty: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">{t('clients.filters.all')}</option>
                <option value="yes">{t('clients.filters.hasProperty')}</option>
                <option value="no">{t('clients.filters.noProperty')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.maritalStatus')}</label>
              <select
                value={filters.maritalStatus}
                onChange={(e) => setFilters(prev => ({ ...prev, maritalStatus: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">{t('clients.filters.all')}</option>
                <option value="married">{t('clients.filters.married')}</option>
                <option value="single">{t('clients.filters.single')}</option>
                <option value="divorced">{t('clients.filters.divorced')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t('clients.filters.sponsor')}</label>
              <select
                value={filters.hasSponsor}
                onChange={(e) => setFilters(prev => ({ ...prev, hasSponsor: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">{t('clients.filters.all')}</option>
                <option value="yes">{t('clients.filters.hasSponsor')}</option>
                <option value="no">{t('clients.filters.noSponsor')}</option>
              </select>
            </div>
          </div>
          
          {/* Clear Filters Button */}
          <div className="mt-4 flex justify-end space-x-3">
            <button
              onClick={handleShowAllCountries}
              className="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {t('clients.showAllCountries')}
            </button>
            
            <button
              onClick={handleClearFilters}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              {t('clients.filters.resetFilters')}
            </button>
          </div>
        </div>
      )}

      {/* Pagination Controls */}
      <div className="flex items-center justify-between bg-gray-50 px-4 py-3 rounded-lg">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-700">
            Показано: {filteredApplications.length} из {totalApplications || applications.length}
          </span>
          
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-700">На странице:</label>
            <select
              value={itemsPerPage}
              onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value={50}>50</option>
              <option value={100}>100</option>
              <option value={200}>200</option>
              <option value={500}>500</option>
            </select>
          </div>
        </div>

        {showLoadMore && (
          <button
            onClick={handleLoadMore}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
            Загрузить ещё
          </button>
        )}
      </div>

      {/* Content View */}
      {_isLoading ? (
        // Show shimmer/blur only on the table/list, not the whole page
        viewMode === VIEW_MODE.BOARD ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-4 opacity-60 blur-sm pointer-events-none select-none">
            <SkeletonTrelloColumn />
            <SkeletonTrelloColumn />
            <SkeletonTrelloColumn />
            <SkeletonTrelloColumn />
            <SkeletonTrelloColumn />
            <SkeletonTrelloColumn />
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden opacity-60 blur-sm pointer-events-none select-none">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('clients.firstName')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('clients.lastName')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('clients.phone')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('clients.email')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('clients.consularFee')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('common.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <SkeletonTableRow />
                  <SkeletonTableRow />
                  <SkeletonTableRow />
                  <SkeletonTableRow />
                  <SkeletonTableRow />
                  <SkeletonTableRow />
                </tbody>
              </table>
            </div>
          </div>
        )
      ) : viewMode === VIEW_MODE.BOARD ? (
        /* Trello-style Board */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-4">
          {currentStatuses.map((status: WorkflowStep) => (
            <div key={status.id} className={`${status.color} rounded-lg p-4 min-h-[600px] max-h-[800px] flex flex-col`}>
              <div className="flex items-center justify-between mb-4 flex-shrink-0">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-gray-800">{status.label}</h3>
                </div>
                <span className="bg-white text-gray-600 text-sm px-2 py-1 rounded-full">
                  {groupedApplications[status.id.toString()]?.length || 0}
                </span>
              </div>
              
              <div className="flex-1 overflow-y-auto space-y-3 pr-2 custom-scrollbar" 
                style={{
                  scrollbarWidth: 'thin', 
                  scrollbarColor: '#cbd5e0 transparent',
                  maxHeight: '650px',
                  minHeight: '400px'
                }}>
                {groupedApplications[status.id.toString()]?.map((client, clientIndex) => {
                  // Debug logging for step 1 rendering
                  if (status.id === 1 && clientIndex < 3) {
                    console.log(`Rendering step 1 client ${clientIndex + 1}:`, {
                      name: `${client.form_data?.surname || ''} ${client.form_data?.name || ''}`.trim(),
                      id: client.id,
                      payment: client.service_payment_status
                    });
                  }
                  const formData = client.form_data as Record<string, any>;
                  // Debug logging for paid package clients in board view
                  if (client.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE && status.id === 2) {
                    console.log('Rendering paid package client in board view column:', {
                      id: client.id,
                      name: `${formData.surname || ''} ${formData.name || ''}`.trim(),
                      step_status: client.step_status,
                      client_progress_status: client.client_progress_status,
                      service_payment_status: client.service_payment_status,
                      column_id: status.id,
                      column_label: status.label
                    });
                  }
                  // Use updated_at if available, otherwise fallback to created_at
                  const displayDate = new Date(client.updated_at || client.created_at);
                  const formattedDate = displayDate.toLocaleDateString('ru-RU', {
                    day: '2-digit',
                    month: '2-digit',
                    year: '2-digit'
                  });
                  const formattedTime = displayDate.toLocaleTimeString('ru-RU', {
                    hour: '2-digit',
                    minute: '2-digit'
                  });
                  
                  return (
                    <div
                      key={client.id}
                      onClick={() => handleClientClick(client)}
                      className="bg-white p-3 rounded-lg shadow-sm cursor-pointer hover:shadow-md transition-shadow border-l-4 border-blue-400"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900 text-sm">
                          {formData.surname} {formData.name}
                          {client.added_by_manager && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                              👤 {t('common.manager')}
                            </span>
                          )}
                        </h4>
                        <button className="text-gray-400 hover:text-gray-600 flex-shrink-0">
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        </button>
                      </div>
                      
                      <div className="text-xs text-gray-600 space-y-1">
                        <p>📞 {client.phone_number || t('clients.phoneNotSpecified')}</p>
                        <p 
                          className="truncate max-w-full"
                          title={formData.email || t('clients.emailNotSpecified')}
                        >
                          📧 {formData.email || t('clients.emailNotSpecified')}
                        </p>
                        <p className="text-gray-500">
                          📅 {formattedDate} в {formattedTime}
                        </p>
                      </div>
                      
                      <div className="mt-3 space-y-2">
                        {/* Payment Status Badge - Hide in "Оплатили пакет услуг" column */}
                        {status.id !== 2 && (
                          <div>
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              client.service_payment_status === SERVICE_PAYMENT_STATUS.PAID 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {client.service_payment_status === SERVICE_PAYMENT_STATUS.PAID ? '💰 Пакет оплачен' : '⏳ Ожидается оплата'}
                            </span>
                          </div>
                        )}
                        
                        {/* Consular Fee Badge */}
                        <div>
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            client.consular_fee_paid 
                              ? 'bg-blue-100 text-blue-800' 
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            {client.consular_fee_paid ? '🏛️ Консул. сбор оплачен' : '🏛️ Консул. сбор не оплачен'}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
                
                {(!groupedApplications[status.id.toString()] || groupedApplications[status.id.toString()].length === 0) && (
                  <div className="text-center text-gray-500 text-sm py-8">
                    Нет клиентов
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Table View */
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('clients.firstName')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('clients.lastName')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('clients.phone')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('clients.email')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Статус оплаты пакета
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('clients.consularFee')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('common.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredApplications.map((client) => {
                  const formData = client.form_data as Record<string, any>;
                  // Debug logging for paid package clients in table view
                  if (client.client_progress_status === CLIENT_PROGRESS_STATUS.PAID_PACKAGE) {
                    console.log('Rendering paid package client in table:', {
                      id: client.id,
                      name: `${formData.surname || ''} ${formData.name || ''}`.trim(),
                      step_status: client.step_status,
                      client_progress_status: client.client_progress_status,
                      service_payment_status: client.service_payment_status
                    });
                  }
                  return (
                    <tr key={client.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formData.name || '-'}
                        {client.added_by_manager && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                            👤 {t('common.manager')}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formData.surname || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {client.phone_number || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div 
                          className="truncate max-w-xs"
                          title={formData.email || '-'}
                        >
                          {formData.email || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          client.service_payment_status === SERVICE_PAYMENT_STATUS.PAID 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {client.service_payment_status === SERVICE_PAYMENT_STATUS.PAID ? 'Пакет оплачен' : 'Ожидается оплата'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          client.consular_fee_paid 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {client.consular_fee_paid ? 'Консул. сбор оплачен' : 'Консул. сбор не оплачен'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleClientClick(client)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          {t('common.edit')}
                        </button>
                      </td>
                    </tr>
                  );
                })}
                {filteredApplications.length === 0 && (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                      {t('clients.noDataForExport')}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Client Modal */}
      {isModalOpen && selectedClient && (
        <>
          <ClientModal
            client={selectedClient}
            onClose={handleModalClose}
            onSave={handleModalSave}
            isNew={false}
            similarCases={[]}
            userRole={userRole}
          />
        </>
      )}

      {/* Add Client Modal - Only for visa companies, not superadmin */}
      {userRole !== 'super_admin' && (
        <AddClientModal
          isOpen={isAddClientModalOpen}
          onClose={handleAddClientModalClose}
          onSave={handleAddClientModalSave}
          companyId={companyId}
          selectedCountry={selectedCountry || undefined}
          userRole={userRole}
        />
      )}

      {/* Help Modal */}
      <HelpModal
        isOpen={isHelpModalOpen}
        onClose={() => setIsHelpModalOpen(false)}
      />
    </div>
  );
} 