import { useTranslation } from '../../utils/localization';
import { Admin } from '../../types/admin';
import { DashboardTab } from '../../pages/admin/dashboard';

interface SidebarProps {
  activeTab: DashboardTab;
  onTabChange: (tab: DashboardTab) => void;
  admin: Omit<Admin, 'password'>;
  onLogout: () => void;
  isSuperAdmin: boolean;
}

export default function Sidebar({ activeTab, onTabChange, admin, onLogout, isSuperAdmin }: SidebarProps) {
  const { t } = useTranslation();

  const navigationItems = [
    {
      id: 'glavnoe' as DashboardTab,
      name: t('navigation.dashboard'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
        </svg>
      ),
      roles: ['super_admin', 'visa_admin', 'manager']
    },
    {
      id: 'clients' as DashboardTab,
      name: t('navigation.clients'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
        </svg>
      ),
      roles: ['super_admin', 'visa_admin', 'manager']
    },
    {
      id: 'statistics' as DashboardTab,
      name: t('navigation.statistics'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
        </svg>
      ),
      roles: ['super_admin', 'visa_admin', 'manager']
    },
    {
      id: 'companies' as DashboardTab,
      name: t('navigation.companies'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
        </svg>
      ),
      roles: ['super_admin', 'visa_admin']
    },
    {
      id: 'employees' as DashboardTab,
      name: t('navigation.employees'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
        </svg>
      ),
      roles: ['visa_admin']
    },
    {
      id: 'price-list' as DashboardTab,
      name: t('navigation.priceList'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
        </svg>
      ),
      roles: ['visa_admin', 'manager']
    },
    {
      id: 'settings' as DashboardTab,
      name: t('navigation.settings'),
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
      ),
      roles: ['super_admin', 'visa_admin', 'manager']
    }
  ];

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-64 bg-blue-900 overflow-y-auto">
      <div className="flex flex-col h-full">
        {/* Logo and Company Info */}
        <div className="flex-shrink-0 px-6 py-8">
          <div className="flex items-center">
            <img
              className="h-12 w-12 rounded-lg"
              src="/visai_logo.png"
              alt="Visa AI"
            />
            <div className="ml-3">
              <h2 className="text-white text-lg font-semibold">Visa AI</h2>
              <p className="text-blue-200 text-sm">{t('common.adminPanel')}</p>
            </div>
          </div>
          
          {/* Admin Info */}
          <div className="mt-6 p-4 bg-blue-800 rounded-lg">
            <div className="text-white text-sm font-medium">{admin.username}</div>
            <div className="text-blue-200 text-xs">
              {isSuperAdmin ? t('common.superAdmin') : t('common.admin')}
            </div>
            {admin.companyName && (
              <div className="text-blue-200 text-xs mt-1">{admin.companyName}</div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-6 pb-4">
          <div className="space-y-2">
            {navigationItems
              .filter(item => {
                if (admin.role === 'super_admin') {
                  return ['glavnoe', 'clients', 'statistics', 'companies', 'settings'].includes(item.id);
                } else if (admin.role === 'visa_admin') {
                  return ['glavnoe', 'clients', 'statistics', 'employees', 'price-list', 'settings'].includes(item.id);
                } else if (admin.role === 'manager') {
                  return ['glavnoe', 'clients', 'statistics', 'price-list', 'settings'].includes(item.id);
                }
                return false;
              })
              .map((item) => (
              <button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === item.id
                    ? 'bg-blue-700 text-white shadow-lg'
                    : 'text-blue-100 hover:bg-blue-800 hover:text-white'
                }`}
              >
                <div className="mr-3">{item.icon}</div>
                {item.name}
              </button>
            ))}
          </div>
        </nav>

        {/* Logout Button */}
        <div className="flex-shrink-0 p-6">
          <button
            onClick={onLogout}
            className="w-full flex items-center px-4 py-3 text-sm font-medium text-red-300 hover:text-red-200 hover:bg-red-900/20 rounded-lg transition-colors"
          >
            <svg className="mr-3 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
            {t('navigation.logout')}
          </button>
        </div>
      </div>
    </div>
  );
} 