import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage, FormikProps } from 'formik';
import { getStepValidation } from '../utils/validationHelpers';
import { Step8Data } from '../utils/types';
import { COUNTRIES } from '../constants/countries';

interface Step8Props {
  initialValues: Step8Data;
  onSubmit: (values: Step8Data) => void;
  updateForm: (values: Step8Data) => void;
}

export interface Step8Ref {
  submitForm: () => void;
  isValid: boolean;
}

const occupationOptions = [
  { value: '', label: 'Выберите...' },
  { value: 'agriculture', label: 'Сельское хозяйство' },
  { value: 'artist', label: 'Искусство/Выступления' },
  { value: 'business', label: 'Бизнес' },
  { value: 'communications', label: 'Коммуникации' },
  { value: 'computer_science', label: 'Информационные технологии' },
  { value: 'culinary', label: 'Кулинария/Общепит' },
  { value: 'education', label: 'Образование' },
  { value: 'engineering', label: 'Инженерия' },
  { value: 'government', label: 'Государственная служба' },
  { value: 'homemaker', label: 'Домохозяйство' },
  { value: 'legal', label: 'Юриспруденция' },
  { value: 'medical', label: 'Медицина/Здравоохранение' },
  { value: 'military', label: 'Военная служба' },
  { value: 'natural_science', label: 'Естественные науки' },
  { value: 'not_employed', label: 'Безработный' },
  { value: 'physical_science', label: 'Физические науки' },
  { value: 'religious', label: 'Религиозная деятельность' },
  { value: 'research', label: 'Научные исследования' },
  { value: 'retired', label: 'Пенсионер' },
  { value: 'social_science', label: 'Социальные науки' },
  { value: 'student', label: 'Студент' },
  { value: 'other', label: 'Другое' },
];

// Consistent list of unemployed occupations
const UNEMPLOYED_OCCUPATIONS = ['not_employed', 'retired', 'homemaker'];

const educationLevelOptions = [
  { value: '', label: 'Выберите...' },
  { value: 'high_school', label: 'Среднее образование (школа)' },
  { value: 'technical_college', label: 'Техническое образование (колледж)' },
  { value: 'vocational_training', label: 'Профессиональное обучение' },
  { value: 'pre_university', label: 'Подготовительная программа' },
  { value: 'other', label: 'Другое' },
];

// const businessRegistrationOptions = [
//   { value: '', label: 'Выберите...' },
//   { value: 'TOO', label: 'ТОО' },
//   { value: 'IP', label: 'ИП' },
//   { value: 'self_employed', label: 'Самозанятый' },
//   { value: 'other', label: 'Другое' },
// ];
//
// const businessStatusOptions = [
//   { value: '', label: 'Выберите...' },
//   { value: 'director', label: 'Директор' },
//   { value: 'manager', label: 'Управляющий' },
//   { value: 'individual', label: 'Индивидуально' },
// ];

const Step8_EducationWork = forwardRef<Step8Ref, Step8Props>(({ initialValues, onSubmit, updateForm }, ref) => {
  const formikRef = useRef<FormikProps<Step8Data>>(null);
  const [formValues, setFormValues] = useState<Step8Data>(initialValues);
  const [forceUpdate, setForceUpdate] = useState(0);
  const validationSchema = getStepValidation(8);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      const formikValid = formikRef.current?.isValid ?? false;
      console.log('[Step8 Submit] submitForm called, formik valid:', formikValid);
      
      if (formikValid) {
        onSubmit(formValues);
      } else {
        console.log('Form is not valid, showing validation errors');
        // Show validation errors by triggering Formik validation
        formikRef.current?.validateForm().then(() => {
          formikRef.current?.setTouched({
            occupation: true,
            educationStatus: true,
            educationLocation: true,
            institutionName: true,
            companyName: true,
            workAddress: true,
            workExperience: true,
            workPhone: true,
            position: true,
            workCity: true,
            workCountry: true,
            workState: true,
            workZipCode: true,
            income: true
          });
        });
      }
    },
    get isValid() {
      const formikValid = formikRef.current?.isValid ?? false;
      console.log('[Step8 isValid] Real-time check:', {
        formikValid,
        formikValues: formikRef.current?.values,
        formikErrors: formikRef.current?.errors,
      });
      return formikValid;
    },
  }), [forceUpdate]);

  // Simplified validation logic - Step by step approach:
  // 1. Check basic required fields (occupation, education status)
  // 2. Check education info based on status (FIXED: handles student + no higher education)
  // 3. Check work info based on hasJob and occupation type (FIXED: students can have jobs)
  // 4. Check optional fields with N/A handling
  // 5. Check institution info for completed education only
  useEffect(() => {
    // Step 1: Basic required fields
    const hasOccupation = Boolean(formValues.occupation && formValues.occupation !== '');
    const hasEducationStatus = Boolean(formValues.educationStatus && formValues.educationStatus !== '');
    
    // Step 2: Education info validation (handle student edge case)
    const isStudentOccupation = formValues.occupation === 'student';
    const needsEducationLocation = formValues.educationStatus === 'completed_education' || 
                                  formValues.educationStatus === 'current_student' ||
                                  isStudentOccupation; // Students always need education location
    const hasEducationInfo = !needsEducationLocation || 
                           Boolean(formValues.educationLocation && formValues.educationLocation.trim() !== '') ||
                           Boolean(formValues.institutionName && formValues.institutionName.trim() !== '');
    
    // Step 3: Work info validation (simplified, but handle student case)
    const unemployedOccupations = UNEMPLOYED_OCCUPATIONS;
    // Students only need work info if they explicitly have a job
    const needsWorkInfo = formValues.hasJob && 
                         (!unemployedOccupations.includes(formValues.occupation || '') || 
                          (isStudentOccupation && formValues.hasJob));
    
    const hasRequiredWorkFields = !needsWorkInfo || (
      Boolean(formValues.companyName) &&
      Boolean(formValues.workAddress) &&
      Boolean(formValues.workExperience) &&
      Boolean(formValues.workPhone) &&
      Boolean(formValues.position)
    );
    
    // Step 4: Optional fields with N/A validation (simplified)
    const areOptionalFieldsValid = 
      (formValues.workStateNA || Boolean(formValues.workState) || !needsWorkInfo) &&
      (formValues.workZipCodeNA || Boolean(formValues.workZipCode) || !needsWorkInfo) &&
      (formValues.incomeNA || Boolean(formValues.income) || !needsWorkInfo);
    
    // Step 5: Institution fields (only for completed education)
    const needsInstitutionInfo = formValues.educationStatus === 'completed_education';
    const areInstitutionFieldsValid = !needsInstitutionInfo || (
      (formValues.institutionStateNA || Boolean(formValues.institutionState)) &&
      (formValues.institutionZipCodeNA || Boolean(formValues.institutionZipCode))
    );
    
    // Final validation (much simpler)
    const formValid = hasOccupation &&
                      hasEducationStatus &&
                      hasEducationInfo &&
                      hasRequiredWorkFields &&
                      areOptionalFieldsValid &&
                      areInstitutionFieldsValid;

    updateForm({ ...formValues });

  }, [formValues, updateForm]);

  // Additional effect to trigger validation when Formik validation changes
  useEffect(() => {
    if (formikRef.current) {
      // Trigger a manual validation to ensure Formik validation is up to date
      formikRef.current.validateForm();
    }
  }, [formValues]);

  // const isBusinessOccupation = (occupation: string) =>
  //   ['business', 'business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(occupation);
  //
  // const isRegisteredBusiness = (occupation: string) =>
  //   ['business', 'business_owner', 'individual_entrepreneur'].includes(occupation);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Образование и деятельность</h2>
      <p className="text-gray-600 mb-4">
        Пожалуйста, предоставьте информацию о вашем текущем занятии, образовании или работе.
      </p>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">Упрощенная логика заполнения</h4>
            <p className="text-sm text-blue-800">
              Теперь форма автоматически определяет, какие поля требуются. Поля с пометкой "Н/Д" можно оставить пустыми, если информация неприменима.
            </p>
          </div>
        </div>
      </div>

      <Formik
        innerRef={formikRef}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setFormValues(values);
          onSubmit(values);
        }}
      >
        {({ values, isValid: _isValid, setFieldValue }) => {
          // Update formValues whenever values object changes
          // No hooks here - just directly update if needed
          if (JSON.stringify(values) !== JSON.stringify(formValues)) {
            setFormValues(values);
          }

          // Принудительно обновляем useImperativeHandle при изменении Formik
          useEffect(() => {
            console.log('[Step8 Formik] State changed:', {
              formikIsValid: _isValid,
              values,
              errors: formikRef.current?.errors,
            });
            setForceUpdate(prev => prev + 1);
          }, [_isValid, values]);

          // Create custom field change handlers
          const updateFieldAndForm = (
            field: string, 
            value: string | boolean | Date | null | undefined, 
            additionalUpdates: Record<string, string | boolean | Date | null | undefined> = {}
          ) => {
            const updatedValues = { 
              ...values, 
              [field]: value,
              ...additionalUpdates
            } as Step8Data;
            
            setFieldValue(field, value);
            
            // Apply additional updates
            Object.entries(additionalUpdates).forEach(([key, val]) => {
              setFieldValue(key, val);
            });
            
            // Update form values state
            setFormValues(updatedValues);
            updateForm(updatedValues);
          };

          return (
          <Form>
            <div className="grid grid-cols-1 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg mb-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Информация об образовании</h3>
                
                <div className="mb-4">
                  <label htmlFor="educationStatus" className="block text-gray-700 font-medium mb-2">
                    Выберите ваш статус образования <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="educationStatus"
                    name="educationStatus"
                    className="form-input"
                    value={values.educationStatus}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      
                      // Generate additional updates
                      const additionalUpdates: Record<string, string | boolean> = {
                        isCurrentStudent: newValue === 'current_student'
                      };
                      
                      // If selecting "no_higher_education", clear higher education fields
                      if (newValue === 'no_higher_education') {
                        additionalUpdates.lastCompletedEducation = '';
                        additionalUpdates.otherEducation = '';
                        additionalUpdates.institutionName = '';
                        additionalUpdates.institutionAddress1 = '';
                        additionalUpdates.institutionAddress2 = '';
                        additionalUpdates.institutionCity = '';
                        additionalUpdates.institutionState = '';
                        additionalUpdates.institutionZipCode = '';
                        additionalUpdates.institutionCountry = '';
                        additionalUpdates.courseOfStudy = '';
                        additionalUpdates.attendanceFrom = '';
                        additionalUpdates.attendanceTo = '';
                      }
                      
                      // Update field and form
                      updateFieldAndForm('educationStatus', newValue, additionalUpdates);
                      
                      // Ensure form values are updated immediately
                      setFormValues(prev => ({
                        ...prev,
                        educationStatus: newValue,
                        isCurrentStudent: newValue === 'current_student',
                        ...additionalUpdates
                      }));
                    }}
                  >
                    <option value="">Выберите...</option>
                    <option value="no_higher_education">У меня нет высшего образования</option>
                    <option value="completed_education">Я закончил обучение</option>
                    <option value="current_student">Я сейчас студент</option>
                  </select>
                  <ErrorMessage
                    name="educationStatus"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                {values.educationStatus === 'no_higher_education' && (
                  <div className="mb-4">
                    <label htmlFor="lastCompletedEducation" className="block text-gray-700 font-medium mb-2">
                      Какое образование вы получили?
                    </label>
                    <select
                      id="lastCompletedEducation"
                      name="lastCompletedEducation"
                      className="form-input"
                      value={values.lastCompletedEducation}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        updateFieldAndForm('lastCompletedEducation', newValue);
                      }}
                    >
                      {educationLevelOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {values.lastCompletedEducation === 'other' && (
                      <div className="mt-2">
                        <input
                          type="text"
                          id="otherEducation"
                          name="otherEducation"
                          className="form-input"
                          placeholder="Укажите ваше образование"
                          value={values.otherEducation || ''}
                          onChange={(e) => {
                            const newValue = e.target.value;
                            updateFieldAndForm('otherEducation', newValue);
                          }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Education location field for students without higher education */}
                {(values.occupation === 'student' && values.educationStatus === 'no_higher_education') && (
                  <div className="mb-4">
                    <label htmlFor="educationLocation" className="block text-gray-700 font-medium mb-2">
                      Где вы сейчас учитесь? <span className="text-red-500">*</span>
                    </label>
                    <div className="mb-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <p className="text-blue-700 text-sm">
                        <strong>Важно:</strong> Поскольку ваша профессия "Студент", укажите название учебного заведения, где вы сейчас обучаетесь (школа, колледж, техникум и т.д.).
                      </p>
                    </div>
                    <Field
                      type="text"
                      id="educationLocation"
                      name="educationLocation"
                      className="form-input"
                      placeholder="Например: Алматинский колледж экономики"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const newValue = e.target.value;
                        updateFieldAndForm('educationLocation', newValue);
                      }}
                    />
                    <ErrorMessage
                      name="educationLocation"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}

                {(values.educationStatus === 'completed_education' || values.educationStatus === 'current_student') && (
                  <div className="space-y-4">
                    <div className="mb-4">
                      <label htmlFor="institutionName" className="block text-gray-700 font-medium mb-2">
                        Название учебного заведения
                      </label>
                      <Field
                        type="text"
                        id="institutionName"
                        name="institutionName"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="institutionName"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="institutionAddress1" className="block text-gray-700 font-medium mb-2">
                        Адрес учебного заведения (строка 1)
                      </label>
                      <Field
                        type="text"
                        id="institutionAddress1"
                        name="institutionAddress1"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="institutionAddress1"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="institutionAddress2" className="block text-gray-700 font-medium mb-2">
                        Адрес учебного заведения (строка 2) <span className="text-gray-500 italic">*Необязательно</span>
                      </label>
                      <Field
                        type="text"
                        id="institutionAddress2"
                        name="institutionAddress2"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="institutionAddress2"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="mb-4">
                        <label htmlFor="institutionCity" className="block text-gray-700 font-medium mb-2">
                          Город
                        </label>
                        <Field
                          type="text"
                          id="institutionCity"
                          name="institutionCity"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="institutionCity"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="institutionState" className="block text-gray-700 font-medium mb-2">
                          Область/Регион
                        </label>
                        <div className="flex items-center">
                          <Field
                            type="text"
                            id="institutionState"
                            name="institutionState"
                            className="form-input flex-grow"
                            disabled={values.institutionStateNA}
                          />
                          <div className="ml-2 flex items-center">
                            <input
                              type="checkbox"
                              id="institutionStateNA"
                              name="institutionStateNA"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              checked={values.institutionStateNA || false}
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                // When checkbox is checked, clear the field
                                if (isChecked) {
                                  updateFieldAndForm('institutionState', '', { institutionStateNA: isChecked });
                                } else {
                                  updateFieldAndForm('institutionStateNA', isChecked);
                                }
                              }}
                            />
                            <label htmlFor="institutionStateNA" className="ml-2 block text-gray-700 text-sm">
                              Не применимо
                            </label>
                          </div>
                        </div>
                        <ErrorMessage
                          name="institutionState"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="mb-4">
                        <label htmlFor="institutionZipCode" className="block text-gray-700 font-medium mb-2">
                          Почтовый индекс
                        </label>
                        <div className="flex items-center">
                          <Field
                            type="text"
                            id="institutionZipCode"
                            name="institutionZipCode"
                            className="form-input flex-grow"
                            disabled={values.institutionZipCodeNA}
                          />
                          <div className="ml-2 flex items-center">
                            <input
                              type="checkbox"
                              id="institutionZipCodeNA"
                              name="institutionZipCodeNA"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              checked={values.institutionZipCodeNA || false}
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                // When checkbox is checked, clear the field
                                if (isChecked) {
                                  updateFieldAndForm('institutionZipCode', '', { institutionZipCodeNA: isChecked });
                                } else {
                                  updateFieldAndForm('institutionZipCodeNA', isChecked);
                                }
                              }}
                            />
                            <label htmlFor="institutionZipCodeNA" className="ml-2 block text-gray-700 text-sm">
                              Не применимо
                            </label>
                          </div>
                        </div>
                        <ErrorMessage
                          name="institutionZipCode"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="institutionCountry" className="block text-gray-700 font-medium mb-2">
                          Страна/Регион
                        </label>
                        <Field
                          as="select"
                          id="institutionCountry"
                          name="institutionCountry"
                          className="form-input"
                        >
                          <option value="">Выберите страну...</option>
                          {COUNTRIES.map((country) => (
                            <option key={country} value={country}>
                              {country}
                            </option>
                          ))}
                        </Field>
                        <ErrorMessage
                          name="institutionCountry"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="courseOfStudy" className="block text-gray-700 font-medium mb-2">
                        Направление обучения
                      </label>
                      <Field
                        type="text"
                        id="courseOfStudy"
                        name="courseOfStudy"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="courseOfStudy"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="mb-4">
                        <label htmlFor="attendanceFrom" className="block text-gray-700 font-medium mb-2">
                          Дата начала обучения
                        </label>
                        <Field
                          type="date"
                          id="attendanceFrom"
                          name="attendanceFrom"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="attendanceFrom"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="attendanceTo" className="block text-gray-700 font-medium mb-2">
                          Дата окончания обучения
                        </label>
                        <Field
                          type="date"
                          id="attendanceTo"
                          name="attendanceTo"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="attendanceTo"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="mb-4">
                <label htmlFor="occupation" className="block text-gray-700 font-medium mb-2">
                  Основной род занятий <span className="text-red-500">*</span>
                </label>
                <select
                  id="occupation"
                  name="occupation"
                  className="form-input"
                  value={values.occupation}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    
                    // Auto set job flag based on occupation
                    const isStudent = newValue === 'student';
                    const isUnemployed = UNEMPLOYED_OCCUPATIONS.includes(newValue);
                    const isEmployed = !isUnemployed && newValue !== 'student';
                    
                    // Create additional updates
                    const additionalUpdates: Record<string, string | boolean> = {
                      hasJob: isEmployed
                    };
                    
                    // If student is selected, set isCurrentStudent to true
                    if (isStudent) {
                      additionalUpdates.isCurrentStudent = true;
                      additionalUpdates.educationStatus = 'current_student';
                    }
                    
                    // If current student selects an employed occupation, set hasStudentJob to true
                    if (values.educationStatus === 'current_student' && isEmployed) {
                      additionalUpdates.hasStudentJob = true;
                    }
                    
                    // Update field and form
                    updateFieldAndForm('occupation', newValue, additionalUpdates);
                  }}
                >
                  {occupationOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <ErrorMessage
                  name="occupation"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              {/* Employment Information Section */}
              {((values.hasJob && values.occupation !== 'student') || 
               (values.educationStatus === 'current_student' && 
                ![...UNEMPLOYED_OCCUPATIONS, 'student'].includes(values.occupation || '') && 
                values.occupation !== '')) && (
                <div className="p-4 border border-gray-200 rounded-lg mb-4">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">
                    {['business', 'business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(values.occupation || '') 
                      ? 'Информация о вашей деятельности' 
                      : 'Информация о работе'}
                  </h3>
                  {values.educationStatus === 'current_student' && 
                   ![...UNEMPLOYED_OCCUPATIONS, 'student'].includes(values.occupation || '') && 
                   values.occupation !== '' && (
                    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-yellow-700 text-sm">
                        <strong>Внимание:</strong> Поскольку вы выбрали статус студента и указали профессиональный род занятий, 
                        все поля информации о работе обязательны для заполнения. Вы не сможете перейти к следующему шагу, 
                        пока не заполните эти поля.
                      </p>
                    </div>
                  )}

                  <div className="mb-4">
                    <label htmlFor="companyName" className="block text-gray-700 font-medium mb-2">
                      Название компании/организации <span className="text-red-500">*</span>
                    </label>
                    <Field
                      type="text"
                      id="companyName"
                      name="companyName"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="companyName"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="workAddress" className="block text-gray-700 font-medium mb-2">
                      Адрес работы (строка 1) <span className="text-red-500">*</span>
                    </label>
                    <Field
                      type="text"
                      id="workAddress"
                      name="workAddress"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="workAddress"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="workAddress2" className="block text-gray-700 font-medium mb-2">
                      Адрес работы (строка 2) <span className="text-gray-500 italic">*Необязательно</span>
                    </label>
                    <Field
                      type="text"
                      id="workAddress2"
                      name="workAddress2"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="workAddress2"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="mb-4">
                      <label htmlFor="workCity" className="block text-gray-700 font-medium mb-2">
                        Город
                      </label>
                      <Field
                        type="text"
                        id="workCity"
                        name="workCity"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="workCity"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="workState" className="block text-gray-700 font-medium mb-2">
                        Область/Регион <span className="text-red-500">*</span>
                      </label>
                      <div className="flex items-center">
                        <Field
                          type="text"
                          id="workState"
                          name="workState"
                          className="form-input flex-grow"
                          disabled={values.workStateNA}
                        />
                        <div className="ml-2 flex items-center">
                          <input
                            type="checkbox"
                            id="workStateNA"
                            name="workStateNA"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={values.workStateNA || false}
                            onChange={(e) => {
                              const isChecked = e.target.checked;
                              // When checkbox is checked, clear the field
                              if (isChecked) {
                                updateFieldAndForm('workState', '', { workStateNA: isChecked });
                              } else {
                                updateFieldAndForm('workStateNA', isChecked);
                              }
                            }}
                          />
                          <label htmlFor="workStateNA" className="ml-2 block text-gray-700 text-sm">
                            Не применимо
                          </label>
                        </div>
                      </div>
                      <ErrorMessage
                        name="workState"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="mb-4">
                      <label htmlFor="workZipCode" className="block text-gray-700 font-medium mb-2">
                        Почтовый индекс <span className="text-red-500">*</span>
                      </label>
                      <div className="flex items-center">
                        <Field
                          type="text"
                          id="workZipCode"
                          name="workZipCode"
                          className="form-input flex-grow"
                          disabled={values.workZipCodeNA}
                        />
                        <div className="ml-2 flex items-center">
                          <input
                            type="checkbox"
                            id="workZipCodeNA"
                            name="workZipCodeNA"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={values.workZipCodeNA || false}
                            onChange={(e) => {
                              const isChecked = e.target.checked;
                              // When checkbox is checked, clear the field
                              if (isChecked) {
                                updateFieldAndForm('workZipCode', '', { workZipCodeNA: isChecked });
                              } else {
                                updateFieldAndForm('workZipCodeNA', isChecked);
                              }
                            }}
                          />
                          <label htmlFor="workZipCodeNA" className="ml-2 block text-gray-700 text-sm">
                            Не применимо
                          </label>
                        </div>
                      </div>
                      <ErrorMessage
                        name="workZipCode"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="workCountry" className="block text-gray-700 font-medium mb-2">
                        Страна/Регион
                      </label>
                      <Field
                        as="select"
                        id="workCountry"
                        name="workCountry"
                        className="form-input"
                      >
                        <option value="">Выберите страну...</option>
                        {COUNTRIES.map((country) => (
                          <option key={country} value={country}>
                            {country}
                          </option>
                        ))}
                      </Field>
                      <ErrorMessage
                        name="workCountry"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="workPhone" className="block text-gray-700 font-medium mb-2">
                      Телефон работы <span className="text-red-500">*</span>
                    </label>
                    <Field
                      type="text"
                      id="workPhone"
                      name="workPhone"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="workPhone"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="position" className="block text-gray-700 font-medium mb-2">
                      Должность <span className="text-red-500">*</span>
                    </label>
                    <Field
                      type="text"
                      id="position"
                      name="position"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="position"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="workExperience" className="block text-gray-700 font-medium mb-2">
                      Как давно работаете <span className="text-red-500">*</span>
                    </label>
                    <Field
                      type="text"
                      id="workExperience"
                      name="workExperience"
                      className="form-input"
                      placeholder="Например: 3 года"
                    />
                    <ErrorMessage
                      name="workExperience"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="income" className="block text-gray-700 font-medium mb-2">
                      Месячный доход (в тенге) <span className="text-red-500">*</span>
                    </label>
                    <div className="flex items-center">
                      <Field
                        type="number"
                        min="0"
                        id="income"
                        name="income"
                        className="form-input flex-grow"
                        placeholder="Например: 300000"
                        disabled={values.incomeNA}
                      />
                      <div className="ml-2 flex items-center">
                        <input
                          type="checkbox"
                          id="incomeNA"
                          name="incomeNA"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={values.incomeNA || false}
                          onChange={(e) => {
                            const isChecked = e.target.checked;
                            // When checkbox is checked, clear the field
                            if (isChecked) {
                              updateFieldAndForm('income', '', { incomeNA: isChecked });
                            } else {
                              updateFieldAndForm('incomeNA', isChecked);
                            }
                          }}
                        />
                        <label htmlFor="incomeNA" className="ml-2 block text-gray-700 text-sm">
                          Не применимо
                        </label>
                      </div>
                    </div>
                    <ErrorMessage
                      name="income"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="duties" className="block text-gray-700 font-medium mb-2">
                      Кратко опишите ваши обязанности
                    </label>
                    <Field
                      as="textarea"
                      id="duties"
                      name="duties"
                      className="form-input"
                      rows="3"
                    />
                    <ErrorMessage
                      name="duties"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>
              )}

              {/* Student Additional Info */}
              {values.educationStatus === 'current_student' && (
                <div className="p-4 border border-gray-200 rounded-lg mb-4">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">Дополнительная информация для студентов</h3>
                  
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <Field
                        type="checkbox"
                        id="hasStudentJob"
                        name="hasStudentJob"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        checked={values.hasStudentJob || 
                          (![...UNEMPLOYED_OCCUPATIONS, 'student'].includes(values.occupation || '') && 
                          values.occupation !== '')}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          const isChecked = e.target.checked;
                          updateFieldAndForm('hasStudentJob', isChecked);
                        }}
                        disabled={![...UNEMPLOYED_OCCUPATIONS, 'student'].includes(values.occupation || '') && 
                          values.occupation !== ''}
                      />
                      <label htmlFor="hasStudentJob" className="ml-2 block text-gray-700 font-medium">
                        У меня есть работа помимо учебы
                        {![...UNEMPLOYED_OCCUPATIONS, 'student'].includes(values.occupation || '') && 
                          values.occupation !== '' && (
                          <span className="ml-2 text-yellow-600 text-sm">(Автоматически выбрано на основе указанного рода занятий)</span>
                        )}
                      </label>
                    </div>
                    {![...UNEMPLOYED_OCCUPATIONS, 'student'].includes(values.occupation || '') && 
                      values.occupation !== '' && (
                      <p className="text-sm text-gray-600 mt-1 ml-6">
                        Поскольку вы указали профессиональный род занятий, вам необходимо заполнить информацию о работе выше.
                      </p>
                    )}
                  </div>

                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <Field
                        type="checkbox"
                        id="hasStateGrant"
                        name="hasStateGrant"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <label htmlFor="hasStateGrant" className="ml-2 block text-gray-700 font-medium">
                        Я получаю государственную стипендию/грант
                      </label>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="additionalStudentInfo" className="block text-gray-700 font-medium mb-2">
                      Дополнительная информация
                    </label>
                    <Field
                      as="textarea"
                      id="additionalStudentInfo"
                      name="additionalStudentInfo"
                      className="form-input"
                      rows="3"
                      placeholder="Укажите любую дополнительную информацию о вашем финансовом положении"
                    />
                  </div>
                </div>
              )}

            </div>

            {/* Hidden submit button - will be triggered by StepWrapper */}
            <button type="submit" style={{ display: 'none' }} />
          </Form>
          );
        }}
      </Formik>
    </div>
  );
});

Step8_EducationWork.displayName = 'Step8_EducationWork';

export default Step8_EducationWork;