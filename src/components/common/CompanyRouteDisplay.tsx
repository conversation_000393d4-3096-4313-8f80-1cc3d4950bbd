import React, { useState } from 'react';

interface CompanyRouteDisplayProps {
  slug: string;
  className?: string;
}

const CompanyRouteDisplay: React.FC<CompanyRouteDisplayProps> = ({ slug, className = '' }) => {
  const [copied, setCopied] = useState(false);
  
  // Use window.location.origin instead of hardcoded localhost:3000
  const fullUrl = typeof window !== 'undefined' ? `${window.location.origin}/${slug}` : `/${slug}`;
  
  const handleCopy = () => {
    navigator.clipboard.writeText(fullUrl);
    setCopied(true);
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };
  
  return (
    <div className={`flex items-center ${className}`}>
      <div className="flex items-center bg-blue-50 border border-blue-200 rounded-lg px-3 py-1.5">
        <span className="text-xs font-mono text-blue-600 truncate max-w-[300px]">
          {fullUrl}
        </span>
        <button
          onClick={handleCopy}
          className="ml-2 p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors duration-200"
          title="Копировать ссылку"
        >
          {copied ? (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          )}
        </button>
      </div>
    </div>
  );
};

export default CompanyRouteDisplay; 