import { useState, useEffect } from 'react';

interface PriceListItem {
  id: string;
  company_id: string;
  country: string;
  title: string;
  price: number;
  duration?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

interface Company {
  id: string;
  name: string;
  phone_number?: string;
  email?: string;
}

interface PriceListDisplayProps {
  selectedCountry?: string;
  onPackageSelect?: (packageId: string, packageInfo: PriceListItem) => void;
  className?: string;
}

export default function PriceListDisplay({ 
  selectedCountry, 
  onPackageSelect, 
  className = '' 
}: PriceListDisplayProps) {
  const [priceList, setPriceList] = useState<PriceListItem[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch price list and companies in parallel
      const [priceResponse, companiesResponse] = await Promise.all([
        fetch('/api/admin/price-list'),
        fetch('/api/admin/companies')
      ]);

      const [priceData, companiesData] = await Promise.all([
        priceResponse.json(),
        companiesResponse.json()
      ]);

      if (priceResponse.ok) {
        setPriceList(priceData.packages || []);
      } else {
        setError('Не удалось загрузить прайс-лист');
      }

      if (companiesResponse.ok) {
        setCompanies(companiesData.companies || []);
      }
    } catch (err) {
      setError('Произошла ошибка при загрузке данных');
      console.error('Error fetching price list:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter by country if specified
  const filteredPriceList = selectedCountry 
    ? priceList.filter(item => item.country === selectedCountry)
    : priceList;

  // Group by company and country
  const groupedData = filteredPriceList.reduce((acc, item) => {
    const company = companies.find(c => c.id === item.company_id);
    const companyName = company ? company.name : 'Неизвестная компания';
    
    if (!acc[companyName]) {
      acc[companyName] = {};
    }
    if (!acc[companyName][item.country]) {
      acc[companyName][item.country] = [];
    }
    acc[companyName][item.country].push(item);
    return acc;
  }, {} as Record<string, Record<string, PriceListItem[]>>);

  if (loading) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
        <p className="mt-2 text-gray-500">Загрузка прайс-листа...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="text-red-600 mb-2">⚠️ Ошибка</div>
        <p className="text-gray-500">{error}</p>
        <button 
          onClick={fetchData}
          className="mt-2 text-blue-600 hover:text-blue-800 underline"
        >
          Попробовать снова
        </button>
      </div>
    );
  }

  if (filteredPriceList.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="text-gray-400 mb-2">📋</div>
        <p className="text-gray-500">
          {selectedCountry 
            ? `Нет доступных пакетов для ${selectedCountry}`
            : 'Прайс-лист пуст'
          }
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {Object.entries(groupedData).map(([companyName, countries]) => (
        <div key={companyName} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">{companyName}</h3>
          </div>
          
          <div className="p-4">
            {Object.entries(countries).map(([country, packages]) => (
              <div key={country} className="mb-6 last:mb-0">
                <h4 className="text-md font-medium text-gray-800 mb-3 flex items-center">
                  <span className="mr-2">{country}</span>
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                    {packages.length} пакет{packages.length === 1 ? '' : packages.length < 5 ? 'а' : 'ов'}
                  </span>
                </h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {packages.map((pkg) => (
                    <div 
                      key={pkg.id}
                      className={`border border-gray-200 rounded-lg p-4 transition-all ${
                        onPackageSelect 
                          ? 'hover:shadow-md hover:border-blue-300 cursor-pointer group' 
                          : 'hover:shadow-sm'
                      }`}
                      onClick={() => onPackageSelect && onPackageSelect(pkg.id, pkg)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h5 className={`font-semibold text-gray-900 text-sm ${
                          onPackageSelect ? 'group-hover:text-blue-600' : ''
                        }`}>
                          {pkg.title}
                        </h5>
                        <div className="text-right">
                          <div className="text-lg font-bold text-green-600">
                            {pkg.price.toLocaleString()} теңге
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-1 text-sm text-gray-600">
                        {pkg.duration && (
                          <div className="flex justify-between">
                            <span>Срок:</span>
                            <span className="font-medium">{pkg.duration}</span>
                          </div>
                        )}
                        {pkg.description && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 line-clamp-2">
                              {pkg.description}
                            </p>
                          </div>
                        )}
                      </div>
                      
                      {onPackageSelect && (
                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="text-xs text-blue-600 group-hover:text-blue-800 font-medium">
                            Нажмите для выбора →
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
} 