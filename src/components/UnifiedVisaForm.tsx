import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react';
import { Formik, Form, FormikProps } from 'formik';
import { VisaFormData } from '../utils/types';
import { masterValidationSchema } from '../utils/validations';

// Step renderers
import Step1Fields from './steps/Step1Fields';
import Step2Fields from './steps/Step2Fields';
import Step3Fields from './steps/Step3Fields';
import Step4Fields from './steps/Step4Fields';
import Step5Fields from './steps/Step5Fields';
import Step6Fields from './steps/Step6Fields';
import Step7Fields from './steps/Step7Fields';
import Step8Fields from './steps/Step8Fields';
import Step9Fields from './steps/Step9Fields';

export interface UnifiedVisaFormRef {
  submitForm: () => void;
  isValid: boolean;
  getFormData: () => VisaFormData;
  setFormData: (data: Partial<VisaFormData>) => void;
}

interface UnifiedVisaFormProps {
  initialValues: VisaFormData;
  currentStep: number;
  onSubmit: (values: VisaFormData) => void;
  onFormDataUpdate?: (data: Partial<VisaFormData>) => void;
  uploadedFiles?: Record<string, string>;
  setUploadedFiles?: (files: Record<string, string>) => void;
  maritalStatus?: string;
  updateForm?: (values: any) => void;
}

const UnifiedVisaForm = forwardRef<UnifiedVisaFormRef, UnifiedVisaFormProps>(({
  initialValues,
  currentStep,
  onSubmit,
  onFormDataUpdate,
  uploadedFiles = {},
  setUploadedFiles,
  maritalStatus,
  updateForm
}, ref) => {
  const formikRef = useRef<FormikProps<VisaFormData>>(null);
  const [isFormValid, setIsFormValid] = useState(false);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      console.log('[UnifiedForm] submitForm called for step:', currentStep);
      if (formikRef.current) {
        formikRef.current.submitForm();
      }
    },
    isValid: isFormValid,
    getFormData: () => {
      return formikRef.current?.values || initialValues;
    },
    setFormData: (data: Partial<VisaFormData>) => {
      if (formikRef.current) {
        Object.keys(data).forEach(key => {
          formikRef.current?.setFieldValue(key, data[key as keyof VisaFormData]);
        });
      }
    }
  }), [isFormValid, currentStep]);

  // Update form validity when Formik state changes
  useEffect(() => {
    if (formikRef.current) {
      const formikValid = formikRef.current.isValid;
      setIsFormValid(formikValid);
      
      console.log('[UnifiedForm] Validity changed:', {
        step: currentStep,
        isValid: formikValid,
        errors: formikRef.current.errors
      });
    }
  }, [currentStep]);

  const renderCurrentStepFields = (formikProps: FormikProps<VisaFormData>) => {
    const commonProps = {
      values: formikProps.values,
      setFieldValue: formikProps.setFieldValue,
      errors: formikProps.errors,
      touched: formikProps.touched
    };

    switch (currentStep) {
      case 1:
        return <Step1Fields {...commonProps} />;
      case 2:
        return (
          <Step2Fields 
            {...commonProps}
            uploadedFiles={uploadedFiles}
            setUploadedFiles={setUploadedFiles}
            onFormDataUpdate={onFormDataUpdate}
          />
        );
      case 3:
        return <Step3Fields {...commonProps} />;
      case 4:
        return <Step4Fields {...commonProps} />;
      case 5:
        return <Step5Fields {...commonProps} />;
      case 6:
        return <Step6Fields {...commonProps} />;
      case 7:
        return <Step7Fields {...commonProps} maritalStatus={maritalStatus} />;
      case 8:
        return <Step8Fields {...commonProps} updateForm={updateForm} />;
      case 9:
        return <Step9Fields {...commonProps} />;
      default:
        return null;
    }
  };

  return (
    <Formik
      innerRef={formikRef}
      initialValues={initialValues}
      validationSchema={masterValidationSchema}
      onSubmit={onSubmit}
      validateOnChange={true}
      validateOnBlur={true}
      validateOnMount={false}
      enableReinitialize={true}
    >
      {(formikProps) => {
        // Update validity when formik state changes
        useEffect(() => {
          setIsFormValid(formikProps.isValid);
          
          // Call onFormDataUpdate if provided
          if (onFormDataUpdate) {
            onFormDataUpdate(formikProps.values);
          }
        }, [formikProps.isValid, formikProps.values]);

        return (
          <Form>
            {renderCurrentStepFields(formikProps)}
            {/* Hidden submit button - will be triggered by StepWrapper */}
            <button type="submit" style={{ display: 'none' }} />
          </Form>
        );
      }}
    </Formik>
  );
});

UnifiedVisaForm.displayName = 'UnifiedVisaForm';

export default UnifiedVisaForm;
