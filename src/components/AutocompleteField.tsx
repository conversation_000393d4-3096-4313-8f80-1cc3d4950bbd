import React, { useEffect, useRef } from 'react';
import { Field, useFormikContext } from 'formik';

interface AutocompleteFieldProps {
  name: string;
  type?: string;
  placeholder?: string;
  className?: string;
  id?: string;
  as?: any;
  children?: React.ReactNode;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  [key: string]: any;
}

/**
 * Custom Field component that detects browser autocomplete and triggers validation
 */
export const AutocompleteField: React.FC<AutocompleteFieldProps> = ({
  name,
  type = 'text',
  onChange,
  ...props
}) => {
  const { setFieldValue, setFieldTouched, validateField } = useFormikContext();
  const fieldRef = useRef<HTMLInputElement>(null);
  const lastValueRef = useRef<string>('');

  useEffect(() => {
    const checkAutocomplete = () => {
      if (!fieldRef.current) return;

      const currentValue = fieldRef.current.value;
      
      // If value changed without our knowledge (autocomplete)
      if (currentValue !== lastValueRef.current && currentValue) {
        console.log(`Autocomplete detected for field ${name}: "${currentValue}"`);
        
        // Update Formik's state
        setFieldValue(name, currentValue);
        setFieldTouched(name, true);
        
        // Trigger validation after a short delay
        setTimeout(() => {
          validateField(name);
        }, 100);
        
        lastValueRef.current = currentValue;
      }
    };

    // Check on mount and periodically
    const timeouts = [0, 100, 300, 500, 1000, 2000].map(delay => 
      setTimeout(checkAutocomplete, delay)
    );

    // Also listen for animation events which indicate autocomplete
    const handleAnimationStart = (e: AnimationEvent) => {
      if (e.animationName === 'onAutoFillStart') {
        setTimeout(checkAutocomplete, 50);
      }
    };

    const field = fieldRef.current;
    if (field) {
      field.addEventListener('animationstart', handleAnimationStart as any);
      
      // Listen for input events
      const handleInput = () => setTimeout(checkAutocomplete, 50);
      field.addEventListener('input', handleInput);
      
      return () => {
        timeouts.forEach(clearTimeout);
        field.removeEventListener('animationstart', handleAnimationStart as any);
        field.removeEventListener('input', handleInput);
      };
    }

    return () => {
      timeouts.forEach(clearTimeout);
    };
  }, [name, setFieldValue, setFieldTouched, validateField]);

  return (
    <>
      <style jsx>{`
        input:-webkit-autofill {
          animation-name: onAutoFillStart;
        }
        
        @keyframes onAutoFillStart {
          from { opacity: 1; }
          to { opacity: 1; }
        }
      `}</style>
      <Field
        innerRef={fieldRef}
        name={name}
        type={type}
        {...props}
        onChange={onChange ? (e: React.ChangeEvent<HTMLInputElement>) => {
          // Custom onChange provided - call it and update our ref
          onChange(e);
          lastValueRef.current = e.target.value;
        } : undefined}
      />
    </>
  );
};