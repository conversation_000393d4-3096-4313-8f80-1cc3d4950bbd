import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Formik, Form, Field, ErrorMessage, FieldArray, FormikProps } from 'formik';
import { getStepValidation } from '../utils/validationHelpers';
import { Step7Data } from '../utils/types';
import { COUNTRIES } from '../constants/countries';

interface Step7Props {
  initialValues: Step7Data;
  onSubmit: (values: Step7Data) => void;
  maritalStatus?: string;
}

export interface Step7Ref {
  submitForm: () => void;
  isValid: boolean;
}

const Step7_FamilyInfo = forwardRef<Step7Ref, Step7Props>(({ initialValues, onSubmit, maritalStatus }, ref) => {
  const formikRef = useRef<FormikProps<Step7Data>>(null);
  const validationSchema = getStepValidation(7);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      formikRef.current?.submitForm();
    },
    isValid: formikRef.current?.isValid ?? false,
  }));



  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Информация о семье</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, предоставьте информацию о вашей семье и родственниках в США.
      </p>

      <Formik
        innerRef={formikRef}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        validateOnChange={true}
        validateOnBlur={true}
      >
        {({ values, isValid: _isValid, setFieldValue }) => {
          // Note: For users who selected "Незамужем/холост" in Step 3,
          // we still want to allow them to provide spouse information
          
          return (
          <Form>
            <div className="grid grid-cols-1 gap-4">

              {/* Информация о супруге */}
              <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Информация о супруге</h3>
                
                {/* Показываем поля супруга только если семейное положение подходящее */}
                {(maritalStatus === 'married' || maritalStatus === 'divorced' || maritalStatus === 'widowed') && (
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <Field name="hasSpouse">
                        {() => (
                          <input
                            type="checkbox"
                            id="hasSpouse"
                            checked={values.hasSpouse || false}
                            onChange={(e) => {
                              setFieldValue('hasSpouse', e.target.checked);
                            }}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          />
                        )}
                      </Field>
                      <label htmlFor="hasSpouse" className="ml-2 block text-gray-700 font-medium">
                        У вас есть/была супруг(а)?
                      </label>
                    </div>
                    <div className="text-sm text-gray-500 italic">
                      {maritalStatus === 'married' && 'Укажите информацию о текущем супруге'}
                      {maritalStatus === 'divorced' && 'Укажите информацию о бывшем супруге, если необходимо'}
                      {maritalStatus === 'widowed' && 'Укажите информацию о покойном супруге, если необходимо'}
                    </div>
                  </div>
                )}
                
                {/* Если статус "single", показываем информационное сообщение */}
                {maritalStatus === 'single' && (
                  <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-blue-700 text-sm">
                      <strong>Информация:</strong> Поскольку вы указали статус &quot;Холост/Не замужем&quot;, 
                      поля о супруге не отображаются. Если у вас был супруг ранее, пожалуйста, 
                      вернитесь к шагу 3 и измените семейное положение.
                    </p>
                  </div>
                )}

                {values.hasSpouse && (maritalStatus === 'married' || maritalStatus === 'divorced' || maritalStatus === 'widowed') && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="mb-4">
                        <label htmlFor="spouseLastName" className="block text-gray-700 font-medium mb-2">
                          Фамилия
                        </label>
                        <Field
                          type="text"
                          id="spouseLastName"
                          name="spouseLastName"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="spouseLastName"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="spouseFirstName" className="block text-gray-700 font-medium mb-2">
                          Имя
                        </label>
                        <Field
                          type="text"
                          id="spouseFirstName"
                          name="spouseFirstName"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="spouseFirstName"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="spouseMiddleName" className="block text-gray-700 font-medium mb-2">
                          Отчество
                        </label>
                        <Field
                          type="text"
                          id="spouseMiddleName"
                          name="spouseMiddleName"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="spouseMiddleName"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="mb-4">
                        <label htmlFor="spouseCityOfBirth" className="block text-gray-700 font-medium mb-2">
                          Город рождения
                        </label>
                        <Field
                          type="text"
                          id="spouseCityOfBirth"
                          name="spouseCityOfBirth"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="spouseCityOfBirth"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="spouseCountryOfBirth" className="block text-gray-700 font-medium mb-2">
                          Страна рождения
                        </label>
                        <Field
                          as="select"
                          id="spouseCountryOfBirth"
                          name="spouseCountryOfBirth"
                          className="form-input"
                        >
                          <option value="">Выберите страну...</option>
                          {COUNTRIES.map((country) => (
                            <option key={country} value={country}>
                              {country}
                            </option>
                          ))}
                        </Field>
                        <ErrorMessage
                          name="spouseCountryOfBirth"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="spouseDateOfBirth" className="block text-gray-700 font-medium mb-2">
                        Дата рождения супруга(и)
                      </label>
                      <Field
                        type="date"
                        id="spouseDateOfBirth"
                        name="spouseDateOfBirth"
                        className="form-input"
                        min={(new Date().getFullYear() - 120) + "-01-01"}
                        max={(new Date().getFullYear() - 18) + "-12-31"}
                      />
                      <ErrorMessage
                        name="spouseDateOfBirth"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="spouseCitizenship" className="block text-gray-700 font-medium mb-2">
                        Гражданство супруга(и)
                      </label>
                      <Field
                        as="select"
                        id="spouseCitizenship"
                        name="spouseCitizenship"
                        className="form-input"
                      >
                        <option value="">Выберите страну...</option>
                        {COUNTRIES.map((country) => (
                          <option key={country} value={country}>
                            {country}
                          </option>
                        ))}
                      </Field>
                      <ErrorMessage
                        name="spouseCitizenship"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    <div className="mb-4">
                      <div className="flex items-center mb-2">
                        <Field name="wasSpouseInUSA">
                          {() => (
                            <input
                              type="checkbox"
                              id="wasSpouseInUSA"
                              checked={values.wasSpouseInUSA || false}
                              onChange={(e) => {
                                setFieldValue('wasSpouseInUSA', e.target.checked);
                              }}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            />
                          )}
                        </Field>
                        <label htmlFor="wasSpouseInUSA" className="ml-2 block text-gray-700 font-medium">
                          Был(а) ли супруг(а) в США ранее?
                        </label>
                      </div>
                    </div>

                    {values.wasSpouseInUSA && (
                      <div className="p-4 border border-gray-200 rounded-md">
                        <div className="mb-4">
                          <label htmlFor="spouseUSAEntryDate" className="block text-gray-700 font-medium mb-2">
                            Дата прибытия (ДД-МММ-ГГГГ)
                          </label>
                          <Field
                            type="date"
                            id="spouseUSAEntryDate"
                            name="spouseUSAEntryDate"
                            className="form-input"
                            min="1950-01-01"
                            max={new Date().toISOString().split('T')[0]}
                          />
                          <ErrorMessage
                            name="spouseUSAEntryDate"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="mb-4">
                            <label htmlFor="spouseUSAStayDuration" className="block text-gray-700 font-medium mb-2">
                              Продолжительность пребывания
                            </label>
                            <Field
                              type="text"
                              id="spouseUSAStayDuration"
                              name="spouseUSAStayDuration"
                              className="form-input"
                            />
                            <ErrorMessage
                              name="spouseUSAStayDuration"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          </div>

                          <div className="mb-4">
                            <label htmlFor="spouseUSAStayDurationType" className="block text-gray-700 font-medium mb-2">
                              Единица измерения
                            </label>
                            <Field
                              as="select"
                              id="spouseUSAStayDurationType"
                              name="spouseUSAStayDurationType"
                              className="form-input"
                            >
                              <option value="">Выберите...</option>
                              <option value="years">Лет</option>
                              <option value="months">Месяцев</option>
                              <option value="weeks">Недель</option>
                              <option value="days">Дней</option>
                              <option value="less_than_24h">Менее 24 часов</option>
                            </Field>
                            <ErrorMessage
                              name="spouseUSAStayDurationType"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Информация об отце */}
              <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Информация об отце</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="mb-4">
                    <label htmlFor="fatherSurname" className="block text-gray-700 font-medium mb-2">
                      Фамилия
                    </label>
                    <Field
                      type="text"
                      id="fatherSurname"
                      name="fatherSurname"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="fatherSurname"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="fatherName" className="block text-gray-700 font-medium mb-2">
                      Имя
                    </label>
                    <Field
                      type="text"
                      id="fatherName"
                      name="fatherName"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="fatherName"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field name="isFatherDateOfBirthUnknown">
                      {() => (
                        <input
                          type="checkbox"
                          id="isFatherDateOfBirthUnknown"
                          checked={values.isFatherDateOfBirthUnknown || false}
                          onChange={(e) => {
                            setFieldValue('isFatherDateOfBirthUnknown', e.target.checked);
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      )}
                    </Field>
                    <label htmlFor="isFatherDateOfBirthUnknown" className="ml-2 block text-gray-700 font-medium">
                      Неизвестна дата рождения
                    </label>
                  </div>
                </div>

                {!values.isFatherDateOfBirthUnknown && (
                  <div className="mb-4">
                    <label htmlFor="fatherDateOfBirth" className="block text-gray-700 font-medium mb-2">
                      Дата рождения
                    </label>
                    <Field
                      type="date"
                      id="fatherDateOfBirth"
                      name="fatherDateOfBirth"
                      className="form-input"
                      min={(new Date().getFullYear() - 120) + "-01-01"}
                      max={(new Date().getFullYear() - 18) + "-12-31"}
                    />
                    <ErrorMessage
                      name="fatherDateOfBirth"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field name="isFatherInUSA">
                      {() => (
                        <input
                          type="checkbox"
                          id="isFatherInUSA"
                          checked={values.isFatherInUSA || false}
                          onChange={(e) => {
                            setFieldValue('isFatherInUSA', e.target.checked);
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      )}
                    </Field>
                    <label htmlFor="isFatherInUSA" className="ml-2 block text-gray-700 font-medium">
                      Проживает ли отец в США?
                    </label>
                  </div>

                  {values.isFatherInUSA && (
                    <div className="mt-2">
                      <Field
                        type="text"
                        id="fatherUSAReason"
                        name="fatherUSAReason"
                        placeholder="Причина нахождения в США"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="fatherUSAReason"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Информация о матери */}
              <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Информация о матери</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="mb-4">
                    <label htmlFor="motherSurname" className="block text-gray-700 font-medium mb-2">
                      Фамилия
                    </label>
                    <Field
                      type="text"
                      id="motherSurname"
                      name="motherSurname"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="motherSurname"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="motherName" className="block text-gray-700 font-medium mb-2">
                      Имя
                    </label>
                    <Field
                      type="text"
                      id="motherName"
                      name="motherName"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="motherName"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field name="isMotherDateOfBirthUnknown">
                      {() => (
                        <input
                          type="checkbox"
                          id="isMotherDateOfBirthUnknown"
                          checked={values.isMotherDateOfBirthUnknown || false}
                          onChange={(e) => {
                            setFieldValue('isMotherDateOfBirthUnknown', e.target.checked);
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      )}
                    </Field>
                    <label htmlFor="isMotherDateOfBirthUnknown" className="ml-2 block text-gray-700 font-medium">
                      Неизвестна дата рождения
                    </label>
                  </div>
                </div>

                {!values.isMotherDateOfBirthUnknown && (
                  <div className="mb-4">
                    <label htmlFor="motherDateOfBirth" className="block text-gray-700 font-medium mb-2">
                      Дата рождения
                    </label>
                    <Field
                      type="date"
                      id="motherDateOfBirth"
                      name="motherDateOfBirth"
                      className="form-input"
                      min={(new Date().getFullYear() - 120) + "-01-01"}
                      max={(new Date().getFullYear() - 18) + "-12-31"}
                    />
                    <ErrorMessage
                      name="motherDateOfBirth"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field name="isMotherInUSA">
                      {() => (
                        <input
                          type="checkbox"
                          id="isMotherInUSA"
                          checked={values.isMotherInUSA || false}
                          onChange={(e) => {
                            setFieldValue('isMotherInUSA', e.target.checked);
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      )}
                    </Field>
                    <label htmlFor="isMotherInUSA" className="ml-2 block text-gray-700 font-medium">
                      Проживает ли мать в США?
                    </label>
                  </div>

                  {values.isMotherInUSA && (
                    <div className="mt-2">
                      <Field
                        type="text"
                        id="motherUSAReason"
                        name="motherUSAReason"
                        placeholder="Причина нахождения в США"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="motherUSAReason"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Информация о родственниках в США */}
              <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field name="hasRelativesInUSA">
                      {() => (
                        <input
                          type="checkbox"
                          id="hasRelativesInUSA"
                          checked={values.hasRelativesInUSA || false}
                          onChange={(e) => {
                            setFieldValue('hasRelativesInUSA', e.target.checked);
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      )}
                    </Field>
                    <label htmlFor="hasRelativesInUSA" className="ml-2 block text-gray-700 font-medium">
                      Есть ли у вас родные братья/сестры или другие близкие родственники в США?
                    </label>
                  </div>

                  {values.hasRelativesInUSA && (
                    <div className="mt-4">
                      <FieldArray name="relatives">
                        {({ push, remove }) => (
                          <div>
                            {values.relatives && values.relatives.length > 0 ? (
                              values.relatives.map((relative, index) => (
                                <div key={index} className="flex flex-col md:flex-row gap-4 mb-4">
                                  <div className="flex-1">
                                    <Field
                                      type="text"
                                      name={`relatives.${index}.name`}
                                      placeholder="ФИО родственника"
                                      className="form-input"
                                    />
                                    <ErrorMessage
                                      name={`relatives.${index}.name`}
                                      component="div"
                                      className="text-red-500 text-sm mt-1"
                                    />
                                  </div>
                                  <div className="flex-1">
                                    <Field
                                      type="text"
                                      name={`relatives.${index}.relationship`}
                                      placeholder="Кем приходится (брат, сестра и т.д.)"
                                      className="form-input"
                                    />
                                    <ErrorMessage
                                      name={`relatives.${index}.relationship`}
                                      component="div"
                                      className="text-red-500 text-sm mt-1"
                                    />
                                  </div>
                                  <button
                                    type="button"
                                    className="bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600"
                                    onClick={() => remove(index)}
                                  >
                                    Удалить
                                  </button>
                                </div>
                              ))
                            ) : (
                              <div className="text-gray-500 mb-2">Нет добавленных родственников</div>
                            )}
                            <button
                              type="button"
                              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                              onClick={() => push({ name: '', relationship: '' })}
                            >
                              Добавить родственника
                            </button>
                          </div>
                        )}
                      </FieldArray>
                      {values.relatives && values.relatives.length === 0 && (
                        <ErrorMessage
                          name="relatives"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>

            </div>

            {/* Hidden submit button - will be triggered by StepWrapper */}
            <button type="submit" style={{ display: 'none' }} />
          </Form>
          );
        }}
      </Formik>
    </div>
  );
});

Step7_FamilyInfo.displayName = 'Step7_FamilyInfo';

export default Step7_FamilyInfo;