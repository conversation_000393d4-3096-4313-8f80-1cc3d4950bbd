import React, { useState, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { Formik, Form, ErrorMessage, FieldArray, FormikProps } from 'formik';
import { getStepValidation } from '../utils/validationHelpers';
import { Step9Data } from '../utils/types';
import { sanitizeCountryName } from '../utils/sanitization';

export interface Step9Ref {
  submitForm: () => void;
  isValid: boolean;
}

interface Step9Props {
  initialValues: Step9Data;
  onSubmit: (values: Step9Data) => void;
}

// Список популярных стран на русском языке
const popularCountries = [
  'Турция', 'Египет', 'ОАЭ', 'Таиланд', 'Китай',
  'Германия', 'Франция', 'Италия', 'Испания', 'Великобритания',
  'США', 'Канада', 'Япония', 'Южная Корея', 'Индия',
  'Греция', 'Черногория', 'Грузия', 'Армения', 'Азербайджан'
];

// Расширенный список стран на русском языке
const allCountries = [
  'Австралия', 'Австрия', 'Азербайджан', 'Албания', 'Алжир', 'Ангола', 'Андорра',
  'Антигуа и Барбуда', 'Аргентина', 'Армения', 'Афганистан', 'Багамы', 'Бангладеш',
  'Барбадос', 'Бахрейн', 'Беларусь', 'Белиз', 'Бельгия', 'Бенин', 'Болгария',
  'Боливия', 'Босния и Герцеговина', 'Ботсвана', 'Бразилия', 'Бруней', 'Буркина-Фасо',
  'Бурунди', 'Бутан', 'Вануату', 'Ватикан', 'Великобритания', 'Венгрия', 'Венесуэла',
  'Вьетнам', 'Габон', 'Гаити', 'Гайана', 'Гамбия', 'Гана', 'Гватемала', 'Гвинея',
  'Гвинея-Бисау', 'Германия', 'Гондурас', 'Гренада', 'Греция', 'Грузия', 'Дания',
  'Джибути', 'Доминика', 'Доминиканская Республика', 'Египет', 'Замбия', 'Зимбабве',
  'Израиль', 'Индия', 'Индонезия', 'Иордания', 'Ирак', 'Иран', 'Ирландия', 'Исландия',
  'Испания', 'Италия', 'Йемен', 'Кабо-Верде', 'Казахстан', 'Камбоджа', 'Камерун',
  'Канада', 'Катар', 'Кения', 'Кипр', 'Киргизия', 'Кирибати', 'Китай', 'Колумбия',
  'Коморы', 'Конго', 'Коста-Рика', 'Кот-д\'Ивуар', 'Куба', 'Кувейт', 'Лаос', 'Латвия',
  'Лесото', 'Либерия', 'Ливан', 'Ливия', 'Литва', 'Лихтенштейн', 'Люксембург', 'Маврикий',
  'Мавритания', 'Мадагаскар', 'Малави', 'Малайзия', 'Мали', 'Мальдивы', 'Мальта',
  'Марокко', 'Маршалловы Острова', 'Мексика', 'Мозамбик', 'Молдова', 'Монако', 'Монголия',
  'Мьянма', 'Намибия', 'Науру', 'Непал', 'Нигер', 'Нигерия', 'Нидерланды', 'Никарагуа',
  'Новая Зеландия', 'Норвегия', 'ОАЭ', 'Оман', 'Пакистан', 'Палау', 'Панама',
  'Папуа - Новая Гвинея', 'Парагвай', 'Перу', 'Польша', 'Португалия', 'Россия', 'Руанда',
  'Румыния', 'Сальвадор', 'Самоа', 'Сан-Марино', 'Сан-Томе и Принсипи', 'Саудовская Аравия',
  'Северная Македония', 'Сейшелы', 'Сенегал', 'Сент-Винсент и Гренадины', 'Сент-Китс и Невис',
  'Сент-Люсия', 'Сербия', 'Сингапур', 'Сирия', 'Словакия', 'Словения', 'Соломоновы Острова',
  'Сомали', 'Судан', 'Суринам', 'США', 'Сьерра-Леоне', 'Таджикистан', 'Таиланд', 'Танзания',
  'Того', 'Тонга', 'Тринидад и Тобаго', 'Тувалу', 'Тунис', 'Туркменистан', 'Турция', 'Уганда',
  'Узбекистан', 'Украина', 'Уругвай', 'Фиджи', 'Филиппины', 'Финляндия', 'Франция', 'Хорватия',
  'Центральноафриканская Республика', 'Чад', 'Черногория', 'Чехия', 'Чили', 'Швейцария',
  'Швеция', 'Шри-Ланка', 'Эквадор', 'Экваториальная Гвинея', 'Эритрея', 'Эсватини', 'Эстония',
  'Эфиопия', 'Южная Корея', 'Южный Судан', 'Ямайка', 'Япония'
];

const Step9_TravelHistory = forwardRef<Step9Ref, Step9Props>(({ initialValues, onSubmit }, ref) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCountries, setFilteredCountries] = useState<string[]>([]);
  const [hasNotTraveled, setHasNotTraveled] = useState(initialValues.hasNotTraveled || false);
  const [isFormValid, setIsFormValid] = useState(false);
  const formikRef = useRef<FormikProps<Step9Data>>(null);
  const validationSchema = getStepValidation(9);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      if (formikRef.current) {
        formikRef.current.submitForm();
      }
    },
    isValid: isFormValid,
  }), [isFormValid]);

  // Filter countries based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCountries([]);
    } else {
      const query = searchQuery.toLowerCase();
      const results = allCountries.filter(
        country => country.toLowerCase().includes(query)
      ).slice(0, 10); // Limit to 10 results for better UX
      setFilteredCountries(results);
    }
  }, [searchQuery]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">История поездок</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, укажите страны, которые вы посещали ранее.
      </p>

      <Formik
        innerRef={formikRef}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ values, isValid: formikIsValid, setFieldValue }) => {
          // Update the validity state when Formik's validity changes
          useEffect(() => {
            setIsFormValid(formikIsValid);
          }, [formikIsValid]);

          return (
            <Form>
              <div className="grid grid-cols-1 gap-4">
                <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">В каких странах вы были ранее?</h3>

                  {/* Option for "Never traveled abroad" */}
                  <div className="mb-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={hasNotTraveled}
                        onChange={(e) => {
                          setHasNotTraveled(e.target.checked);
                          setFieldValue('hasNotTraveled', e.target.checked);
                          if (e.target.checked) {
                            setFieldValue('visitedCountries', []);
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="text-gray-700">Не был за границей</span>
                    </label>
                  </div>

                  {/* Popular Countries */}
                  {!hasNotTraveled && (
                    <div className="mb-4">
                      <label className="block text-gray-700 font-medium mb-2">
                        Популярные страны:
                      </label>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {popularCountries.map((country) => (
                          <button
                            key={country}
                            type="button"
                            className={`px-3 py-1 rounded-full text-sm font-medium
                              ${values.visitedCountries.includes(country)
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                            onClick={() => {
                              if (values.visitedCountries.includes(country)) {
                                setFieldValue(
                                  'visitedCountries',
                                  values.visitedCountries.filter((c) => c !== country)
                                );
                              } else {
                                setHasNotTraveled(false);
                                setFieldValue('hasNotTraveled', false);
                                setFieldValue(
                                  'visitedCountries',
                                  [...values.visitedCountries, country]
                                );
                              }
                            }}
                          >
                            {country}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Country Search */}
                  {!hasNotTraveled && (
                    <div className="mb-4">
                      <label className="block text-gray-700 font-medium mb-2">
                        Поиск стран:
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(sanitizeCountryName(e.target.value))}
                          placeholder="Начните вводить название страны..."
                          className="form-input w-full"
                        />
                        
                        {filteredCountries.length > 0 && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                            {filteredCountries.map((country) => (
                              <div
                                key={country}
                                className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${
                                  values.visitedCountries.includes(country) ? 'bg-blue-50' : ''
                                }`}
                                onClick={() => {
                                  if (!values.visitedCountries.includes(country)) {
                                    setHasNotTraveled(false);
                                    setFieldValue('hasNotTraveled', false);
                                    setFieldValue(
                                      'visitedCountries',
                                      [...values.visitedCountries, country]
                                    );
                                  }
                                  setSearchQuery('');
                                }}
                              >
                                {country}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Selected Countries */}
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">
                      Выбранные страны:
                    </label>
                    <FieldArray name="visitedCountries">
                      {({ remove }) => (
                        <div>
                          {hasNotTraveled ? (
                            <div className="text-gray-500 mb-2">Не был за границей</div>
                          ) : values.visitedCountries.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {values.visitedCountries.map((country, index) => (
                                <div
                                  key={index}
                                  className="flex items-center gap-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full"
                                >
                                  <span>{country}</span>
                                  <button
                                    type="button"
                                    className="text-blue-500 hover:text-blue-700"
                                    onClick={() => remove(index)}
                                  >
                                    ✕
                                  </button>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-gray-500 mb-2">Нет выбранных стран</div>
                          )}
                        </div>
                      )}
                    </FieldArray>
                    <ErrorMessage
                      name="visitedCountries"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>
              </div>

              {/* Hidden submit button - will be triggered by StepWrapper */}
              <button type="submit" style={{ display: 'none' }} />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
});

Step9_TravelHistory.displayName = 'Step9_TravelHistory';

export default Step9_TravelHistory;