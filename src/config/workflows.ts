// Workflow configuration for different countries
export interface WorkflowStep {
  id: number;
  label: string;
  description?: string;
  color: string;
}

export interface CountryWorkflow {
  country: string;
  countryCode: string;
  steps: WorkflowStep[];
}

// Define workflow steps for different countries
export const COUNTRY_WORKFLOWS: CountryWorkflow[] = [
  {
    country: 'США',
    countryCode: 'US',
    steps: [
      { id: 1, label: 'Прошли опросник', description: 'Клиент заполнил начальную анкету', color: 'bg-gray-100' },
      { id: 2, label: 'Оплатили пакет услуг', description: 'Подтверждена оплата услуг', color: 'bg-blue-100' },
      { id: 3, label: 'Сбор информации ботом', description: 'Автоматический сбор документов', color: 'bg-yellow-100' },
      { id: 4, label: 'Ожидает приглашения', description: 'Ожидание приглашения от принимающей стороны', color: 'bg-orange-100' },
      { id: 5, label: 'Согласование кейса', description: 'Согласование документов с клиентом', color: 'bg-purple-100' },
      { id: 6, label: 'Заполнение анкеты + обучение', description: 'Заполнение DS-160 и подготовка к собеседованию', color: 'bg-indigo-100' },
      { id: 7, label: 'Подано', description: 'Документы поданы в консульство', color: 'bg-green-100' },
    ]
  },
  {
    country: 'Великобритания',
    countryCode: 'UK',
    steps: [
      { id: 1, label: 'Прошли опросник', description: 'Клиент заполнил начальную анкету', color: 'bg-gray-100' },
      { id: 2, label: 'Оплатили пакет услуг', description: 'Подтверждена оплата услуг', color: 'bg-blue-100' },
      { id: 3, label: 'Подготовка документов', description: 'Сбор и подготовка документов', color: 'bg-yellow-100' },
      { id: 4, label: 'Заполнение анкеты', description: 'Заполнение визовой анкеты UK', color: 'bg-orange-100' },
      { id: 5, label: 'Запись на подачу', description: 'Запись в визовый центр', color: 'bg-purple-100' },
      { id: 6, label: 'Подано', description: 'Документы поданы в визовый центр', color: 'bg-green-100' },
    ]
  },
  {
    country: 'Шенгенские страны',
    countryCode: 'EU',
    steps: [
      { id: 1, label: 'Прошли опросник', description: 'Клиент заполнил начальную анкету', color: 'bg-gray-100' },
      { id: 2, label: 'Оплатили пакет услуг', description: 'Подтверждена оплата услуг', color: 'bg-blue-100' },
      { id: 3, label: 'Подготовка документов', description: 'Сбор документов для Шенгена', color: 'bg-yellow-100' },
      { id: 4, label: 'Заполнение анкеты', description: 'Заполнение визовой анкеты', color: 'bg-orange-100' },
      { id: 5, label: 'Подано', description: 'Документы поданы в консульство', color: 'bg-green-100' },
    ]
  },
  {
    country: 'Китай',
    countryCode: 'CN',
    steps: [
      { id: 1, label: 'Прошли опросник', description: 'Клиент заполнил начальную анкету', color: 'bg-gray-100' },
      { id: 2, label: 'Оплатили пакет услуг', description: 'Подтверждена оплата услуг', color: 'bg-blue-100' },
      { id: 3, label: 'Подготовка документов', description: 'Сбор документов для Китая', color: 'bg-yellow-100' },
      { id: 4, label: 'Подано', description: 'Документы поданы в консульство', color: 'bg-green-100' },
    ]
  }
];

// Helper functions
export const getWorkflowByCountry = (countryCode: string): CountryWorkflow | undefined => {
  return COUNTRY_WORKFLOWS.find(workflow => workflow.countryCode === countryCode);
};

export const getStepLabel = (countryCode: string, stepId: number): string => {
  const workflow = getWorkflowByCountry(countryCode);
  const step = workflow?.steps.find(s => s.id === stepId);
  return step?.label || `Этап ${stepId}`;
};

export const getStepColor = (countryCode: string, stepId: number): string => {
  const workflow = getWorkflowByCountry(countryCode);
  const step = workflow?.steps.find(s => s.id === stepId);
  return step?.color || 'bg-gray-100';
};

export const getAllStepsForCountry = (countryCode: string): WorkflowStep[] => {
  const workflow = getWorkflowByCountry(countryCode);
  return workflow?.steps || [];
}; 