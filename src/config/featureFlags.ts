// Feature flags configuration
export const FEATURE_FLAGS = {
  // Check payment status when completing the form
  CHECK_PAYMENT_STATUS: process.env.NEXT_PUBLIC_FF_CHECK_PAYMENT_STATUS === 'true' || false,
  
  // Add more feature flags here as needed
} as const;

// Helper function to check if a feature is enabled
export const isFeatureEnabled = (featureName: keyof typeof FEATURE_FLAGS): boolean => {
  return FEATURE_FLAGS[featureName];
};