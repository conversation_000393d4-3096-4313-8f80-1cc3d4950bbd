// Claude Vision API Types

export interface ClaudeVisionConfig {
  apiKey: string;
  baseUrl: string;
  model: 'claude-3-5-sonnet-20241022' | 'claude-3-5-haiku-20241022';
  maxTokens: number;
  timeout: number;
}

export interface ClaudeVisionMessageContent {
  type: 'text' | 'image' | 'document';
  text?: string;
  source?: {
    type: 'url' | 'base64';
    url?: string;
    media_type?: string;
    data?: string;
  };
}

export interface ClaudeVisionMessage {
  role: 'user' | 'assistant';
  content: ClaudeVisionMessageContent[];
}

export interface ClaudeVisionRequest {
  model: string;
  max_tokens: number;
  messages: ClaudeVisionMessage[];
}

export interface ClaudeVisionResponseContent {
  type: 'text';
  text: string;
}

export interface ClaudeVisionUsage {
  input_tokens: number;
  output_tokens: number;
}

export interface ClaudeVisionResponse {
  id: string;
  type: 'message';
  role: 'assistant';
  content: ClaudeVisionResponseContent[];
  model: string;
  stop_reason: string;
  stop_sequence: string | null;
  usage: ClaudeVisionUsage;
}

export interface ClaudeVisionError {
  error: {
    type: string;
    message: string;
  };
}

export interface ClaudeVisionProcessResult {
  success: boolean;
  extractedData?: ClaudeExtractedData;
  rawText: string;
  error?: string;
  tokensUsed?: ClaudeVisionUsage;
}

export interface ClaudeExtractedData {
  name?: string;
  surname?: string;
  dateOfBirth?: string;
  passportIssueDate?: string;
  passportExpiryDate?: string;
  passportNumber?: string;
  nationality?: string;
  rawText: string;
  source: 'claude' | 'mistral' | 'tesseract';
}

// Configuration status
export interface ClaudeOcrStatus {
  available: boolean;
  hasApiKey: boolean;
  model?: string;
}

// Public storage types
export interface PublicStorageUploadResult {
  success: boolean;
  publicUrl?: string;
  error?: string;
}

export interface PublicStorageCleanupResult {
  success: boolean;
  error?: string;
}