// Workflow status types and mappings

export interface WorkflowStatus {
  id: string;
  title: string;
  color: string;
  order: number;
}

export interface ClientStatus {
  visa_status: VisaStatus;
  service_payment_status: PaymentStatus;
  step_status: number;
  requires_fixes: boolean;
}

// Database visa status values - using TypeScript enums instead of strings
export enum VisaStatus {
  PENDING = 'ожидает',
  SUBMITTED = 'подано', 
  APPROVED = 'одобрено',
  REJECTED = 'отклонено'
}

// Database payment status values - using TypeScript enums instead of strings
export enum PaymentStatus {
  PAID = 'оплачено',
  UNPAID = 'не оплачено'
}

// Client status values - using TypeScript enums instead of strings
export enum ClientStatusType {
  COMPLETED_QUESTIONNAIRE = 'Прошли опросник',
  PAID_PACKAGE = 'Оплатили пакет услуг',
  WAITING_INVITATION = 'Ожидает приглашения'
}

// User roles - using TypeScript enums instead of strings
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  VISA_ADMIN = 'visa_admin', 
  MANAGER = 'manager'
}

// Workflow statuses for each country
export const WORKFLOW_STATUSES: { [country: string]: WorkflowStatus[] } = {
  US: [
    { id: 'questionnaire_completed', title: 'Прошли опросник', color: 'bg-blue-100', order: 1 },
    { id: 'package_paid', title: 'Оплатили пакет', color: 'bg-green-100', order: 2 },
    { id: 'bot_collecting', title: 'Сбор информации ботом', color: 'bg-yellow-100', order: 3 },
    { id: 'waiting_invitation', title: 'Ожидает приглашения', color: 'bg-purple-100', order: 4 },
    { id: 'case_coordination', title: 'Согласование кейса', color: 'bg-orange-100', order: 5 },
    { id: 'form_filling', title: 'Заполнение анкеты + обучение', color: 'bg-indigo-100', order: 6 },
    { id: 'submitted', title: 'Подано', color: 'bg-teal-100', order: 7 },
    { id: 'interview_prep', title: 'Оффлайн подготовка к собеседованию', color: 'bg-pink-100', order: 8 }
  ],
  UK: [
    { id: 'questionnaire_completed', title: 'Прошли опросник', color: 'bg-blue-100', order: 1 },
    { id: 'package_paid', title: 'Оплатили пакет', color: 'bg-green-100', order: 2 },
    { id: 'document_check', title: 'Проверка документов', color: 'bg-yellow-100', order: 3 },
    { id: 'case_preparation', title: 'Подготовка кейса', color: 'bg-orange-100', order: 4 },
    { id: 'form_filling', title: 'Заполнение анкеты', color: 'bg-indigo-100', order: 5 },
    { id: 'submitted', title: 'Подано', color: 'bg-teal-100', order: 6 }
  ],
  EU: [
    { id: 'questionnaire_completed', title: 'Прошли опросник', color: 'bg-blue-100', order: 1 },
    { id: 'package_paid', title: 'Оплатили пакет', color: 'bg-green-100', order: 2 },
    { id: 'country_selection', title: 'Выбор страны', color: 'bg-purple-100', order: 3 },
    { id: 'document_prep', title: 'Подготовка документов', color: 'bg-yellow-100', order: 4 },
    { id: 'appointment_booking', title: 'Запись на подачу', color: 'bg-orange-100', order: 5 },
    { id: 'submitted', title: 'Подано', color: 'bg-teal-100', order: 6 }
  ],
  CN: [
    { id: 'questionnaire_completed', title: 'Прошли опросник', color: 'bg-blue-100', order: 1 },
    { id: 'package_paid', title: 'Оплатили пакет', color: 'bg-green-100', order: 2 },
    { id: 'invitation_letter', title: 'Получение приглашения', color: 'bg-purple-100', order: 3 },
    { id: 'document_translation', title: 'Перевод документов', color: 'bg-yellow-100', order: 4 },
    { id: 'form_filling', title: 'Заполнение анкеты', color: 'bg-indigo-100', order: 5 },
    { id: 'submitted', title: 'Подано', color: 'bg-teal-100', order: 6 }
  ]
};

// Function to determine workflow status based on client data
export function getWorkflowStatus(client: ClientStatus): string {
  // If visa is approved, it's the final stage
  if (client.visa_status === VisaStatus.APPROVED) {
    return 'interview_prep'; // For US, or last stage for other countries
  }
  
  // If visa is submitted
  if (client.visa_status === VisaStatus.SUBMITTED) {
    return 'submitted';
  }
  
  // If visa is rejected, still show as submitted (they went through the process)
  if (client.visa_status === VisaStatus.REJECTED) {
    return 'submitted';
  }
  
  // If requires fixes, show as questionnaire completed (needs to fix form)
  if (client.requires_fixes) {
    return 'questionnaire_completed';
  }
  
  // If not paid, only show as questionnaire completed if they have sufficient data
  if (client.service_payment_status === PaymentStatus.UNPAID) {
    // Only show in "Прошли опросник" if step_status >= 3 (completed at least first 3 steps)
    if (client.step_status >= 3) {
      return 'questionnaire_completed';
    }
    // If step_status < 3, don't show in any workflow column (filter out)
    return 'not_ready';
  }
  
  // If paid, determine status based on step_status
  if (client.service_payment_status === PaymentStatus.PAID) {
    if (client.step_status >= 8) {
      return 'form_filling';
    } else if (client.step_status >= 6) {
      return 'case_coordination'; // or equivalent for other countries
    } else if (client.step_status >= 4) {
      return 'bot_collecting'; // or equivalent for other countries  
    } else {
      return 'package_paid';
    }
  }
  
  // Default fallback - only show if they have sufficient data
  if (client.step_status >= 3) {
    return 'questionnaire_completed';
  }
  
  // Filter out incomplete applications
  return 'not_ready';
}

// Function to get workflow statuses for a specific country
export function getCountryWorkflowStatuses(country: string): WorkflowStatus[] {
  return WORKFLOW_STATUSES[country] || WORKFLOW_STATUSES.US;
}

// Function to map workflow status to country-specific equivalent
export function mapStatusToCountry(statusId: string, country: string): string {
  const countryStatuses = getCountryWorkflowStatuses(country);
  
  // Direct mapping if status exists in country
  if (countryStatuses.find(s => s.id === statusId)) {
    return statusId;
  }
  
  // Map common statuses to country equivalents
  const statusMappings: { [key: string]: { [country: string]: string } } = {
    'bot_collecting': {
      'UK': 'document_check',
      'EU': 'country_selection', 
      'CN': 'invitation_letter'
    },
    'waiting_invitation': {
      'UK': 'case_preparation',
      'EU': 'document_prep',
      'CN': 'document_translation'
    },
    'case_coordination': {
      'UK': 'case_preparation',
      'EU': 'document_prep', 
      'CN': 'document_translation'
    },
    'interview_prep': {
      'UK': 'submitted',
      'EU': 'submitted',
      'CN': 'submitted'
    }
  };
  
  return statusMappings[statusId]?.[country] || statusId;
} 