// Payment Status Enum for type safety
export enum PaymentStatus {
  PAID = 'paid',
  UNPAID = 'unpaid'
}

// Visa Status Enum for type safety
export enum VisaStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SUBMITTED = 'submitted'
}

// Helper functions for safe type conversion
export const paymentStatusToBoolean = (status: string): boolean => {
  return status === 'оплачено' || status === PaymentStatus.PAID;
};

export const booleanToPaymentStatus = (isPaid: boolean): PaymentStatus => {
  return isPaid ? PaymentStatus.PAID : PaymentStatus.UNPAID;
};

export const paymentStatusToString = (status: PaymentStatus): string => {
  return status === PaymentStatus.PAID ? 'оплачено' : 'не оплачено';
};

export const stringToPaymentStatus = (status: string): PaymentStatus => {
  return status === 'оплачено' ? PaymentStatus.PAID : PaymentStatus.UNPAID;
};

export interface Company {
  id: string;
  name: string;
  phone_number?: string;
  email?: string;
  is_blocked?: boolean;
  slug?: string;
  wapi_token?: string;
  wapi_profile_id?: string;
  wapi_webhook_url?: string;
  created_at: string;
  updated_at: string;
}

export interface ExtendedVisaCompany extends Company {
  isBlocked: boolean;
  employees?: Employee[];
  adminEmail?: string;
}

export interface Employee {
  id: string;
  email: string;
  password_hash?: string;
  password_plain?: string; // Plain text password for visa admin visibility
  role: 'super_admin' | 'visa_admin' | 'manager';
  company_id?: string;
  full_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  company?: Company; // Join data
}

export interface PriceListItem {
  id: string;
  company_id: string;
  country: string;
  title: string;
  price: number;
  duration?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface VisaApplicationWithStatus {
  id: string;
  agent_id: string;
  form_data: Record<string, unknown>;
  step_status: number;
  uploaded_files: Record<string, unknown>;
  phone_number?: string;
  whatsapp_redirected: boolean;
  company_id?: string;
  service_payment_status: 'оплачено' | 'не оплачено';
  is_paid: boolean;
  service_package_id?: string;
  visa_status: 'ожидает' | 'одобрено' | 'отклонено' | 'подано';
  client_progress_status: string;
  manual_fix_status?: string;
  consular_fee_paid: boolean;
  requires_fixes: boolean;
  fix_comment?: string;
  added_by_manager?: boolean; // Flag to identify manager-added clients
  invitation_file_url?: string; // URL to uploaded invitation file
  created_at: string;
  updated_at: string;
  last_updated?: string;
  service_package?: PriceListItem; // Join data
}

export interface DashboardMetrics {
  totalClients: number; // Clients who completed all 9 steps
  paidClients: number; // Applications with service_payment_status = 'оплачено'
  submittedApplications: number; // visa_status = 'подано'
  totalRevenue: number; // Total from all paid packages
  // Метрики за период (фильтрованные)
  filteredTotalClients?: number;
  filteredPaidClients?: number;
  filteredSubmittedApplications?: number;
  filteredTotalRevenue?: number;
}

export interface AnalyticsData {
  metrics: DashboardMetrics;
  pendingPayments: VisaApplicationWithStatus[]; // Completed questionnaire but not paid
  requiresFixes: VisaApplicationWithStatus[]; // Applications marked with requires_fixes
  regionBreakdown: { [key: string]: number };
  professionBreakdown: { [key: string]: number };
  professionSuccessRate: { [key: string]: { total: number; approved: number; rejected: number } };
  salaryBreakdown: { [key: string]: number };
  caseTypeBreakdown: { [key: string]: number };
  countryBreakdown: { [key: string]: { total: number; approved: number; rejected: number; pending: number; revenue: number } };
  ageBreakdown: { [key: string]: number };
  genderBreakdown: { [key: string]: { total: number; approved: number; rejected: number } };
  incomeApprovalCorrelation: { [key: string]: { total: number; approved: number; rejected: number } };
  rejectionReasons: { [key: string]: number };
  monthlySubmissions: { month: string; count: number }[];
  totalApplications: number;
  filteredTotalApplications: number;
  acceptedApplications: number;
  rejectedApplications: number;
  pendingApplications: number;
  availableCountries?: string[]; // List of all available countries for dropdown
  allCountriesBreakdown?: { [key: string]: { total: number; approved: number; rejected: number; pending: number; revenue: number } }; // All countries data for overview
}

export interface SearchFilters {
  dateFrom?: string;
  dateTo?: string;
  service_payment_status?: 'оплачено' | 'не оплачено';
  visa_status?: 'ожидает' | 'одобрено' | 'отклонено' | 'подано';
  requires_fixes?: boolean;
  company_id?: string;
  country?: string;
  searchQuery?: string;
  profession?: string;
  status?: string;
  minIncome?: string;
  maxIncome?: string;
  maritalStatus?: string;
  destinationCountry?: string;
  hasSponsor?: string;
  hasInvitation?: string;
  hasPreviousRejections?: string;
}

export interface AddClientFormData {
  name: string;
  surname: string;
  phone: string;
  country: string;
  service_package_id: string;
  consular_fee_paid: boolean;
}

// Legacy compatibility - keeping Admin interface for existing code
export interface Admin {
  id: string;
  username: string;
  password: string;
  role: 'super_admin' | 'visa_admin' | 'manager';
  companyId?: string;
  companyName?: string;
}

// Legacy compatibility
export interface VisaCompany {
  id: string;
  name: string;
  isBlocked: boolean;
  createdAt: string;
  adminId: string;
}

export interface DashboardOverviewProps {
  admin: Omit<Admin, 'password'>;
  analytics: AnalyticsData | null;
  onRefresh: () => void;
  selectedGlobalCompany?: string;
  metricsLoading?: boolean;
}

export interface StatisticsDashboardProps {
  analytics: AnalyticsData;
  onRefresh: () => void;
  admin?: {
    companyId?: string;
    role: string;
  };
  selectedGlobalCompany?: string;
  metricsLoading?: boolean;
} 