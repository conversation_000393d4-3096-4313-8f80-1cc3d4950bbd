// Client status and step type definitions

export interface CountryMapping {
  code: string;
  displayName: string;
  variations: string[];
}

export const COUNTRY_MAPPINGS: CountryMapping[] = [
  {
    code: 'US',
    displayName: 'США',
    variations: ['США', 'US', 'USA', 'United States', 'America', 'Америка', 'usa']
  },
  {
    code: 'UK', 
    displayName: 'Великобритания',
    variations: ['Великобритания', 'UK', 'United Kingdom', 'Britain', 'Британия', 'uk']
  },
  {
    code: 'EU',
    displayName: 'Германия',
    variations: ['Германия', 'Франция', 'Италия', 'Испания', 'Шенген', 'Germany', 'France', 'Italy', 'Spain', 'Schengen', 'EU', 'schengen']
  },
  {
    code: 'CN',
    displayName: 'Китай', 
    variations: ['Китай', 'CN', 'China']
  }
];

export const CLIENT_STEP_STATUS = {
  COMPLETED_QUESTIONNAIRE: 1,
  PACKAGE_PAID: 2,
  BOT_COLLECTING_INFO: 3,
  WAITING_INVITATION: 4,
  CASE_COORDINATION: 5,
  FORM_FILLING_TRAINING: 6,
  SUBMITTED: 7
} as const;

export const CLIENT_PROGRESS_STATUS = {
  COMPLETED_QUESTIONNAIRE: 'Прошли опросник',
  PAID_PACKAGE: 'Оплатили пакет услуг',
  BOT_COLLECTING: 'Сбор информации ботом',
  WAITING_INVITATION: 'Ожидает приглашения',
  CASE_COORDINATION: 'Согласование кейса',
  FORM_FILLING: 'Заполнение анкеты + обучение',
  SUBMITTED: 'Подано'
} as const;

export const SERVICE_PAYMENT_STATUS = {
  PAID: 'оплачено',
  UNPAID: 'не оплачено',
  PENDING: 'ожидает оплаты'
} as const;

export const VISA_STATUS = {
  PENDING: 'ожидает',
  SUBMITTED: 'подано', 
  APPROVED: 'одобрено',
  REJECTED: 'отклонено'
} as const;

// Helper functions
export const getCountryDisplayName = (countryCode: string): string => {
  const mapping = COUNTRY_MAPPINGS.find(m => m.code === countryCode);
  return mapping?.displayName || countryCode;
};

export const getCountryCodeFromVariation = (variation: string): string | null => {
  const mapping = COUNTRY_MAPPINGS.find(m => 
    m.variations.some(v => 
      v.toLowerCase().includes(variation.toLowerCase()) ||
      variation.toLowerCase().includes(v.toLowerCase())
    )
  );
  return mapping?.code || null;
};

export const getCountryVariations = (countryCode: string): string[] => {
  const mapping = COUNTRY_MAPPINGS.find(m => m.code === countryCode);
  return mapping?.variations || [countryCode];
};

export const normalizeCountryName = (country: string): string => {
  if (!country) return '';
  
  // First try to find direct match
  const directMapping = COUNTRY_MAPPINGS.find(m => 
    m.variations.includes(country)
  );
  
  if (directMapping) {
    return directMapping.code;
  }
  
  // Try partial match
  const partialMapping = COUNTRY_MAPPINGS.find(m => 
    m.variations.some(v => 
      v.toLowerCase().includes(country.toLowerCase()) ||
      country.toLowerCase().includes(v.toLowerCase())
    )
  );
  
  return partialMapping?.code || country;
};

export type ClientStepStatus = typeof CLIENT_STEP_STATUS[keyof typeof CLIENT_STEP_STATUS];
export type ClientProgressStatus = typeof CLIENT_PROGRESS_STATUS[keyof typeof CLIENT_PROGRESS_STATUS];
export type ServicePaymentStatus = typeof SERVICE_PAYMENT_STATUS[keyof typeof SERVICE_PAYMENT_STATUS];
export type VisaStatusType = typeof VISA_STATUS[keyof typeof VISA_STATUS];