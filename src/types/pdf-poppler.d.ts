declare module 'pdf2pic' {
  interface ConvertOptions {
    density?: number;
    saveFilename?: string;
    savePath?: string;
    format?: 'png' | 'jpeg' | 'jpg';
    width?: number;
    height?: number;
    quality?: number;
  }

  interface ConvertResult {
    name: string;
    size: number;
    path: string;
    page: number;
  }

  interface PDF2PicInstance {
    convertBulk(pages: number | string, options?: ConvertOptions): Promise<ConvertResult[]>;
    convert(page: number | string, options?: ConvertOptions): Promise<ConvertResult>;
  }

  function fromPath(pdfPath: string, options?: ConvertOptions): PDF2PicInstance;
  function fromBuffer(buffer: Buffer, options?: ConvertOptions): PDF2PicInstance;

  export = {
    fromPath,
    fromBuffer
  };
}
