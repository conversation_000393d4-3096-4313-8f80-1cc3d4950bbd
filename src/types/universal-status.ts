/**
 * Universal Status Types for Visa Application System
 * 
 * This file contains all status-related types and constants to ensure type safety
 * and prevent string comparison errors throughout the application.
 */

// ============================================================================
// PAYMENT STATUS TYPES
// ============================================================================

export enum PaymentStatus {
  PAID = 'оплачено',
  UNPAID = 'не оплачено'
}

// Helper functions for payment status
export const isPaymentPaid = (status: string): boolean => {
  return status === PaymentStatus.PAID;
};

export const getPaymentStatusDisplay = (status: PaymentStatus): string => {
  const displays = {
    [PaymentStatus.PAID]: 'Оплачено',
    [PaymentStatus.UNPAID]: 'Не оплачено'
  };
  return displays[status] || 'Неизвестно';
};

// ============================================================================
// VISA STATUS TYPES  
// ============================================================================

export enum VisaStatus {
  PENDING = 'ожидает',
  SUBMITTED = 'подано',
  APPROVED = 'одобрено', 
  REJECTED = 'отклонено'
}

export const getVisaStatusDisplay = (status: VisaStatus): string => {
  const displays = {
    [VisaStatus.PENDING]: 'Ожидает',
    [VisaStatus.SUBMITTED]: 'Подано',
    [VisaStatus.APPROVED]: 'Одобрено',
    [VisaStatus.REJECTED]: 'Отклонено'
  };
  return displays[status] || 'Неизвестно';
};

// ============================================================================
// CLIENT PROGRESS STATUS TYPES (Trello Step Labels)
// ============================================================================

export enum ClientProgressStatus {
  // Universal statuses (common across all countries)
  COMPLETED_QUESTIONNAIRE = 'Прошли опросник',
  PAID_PACKAGE = 'Оплатили пакет услуг',
  SUBMITTED = 'Подано',
  
  // US-specific statuses
  BOT_COLLECTING = 'Сбор информации ботом',
  WAITING_INVITATION = 'Ожидает приглашения', 
  CASE_COORDINATION = 'Согласование кейса',
  FORM_FILLING_TRAINING = 'Заполнение анкеты + обучение',
  INTERVIEW_PREP = 'Оффлайн подготовка к собеседованию',
  
  // UK-specific statuses
  DOCUMENT_PREPARATION = 'Подготовка документов',
  FORM_FILLING = 'Заполнение анкеты',
  APPOINTMENT_BOOKING = 'Запись на подачу',
  
  // EU-specific statuses  
  COUNTRY_SELECTION = 'Выбор страны',
  
  // China-specific statuses
  INVITATION_LETTER = 'Получение приглашения',
  DOCUMENT_TRANSLATION = 'Перевод документов'
}

// ============================================================================
// STEP STATUS NUMBERS (Database step_status field)
// ============================================================================

export enum StepStatus {
  // Universal steps
  QUESTIONNAIRE_COMPLETED = 1,
  PACKAGE_PAID = 2,
  
  // Country-specific steps (3-7 depending on country)
  STEP_3 = 3,
  STEP_4 = 4, 
  STEP_5 = 5,
  STEP_6 = 6,
  STEP_7 = 7,
  STEP_8 = 8,
  STEP_9 = 9,
  STEP_10 = 10
}

// ============================================================================
// USER ROLE TYPES
// ============================================================================

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  VISA_ADMIN = 'visa_admin',
  MANAGER = 'manager'
}

// ============================================================================
// COUNTRY CODES
// ============================================================================

export enum CountryCode {
  US = 'US',
  UK = 'UK', 
  EU = 'EU',
  CN = 'CN'
}

export const getCountryDisplay = (code: CountryCode): string => {
  const displays = {
    [CountryCode.US]: 'США',
    [CountryCode.UK]: 'Великобритания',
    [CountryCode.EU]: 'Шенгенские страны',
    [CountryCode.CN]: 'Китай'
  };
  return displays[code] || code;
};

// ============================================================================
// WORKFLOW STEP MAPPING (Country-specific step configurations)
// ============================================================================

export interface WorkflowStepConfig {
  stepNumber: StepStatus;
  label: ClientProgressStatus;
  description: string;
  color: string;
}

export const COUNTRY_WORKFLOW_STEPS: Record<CountryCode, WorkflowStepConfig[]> = {
  [CountryCode.US]: [
    { 
      stepNumber: StepStatus.QUESTIONNAIRE_COMPLETED, 
      label: ClientProgressStatus.COMPLETED_QUESTIONNAIRE, 
      description: 'Клиент заполнил начальную анкету', 
      color: 'bg-gray-100' 
    },
    { 
      stepNumber: StepStatus.PACKAGE_PAID, 
      label: ClientProgressStatus.PAID_PACKAGE, 
      description: 'Подтверждена оплата услуг', 
      color: 'bg-blue-100' 
    },
    { 
      stepNumber: StepStatus.STEP_3, 
      label: ClientProgressStatus.BOT_COLLECTING, 
      description: 'Автоматический сбор документов', 
      color: 'bg-yellow-100' 
    },
    { 
      stepNumber: StepStatus.STEP_4, 
      label: ClientProgressStatus.WAITING_INVITATION, 
      description: 'Ожидание приглашения от принимающей стороны', 
      color: 'bg-orange-100' 
    },
    { 
      stepNumber: StepStatus.STEP_5, 
      label: ClientProgressStatus.CASE_COORDINATION, 
      description: 'Согласование документов с клиентом', 
      color: 'bg-purple-100' 
    },
    { 
      stepNumber: StepStatus.STEP_6, 
      label: ClientProgressStatus.FORM_FILLING_TRAINING, 
      description: 'Заполнение DS-160 и подготовка к собеседованию', 
      color: 'bg-indigo-100' 
    },
    { 
      stepNumber: StepStatus.STEP_7, 
      label: ClientProgressStatus.SUBMITTED, 
      description: 'Документы поданы в консульство', 
      color: 'bg-green-100' 
    }
  ],
  
  [CountryCode.UK]: [
    { 
      stepNumber: StepStatus.QUESTIONNAIRE_COMPLETED, 
      label: ClientProgressStatus.COMPLETED_QUESTIONNAIRE, 
      description: 'Клиент заполнил начальную анкету', 
      color: 'bg-gray-100' 
    },
    { 
      stepNumber: StepStatus.PACKAGE_PAID, 
      label: ClientProgressStatus.PAID_PACKAGE, 
      description: 'Подтверждена оплата услуг', 
      color: 'bg-blue-100' 
    },
    { 
      stepNumber: StepStatus.STEP_3, 
      label: ClientProgressStatus.DOCUMENT_PREPARATION, 
      description: 'Сбор и подготовка документов', 
      color: 'bg-yellow-100' 
    },
    { 
      stepNumber: StepStatus.STEP_4, 
      label: ClientProgressStatus.FORM_FILLING, 
      description: 'Заполнение визовой анкеты UK', 
      color: 'bg-orange-100' 
    },
    { 
      stepNumber: StepStatus.STEP_5, 
      label: ClientProgressStatus.APPOINTMENT_BOOKING, 
      description: 'Запись в визовый центр', 
      color: 'bg-purple-100' 
    },
    { 
      stepNumber: StepStatus.STEP_6, 
      label: ClientProgressStatus.SUBMITTED, 
      description: 'Документы поданы в визовый центр', 
      color: 'bg-green-100' 
    }
  ],
  
  [CountryCode.EU]: [
    { 
      stepNumber: StepStatus.QUESTIONNAIRE_COMPLETED, 
      label: ClientProgressStatus.COMPLETED_QUESTIONNAIRE, 
      description: 'Клиент заполнил начальную анкету', 
      color: 'bg-gray-100' 
    },
    { 
      stepNumber: StepStatus.PACKAGE_PAID, 
      label: ClientProgressStatus.PAID_PACKAGE, 
      description: 'Подтверждена оплата услуг', 
      color: 'bg-blue-100' 
    },
    { 
      stepNumber: StepStatus.STEP_3, 
      label: ClientProgressStatus.DOCUMENT_PREPARATION, 
      description: 'Сбор документов для Шенгена', 
      color: 'bg-yellow-100' 
    },
    { 
      stepNumber: StepStatus.STEP_4, 
      label: ClientProgressStatus.FORM_FILLING, 
      description: 'Заполнение визовой анкеты', 
      color: 'bg-orange-100' 
    },
    { 
      stepNumber: StepStatus.STEP_5, 
      label: ClientProgressStatus.SUBMITTED, 
      description: 'Документы поданы в консульство', 
      color: 'bg-green-100' 
    }
  ],
  
  [CountryCode.CN]: [
    { 
      stepNumber: StepStatus.QUESTIONNAIRE_COMPLETED, 
      label: ClientProgressStatus.COMPLETED_QUESTIONNAIRE, 
      description: 'Клиент заполнил начальную анкету', 
      color: 'bg-gray-100' 
    },
    { 
      stepNumber: StepStatus.PACKAGE_PAID, 
      label: ClientProgressStatus.PAID_PACKAGE, 
      description: 'Подтверждена оплата услуг', 
      color: 'bg-blue-100' 
    },
    { 
      stepNumber: StepStatus.STEP_3, 
      label: ClientProgressStatus.DOCUMENT_PREPARATION, 
      description: 'Сбор документов для Китая', 
      color: 'bg-yellow-100' 
    },
    { 
      stepNumber: StepStatus.STEP_4, 
      label: ClientProgressStatus.SUBMITTED, 
      description: 'Документы поданы в консульство', 
      color: 'bg-green-100' 
    }
  ]
};

// ============================================================================
// UTILITY FUNCTIONS FOR TYPE-SAFE STATUS OPERATIONS
// ============================================================================

/**
 * Get workflow step configuration for a specific country and step number
 */
export const getWorkflowStep = (country: CountryCode, stepNumber: StepStatus): WorkflowStepConfig | undefined => {
  return COUNTRY_WORKFLOW_STEPS[country]?.find(step => step.stepNumber === stepNumber);
};

/**
 * Get all workflow steps for a specific country
 */
export const getCountryWorkflowSteps = (country: CountryCode): WorkflowStepConfig[] => {
  return COUNTRY_WORKFLOW_STEPS[country] || [];
};

/**
 * Get the label for a specific step in a country
 */
export const getStepLabel = (country: CountryCode, stepNumber: StepStatus): string => {
  const step = getWorkflowStep(country, stepNumber);
  return step?.label || `Этап ${stepNumber}`;
};

/**
 * Get the color for a specific step in a country
 */
export const getStepColor = (country: CountryCode, stepNumber: StepStatus): string => {
  const step = getWorkflowStep(country, stepNumber);
  return step?.color || 'bg-gray-100';
};

/**
 * Convert string country code to CountryCode enum
 */
export const parseCountryCode = (countryString: string): CountryCode => {
  const upperCase = countryString.toUpperCase();
  if (Object.values(CountryCode).includes(upperCase as CountryCode)) {
    return upperCase as CountryCode;
  }
  // Default fallback
  return CountryCode.US;
};

/**
 * Convert string to PaymentStatus enum safely
 */
export const parsePaymentStatus = (statusString: string): PaymentStatus => {
  if (Object.values(PaymentStatus).includes(statusString as PaymentStatus)) {
    return statusString as PaymentStatus;
  }
  return PaymentStatus.UNPAID;
};

/**
 * Convert string to VisaStatus enum safely
 */
export const parseVisaStatus = (statusString: string): VisaStatus => {
  if (Object.values(VisaStatus).includes(statusString as VisaStatus)) {
    return statusString as VisaStatus;
  }
  return VisaStatus.PENDING;
};

/**
 * Convert string to ClientProgressStatus enum safely
 */
export const parseClientProgressStatus = (statusString: string): ClientProgressStatus => {
  if (Object.values(ClientProgressStatus).includes(statusString as ClientProgressStatus)) {
    return statusString as ClientProgressStatus;
  }
  return ClientProgressStatus.COMPLETED_QUESTIONNAIRE;
};

/**
 * Convert string to UserRole enum safely
 */
export const parseUserRole = (roleString: string): UserRole => {
  if (Object.values(UserRole).includes(roleString as UserRole)) {
    return roleString as UserRole;
  }
  return UserRole.MANAGER;
};

// ============================================================================
// TYPE GUARDS FOR RUNTIME VALIDATION
// ============================================================================

export const isValidPaymentStatus = (status: string): status is PaymentStatus => {
  return Object.values(PaymentStatus).includes(status as PaymentStatus);
};

export const isValidVisaStatus = (status: string): status is VisaStatus => {
  return Object.values(VisaStatus).includes(status as VisaStatus);
};

export const isValidClientProgressStatus = (status: string): status is ClientProgressStatus => {
  return Object.values(ClientProgressStatus).includes(status as ClientProgressStatus);
};

export const isValidUserRole = (role: string): role is UserRole => {
  return Object.values(UserRole).includes(role as UserRole);
};

export const isValidCountryCode = (country: string): country is CountryCode => {
  return Object.values(CountryCode).includes(country as CountryCode);
};

// ============================================================================
// CLIENT STATUS INTERFACE (with type-safe properties)
// ============================================================================

export interface TypeSafeClientStatus {
  visa_status: VisaStatus;
  service_payment_status: PaymentStatus;
  step_status: StepStatus;
  client_progress_status: ClientProgressStatus;
  requires_fixes: boolean;
  consular_fee_paid: boolean;
  user_role?: UserRole;
  country_code?: CountryCode;
}

/**
 * Create a type-safe client status from database strings
 */
export const createTypeSafeClientStatus = (rawData: {
  visa_status: string;
  service_payment_status: string;
  step_status: number;
  client_progress_status: string;
  requires_fixes: boolean;
  consular_fee_paid: boolean;
  user_role?: string;
  country_code?: string;
}): TypeSafeClientStatus => {
  return {
    visa_status: parseVisaStatus(rawData.visa_status),
    service_payment_status: parsePaymentStatus(rawData.service_payment_status),
    step_status: rawData.step_status as StepStatus,
    client_progress_status: parseClientProgressStatus(rawData.client_progress_status),
    requires_fixes: rawData.requires_fixes,
    consular_fee_paid: rawData.consular_fee_paid,
    user_role: rawData.user_role ? parseUserRole(rawData.user_role) : undefined,
    country_code: rawData.country_code ? parseCountryCode(rawData.country_code) : undefined
  };
}; 