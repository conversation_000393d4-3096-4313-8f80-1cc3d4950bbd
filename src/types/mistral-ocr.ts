// Mistral OCR API Types

export interface MistralOcrDocumentUrl {
  type: 'document_url';
  document_url: string;
}

export interface MistralOcrImageUrl {
  type: 'image_url';
  image_url: string;
}

export type MistralOcrDocument = MistralOcrDocumentUrl | MistralOcrImageUrl;

export interface MistralOcrRequest {
  model: 'mistral-ocr-latest';
  document: MistralOcrDocument;
  include_image_base64?: boolean;
}

export interface MistralOcrImageData {
  id: string;
  top_left_x: number;
  top_left_y: number;
  bottom_right_x: number;
  bottom_right_y: number;
  image_base64?: string; // Optional, only included if include_image_base64 is true
}

export interface MistralOcrResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  pages: Array<{
    index: number;
    markdown: string;
    images: MistralOcrImageData[];
  }>;
}

export interface MistralOcrError {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

export interface MistralOcrProcessResult {
  success: boolean;
  text: string;
  images?: MistralOcrImageData[];
  error?: string;
}

// Configuration types
export interface MistralOcrConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  includeImages?: boolean;
}
