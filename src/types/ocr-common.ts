// Common OCR types used across different OCR engines

export interface ParsedNameData {
  name?: string;
  surname?: string;
  dateOfBirth?: string;
  passportIssueDate?: string;
  passportExpiryDate?: string;
  passportNumber?: string;
  nationality?: string;
  citizenship?: string;
  rawText: string;
  source: 'claude' | 'mistral' | 'tesseract';
}

// File info interface for temporary files
export interface TempFileInfo {
  path: string;
  url: string;
  cleanup: () => Promise<void>;
} 