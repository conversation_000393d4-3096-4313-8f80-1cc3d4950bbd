// Hook for cross-step validation

import { useState, useEffect, useCallback } from 'react';
import { 
  validateAllCrossStepConsistency, 
  CrossStepValidationError,
  groupErrorsByStep 
} from '../utils/crossStepValidation';
import { Step3Data, Step4Data, Step5Data, Step7Data } from '../utils/types';

interface CrossStepValidationHook {
  errors: CrossStepValidationError[];
  errorsByStep: Record<number, CrossStepValidationError[]>;
  hasErrors: boolean;
  validateAll: () => void;
  clearErrors: () => void;
}

export const useCrossStepValidation = (
  step3Data?: Step3Data,
  step4Data?: Step4Data,
  step5Data?: Step5Data,
  step7Data?: Step7Data
): CrossStepValidationHook => {
  const [errors, setErrors] = useState<CrossStepValidationError[]>([]);

  const validateAll = useCallback(() => {
    const validationErrors = validateAllCrossStepConsistency(
      step3Data,
      step4Data,
      step5Data,
      step7Data
    );
    setErrors(validationErrors);
  }, [step3Data, step4Data, step5Data, step7Data]);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // Auto-validate when data changes
  useEffect(() => {
    validateAll();
  }, [validateAll]);

  const errorsByStep = groupErrorsByStep(errors);
  const hasErrors = errors.length > 0;

  return {
    errors,
    errorsByStep,
    hasErrors,
    validateAll,
    clearErrors
  };
};

// Hook for validating specific step relationships
export const useStepRelationshipValidation = (
  currentStep: number,
  allStepData: {
    step3?: Step3Data;
    step4?: Step4Data;
    step5?: Step5Data;
    step7?: Step7Data;
  }
) => {
  const [relationshipErrors, setRelationshipErrors] = useState<string[]>([]);

  const validateCurrentStepRelationships = useCallback(() => {
    const errors: string[] = [];

    // Validate based on current step
    switch (currentStep) {
      case 7:
        // When on step 7, validate marital status consistency
        if (allStepData.step3) {
          if (allStepData.step3.maritalStatus === 'married' && allStepData.step7 && !allStepData.step7.hasSpouse) {
            errors.push('Вы указали, что состоите в браке, но не предоставили информацию о супруге');
          }
        }
        break;
      
      case 5:
        // When on step 5, validate visa history consistency
        if (allStepData.step5) {
          if (allStepData.step5.hasUSVisa && !allStepData.step5.visaNumber?.trim()) {
            errors.push('Номер визы обязателен, если у вас есть виза США');
          }
        }
        break;
      
      case 4:
        // When on step 4, validate travel dates
        if (allStepData.step4) {
          const { departureDate, returnDate } = allStepData.step4;
          if (departureDate && returnDate) {
            const departure = new Date(departureDate);
            const returnD = new Date(returnDate);
            if (returnD <= departure) {
              errors.push('Дата возвращения должна быть после даты вылета');
            }
          }
        }
        break;
    }

    setRelationshipErrors(errors);
  }, [currentStep, allStepData]);

  useEffect(() => {
    validateCurrentStepRelationships();
  }, [validateCurrentStepRelationships]);

  return {
    relationshipErrors,
    hasRelationshipErrors: relationshipErrors.length > 0,
    validateCurrentStepRelationships
  };
};