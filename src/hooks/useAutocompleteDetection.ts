import { useEffect, useRef } from 'react';
import { FormikProps } from 'formik';

/**
 * Hook to detect browser autocomplete and trigger Formik validation
 * Solves the issue where browser autocomplete doesn't trigger form validation
 */
export function useAutocompleteDetection<T>(
  formikRef: React.RefObject<FormikProps<T>>,
  fields: string[]
) {
  const previousValuesRef = useRef<Partial<T>>({});

  useEffect(() => {
    if (!formikRef.current) return;

    // Check for autocomplete on mount and periodically
    const checkAutocomplete = () => {
      if (!formikRef.current) return;

      const currentValues = formikRef.current.values;
      let hasChanges = false;

      // Check if any field values have changed without Formik knowing
      fields.forEach((field) => {
        const currentValue = (currentValues as any)[field];
        const previousValue = (previousValuesRef.current as any)[field];

        if (currentValue !== previousValue && currentValue) {
          hasChanges = true;
        }
      });

      if (hasChanges) {
        // Force Formik to recognize the changes
        formikRef.current.setTouched(
          fields.reduce((acc, field) => ({ ...acc, [field]: true }), {})
        );
        
        // Trigger validation
        formikRef.current.validateForm();
        
        // Update our reference
        previousValuesRef.current = { ...currentValues };
      }
    };

    // Check immediately on mount
    checkAutocomplete();

    // Check periodically for the first few seconds (when autocomplete usually happens)
    const intervals = [100, 300, 500, 1000, 2000];
    const timeouts: NodeJS.Timeout[] = [];

    intervals.forEach((delay) => {
      const timeout = setTimeout(checkAutocomplete, delay);
      timeouts.push(timeout);
    });

    // Also check on any input event in the form
    const handleInput = () => {
      setTimeout(checkAutocomplete, 50);
    };

    const form = formikRef.current?.getFormikBag?.()?.formElement;
    if (form) {
      form.addEventListener('input', handleInput);
    }

    return () => {
      timeouts.forEach(clearTimeout);
      if (form) {
        form.removeEventListener('input', handleInput);
      }
    };
  }, [formikRef, fields]);

  // Also detect when browser might have autofilled by monitoring visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && formikRef.current) {
        setTimeout(() => {
          formikRef.current?.validateForm();
        }, 100);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [formikRef]);
}