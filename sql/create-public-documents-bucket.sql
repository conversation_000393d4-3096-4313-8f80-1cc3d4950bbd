-- Create public-documents bucket for Claude Vision API
-- This bucket stores documents temporarily for Claude API access via URL

-- Create the bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'public-documents',
  'public-documents',
  true,
  52428800, -- 50MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/heic', 'image/heif', 'application/pdf']
);

-- Set up RLS policies for the bucket
CREATE POLICY "Anyone can read public documents" ON storage.objects
  FOR SELECT USING (bucket_id = 'public-documents');

CREATE POLICY "Authenticated users can upload documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'public-documents' AND
    auth.role() = 'authenticated'
  );

CREATE POLICY "Users can delete their own documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'public-documents' AND
    auth.role() = 'authenticated'
  );

-- Grant necessary permissions
GRANT SELECT, INSERT, DELETE ON storage.objects TO authenticated;
GRANT SELECT ON storage.buckets TO authenticated;