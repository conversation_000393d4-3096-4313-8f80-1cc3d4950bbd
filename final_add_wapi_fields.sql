-- Final SQL script to add WhatsApp API fields with specific dummy data
-- Run this in your Supabase SQL Editor

-- Step 1: Add WhatsApp API fields to companies table
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS wapi_token TEXT,
ADD COLUMN IF NOT EXISTS wapi_profile_id TEXT,
ADD COLUMN IF NOT EXISTS wapi_webhook_url TEXT;

-- Step 2: Add comments for documentation
COMMENT ON COLUMN public.companies.wapi_token IS 'WhatsApp API Token for integration';
COMMENT ON COLUMN public.companies.wapi_profile_id IS 'WhatsApp API Profile ID';
COMMENT ON COLUMN public.companies.wapi_webhook_url IS 'WhatsApp API Webhook URL for callbacks';

-- Step 3: Create index for performance
CREATE INDEX IF NOT EXISTS idx_companies_wapi_profile_id ON public.companies(wapi_profile_id) WHERE wapi_profile_id IS NOT NULL;

-- Step 4: Add dummy WhatsApp API data to specific existing companies
UPDATE public.companies 
SET 
  wapi_token = 'test123_token_' || substr(id::text, 1, 8),
  wapi_profile_id = 'test123_profile_001',
  wapi_webhook_url = 'https://api.test123.kz/webhook/whatsapp',
  updated_at = NOW()
WHERE name = '123' AND wapi_token IS NULL;

UPDATE public.companies 
SET 
  wapi_token = 'yersco_token_' || substr(id::text, 1, 8),
  wapi_profile_id = 'yersultan_co_profile_001',
  wapi_webhook_url = 'https://api.yersultan.co/webhook/whatsapp',
  updated_at = NOW()
WHERE name = 'Yersultan Co' AND wapi_token IS NULL;

UPDATE public.companies 
SET 
  wapi_token = 'visapro_token_' || substr(id::text, 1, 8),
  wapi_profile_id = 'visa_pro_kz_profile_001',
  wapi_webhook_url = 'https://api.visaprokz.com/webhook/whatsapp',
  updated_at = NOW()
WHERE name = 'Visa Pro Kazakhstan' AND wapi_token IS NULL;

UPDATE public.companies 
SET 
  wapi_token = 'elitevisa_token_' || substr(id::text, 1, 8),
  wapi_profile_id = 'elite_visa_profile_002',
  wapi_webhook_url = 'https://api.elitevisa.kz/webhook/whatsapp',
  updated_at = NOW()
WHERE name = 'Elite Visa Consulting' AND wapi_token IS NULL;

UPDATE public.companies 
SET 
  wapi_token = 'globalis_token_' || substr(id::text, 1, 8),
  wapi_profile_id = 'globalis_kz_profile_003',
  wapi_webhook_url = 'https://api.globalis.kz/webhook/whatsapp',
  updated_at = NOW()
WHERE name = 'Globalis Kazakhstan' AND wapi_token IS NULL;

-- Step 5: Verify all companies have WhatsApp API data
SELECT 
  name,
  slug,
  wapi_token,
  wapi_profile_id,
  wapi_webhook_url,
  updated_at
FROM public.companies 
ORDER BY name; 