const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixMissingCompanies() {
  console.log('🔧 Fixing missing companies and orphaned applications...\n');

  try {
    // 1. Check what companies exist
    const { data: existingCompanies, error: companiesError } = await supabase
      .from('companies')
      .select('id, name, slug, is_blocked');

    if (companiesError) {
      console.error('❌ Error fetching companies:', companiesError);
      return;
    }

    console.log('📋 Existing companies:');
    existingCompanies.forEach(c => console.log(`  - ${c.name} (${c.slug})`));

    // 2. Create missing companies if they don't exist
    const targetCompanies = [
      { slug: 'visa-pro-kz', name: 'Visa Pro KZ', email: '<EMAIL>', phone_number: '+77001234567' },
      { slug: '123', name: 'Company 123', email: '<EMAIL>', phone_number: '+77001234568' }
    ];

    for (const targetCompany of targetCompanies) {
      const exists = existingCompanies.find(c => c.slug === targetCompany.slug);
      
      if (!exists) {
        console.log(`\n🔨 Creating missing company: ${targetCompany.name} (${targetCompany.slug})`);
        
        const { data: newCompany, error: createError } = await supabase
          .from('companies')
          .insert([{
            name: targetCompany.name,
            slug: targetCompany.slug,
            email: targetCompany.email,
            phone_number: targetCompany.phone_number,
            is_blocked: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select()
          .single();

        if (createError) {
          console.error(`❌ Error creating company ${targetCompany.slug}:`, createError);
        } else {
          console.log(`✅ Created company: ${newCompany.name} (ID: ${newCompany.id})`);
          existingCompanies.push(newCompany);
        }
      } else {
        console.log(`✅ Company ${targetCompany.slug} already exists (ID: ${exists.id})`);
      }
    }

    // 3. Check for applications with null company_id
    const { data: nullCompanyApps, error: nullError } = await supabase
      .from('visa_applications')
      .select('id, agent_id, form_data, company_id, created_at')
      .is('company_id', null)
      .order('created_at', { ascending: false });

    if (nullError) {
      console.error('❌ Error fetching null company applications:', nullError);
      return;
    }

    if (nullCompanyApps.length > 0) {
      console.log(`\n🔍 Found ${nullCompanyApps.length} applications with null company_id`);
      
      // Get the first created company as default (or use visa-pro-kz if it exists)
      const defaultCompany = existingCompanies.find(c => c.slug === 'visa-pro-kz') || existingCompanies[0];
      
      if (!defaultCompany) {
        console.error('❌ No default company found to assign orphaned applications');
        return;
      }

      console.log(`🔧 Assigning orphaned applications to: ${defaultCompany.name} (ID: ${defaultCompany.id})`);

      // Update applications in batches
      const batchSize = 100;
      let updated = 0;

      for (let i = 0; i < nullCompanyApps.length; i += batchSize) {
        const batch = nullCompanyApps.slice(i, i + batchSize);
        const ids = batch.map(app => app.id);

        const { error: updateError } = await supabase
          .from('visa_applications')
          .update({ 
            company_id: defaultCompany.id,
            updated_at: new Date().toISOString()
          })
          .in('id', ids);

        if (updateError) {
          console.error(`❌ Error updating batch ${i + 1}:`, updateError);
        } else {
          updated += batch.length;
          console.log(`✅ Updated ${updated}/${nullCompanyApps.length} applications`);
        }
      }

      console.log(`🎉 Successfully assigned ${updated} orphaned applications to ${defaultCompany.name}`);
    } else {
      console.log('\n✅ No applications with null company_id found');
    }

    // 4. Verify the fix
    console.log('\n🔍 Verification - Applications by company:');
    const { data: verification, error: verifyError } = await supabase
      .from('visa_applications')
      .select('company_id')
      .not('company_id', 'is', null);

    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
    } else {
      const stats = {};
      verification.forEach(app => {
        stats[app.company_id] = (stats[app.company_id] || 0) + 1;
      });

      for (const [companyId, count] of Object.entries(stats)) {
        const company = existingCompanies.find(c => c.id === companyId);
        const name = company ? company.name : `Unknown (${companyId})`;
        console.log(`  - ${name}: ${count} applications`);
      }
    }

    // 5. Check for remaining null company applications
    const { data: remainingNull, error: nullCheckError } = await supabase
      .from('visa_applications')
      .select('id', { count: 'exact', head: true })
      .is('company_id', null);

    if (nullCheckError) {
      console.error('❌ Error checking remaining null applications:', nullCheckError);
    } else {
      if (remainingNull.count === 0) {
        console.log('\n🎉 All applications now have valid company_id!');
      } else {
        console.log(`\n⚠️  ${remainingNull.count} applications still have null company_id`);
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixMissingCompanies();