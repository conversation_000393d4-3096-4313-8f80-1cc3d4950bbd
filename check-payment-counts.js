const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkPaymentCounts() {
  console.log('🔍 Checking payment and fixes counts...\n');
  
  // Get all applications
  const { data: apps, error } = await supabase
    .from('visa_applications')
    .select('*')
    .order('created_at', { ascending: false });
    
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  // Count by payment status
  const unpaidCount = apps.filter(app => app.service_payment_status === 'не оплачено').length;
  const paidCount = apps.filter(app => app.service_payment_status === 'оплачено').length;
  
  // Count by requires_fixes
  const requiresFixesCount = apps.filter(app => app.requires_fixes === true).length;
  
  // Count by client_progress_status
  const statusCounts = {};
  apps.forEach(app => {
    const status = app.client_progress_status || 'No Status';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });
  
  console.log('📊 Total Applications:', apps.length);
  console.log('\n💰 Payment Status:');
  console.log(`   - Unpaid (не оплачено): ${unpaidCount}`);
  console.log(`   - Paid (оплачено): ${paidCount}`);
  
  console.log('\n🔧 Requires Fixes:', requiresFixesCount);
  
  console.log('\n📈 Client Progress Status Distribution:');
  Object.entries(statusCounts).forEach(([status, count]) => {
    console.log(`   - ${status}: ${count}`);
  });
  
  // Check specific statuses
  console.log('\n🎯 Specific Status Checks:');
  const questionnairesCompleted = apps.filter(app => 
    app.client_progress_status === 'Прошли опросник'
  ).length;
  console.log(`   - Прошли опросник: ${questionnairesCompleted}`);
  
  const unpaidQuestionnaires = apps.filter(app => 
    app.client_progress_status === 'Прошли опросник' && 
    app.service_payment_status === 'не оплачено'
  ).length;
  console.log(`   - Прошли опросник (unpaid): ${unpaidQuestionnaires}`);
  
  // Show sample of unpaid applications
  console.log('\n📋 Sample Unpaid Applications:');
  const unpaidApps = apps.filter(app => app.service_payment_status === 'не оплачено');
  unpaidApps.slice(0, 5).forEach((app, index) => {
    const name = `${app.form_data?.name || ''} ${app.form_data?.surname || ''}`.trim() || 'Unknown';
    console.log(`   ${index + 1}. ${name} - Status: ${app.client_progress_status}, Company: ${app.company_id}`);
  });
}

checkPaymentCounts().catch(console.error);