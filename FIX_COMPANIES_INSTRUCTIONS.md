# Fix Companies Tab - WhatsApp API Fields Missing

## Current Situation
✅ **Companies exist in database** (5 companies found)  
❌ **WhatsApp API fields missing** (causing UI issues)  
❌ **Companies tab showing empty** (frontend can't load WhatsApp fields)

## Solution: Add Missing WhatsApp API Fields

### Step 1: Run SQL Migration in Supabase

1. Go to your **Supabase Dashboard**
2. Navigate to **SQL Editor**
3. Copy and paste the SQL from `add_wapi_fields_to_existing.sql`
4. Click **Run** to execute the migration

The migration will:
- ✅ Add `wapi_token`, `wapi_profile_id`, `wapi_webhook_url` columns
- ✅ Update all 5 existing companies with dummy WhatsApp API data
- ✅ Create database index for performance

### Step 2: Verify Migration Success

After running the SQL, you should see output showing all companies with their new WhatsApp API data:

```
name                 | wapi_token              | wapi_profile_id        | wapi_webhook_url
--------------------|-------------------------|------------------------|----------------------------------
123                 | test_token_123_xyz      | test_profile_123       | https://api.test123.kz/webhook/whatsapp
Yersultan Co        | yco_token_abc123def456  | yco_profile_001        | https://api.yersultan.co/webhook/whatsapp
Visa Pro Kazakhstan | vp_token_9a8b7c6d5e4f3g | visa_pro_profile_001   | https://api.visapro.kz/webhook/whatsapp
Elite Visa Consulting| ev_token_1j2k3l4m5n6o7p | elite_visa_profile_002 | https://api.elitevisa.kz/webhook/whatsapp
Globalis Kazakhstan | gl_token_s0r9q8p7o6n5m4 | globalis_profile_003   | https://api.globalis.kz/webhook/whatsapp
```

### Step 3: Test Companies Tab

1. **Refresh your admin panel** (Ctrl+F5 or Cmd+Shift+R)
2. **Navigate to Companies tab**
3. **Verify you see all 5 companies** with WhatsApp API data

### Expected Result

Companies tab should now display:
- **5 companies** instead of 0
- **WhatsApp API fields** visible in edit modal
- **No loading errors** in browser console

### Companies That Will Be Updated

1. **123** - Test company with test WhatsApp data
2. **Yersultan Co** - Your company with Yersultan-specific data  
3. **Visa Pro Kazakhstan** - Professional visa company
4. **Elite Visa Consulting** - Elite consulting firm (currently blocked)
5. **Globalis Kazakhstan** - Global immigration services

### What Each Company Gets

Each company will have:
- 🔗 **API Token** - Unique WhatsApp API authentication token
- 📱 **Profile ID** - WhatsApp Business Profile identifier  
- 🌐 **Webhook URL** - Callback URL for WhatsApp API events

### Troubleshooting

If companies still don't appear after migration:

1. **Check browser console** for JavaScript errors
2. **Verify migration ran successfully** in Supabase
3. **Test API directly**: `curl http://localhost:3000/api/admin/companies`
4. **Clear browser cache** and refresh

### Files Modified

- ✅ `src/pages/api/admin/companies.ts` - Updated to include WhatsApp fields
- ✅ `add_wapi_fields_to_existing.sql` - Migration script for existing companies
- ✅ `src/components/admin/CompanyManagement.tsx` - Already supports WhatsApp fields
- ✅ `src/types/admin.ts` - Already includes WhatsApp field types

## Next Steps After Fix

Once the migration is complete, you'll be able to:
1. **View all companies** with WhatsApp API data
2. **Edit companies** to modify WhatsApp settings
3. **Create new companies** with WhatsApp API fields
4. **Test WhatsApp integration** using the API credentials 