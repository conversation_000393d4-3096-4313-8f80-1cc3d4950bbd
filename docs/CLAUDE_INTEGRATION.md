# Claude Vision API Integration

## Overview
This document describes the integration of Anthropic's Claude Vision API for OCR and document text extraction.

## Setup

### 1. Environment Variables
Add your Claude API key to your environment:

```bash
# .env.local
NEXT_CLAUDE_KEY=your_claude_api_key_here
```

### 2. Database Setup
Run the SQL script to create the public documents bucket:

```bash
# Execute the SQL script in your Supabase dashboard
sql/create-public-documents-bucket.sql
```

## Usage

### Basic OCR Processing
```typescript
import { processDocumentWithClaude } from '../utils/claude-vision-ocr';

const result = await processDocumentWithClaude(
  '/path/to/document.jpg',
  'image', // or 'pdf'
  'agent-id-123'
);

if (result.success) {
  console.log('Extracted data:', result.extractedData);
  console.log('Tokens used:', result.tokensUsed);
}
```

### Check Claude Availability
```typescript
import { isClaudeOcrAvailable, getClaudeOcrStatus } from '../utils/claude-vision-ocr';

if (isClaudeOcrAvailable()) {
  console.log('<PERSON> OCR is available');
  console.log('Status:', getClaudeOcrStatus());
}
```

## Architecture

### OCR Engine Priority
1. **Claude Vision API** (if `NEXT_CLAUDE_KEY` is set)
2. **Mistral OCR** (if `MISTRAL_API_KEY` is set)
3. **Tesseract.js** (fallback)

### Data Flow
```
File Upload → HEIC Conversion → File Type Detection → Claude API → Structured Data
                                                        ↓ (fallback)
                                                   Mistral OCR → Tesseract
```

### URL-based Processing
Claude API uses URL-based document processing:
1. Upload document to Supabase public storage
2. Generate public URL
3. Send URL to Claude API
4. Clean up document after processing

## Configuration

### Models Available
- `claude-3-5-sonnet-20241022` (default, high accuracy)
- `claude-3-5-haiku-20241022` (faster, lower cost)

### Default Settings
```typescript
const DEFAULT_CONFIG = {
  baseUrl: 'https://api.anthropic.com/v1/messages',
  model: 'claude-3-5-sonnet-20241022',
  maxTokens: 2000,
  timeout: 60000,
};
```

## Supported Features

### Document Types
- ✅ Images (JPEG, PNG, WebP, GIF, HEIC, HEIF)
- ✅ PDF documents (native support)
- ✅ Multi-page documents

### Extracted Data
- Name and surname
- Date of birth
- Passport number
- Passport issue/expiry dates
- Nationality
- Raw text content

### Languages
- English
- Russian
- Kazakh
- Multi-language documents

## Cost Optimization

### Token Usage
- Images: `(width × height) / 750` tokens
- Text: Standard token calculation
- PDF: 1,500-3,000 tokens per page

### Best Practices
1. Resize images to < 1.15 megapixels
2. Use appropriate model for task complexity
3. Implement proper error handling with fallbacks
4. Clean up temporary documents

## Error Handling

### Fallback Strategy
```typescript
try {
  // Try Claude Vision
  const result = await processDocumentWithClaude(path, type, agentId);
  return result;
} catch (error) {
  // Fallback to Mistral OCR
  console.log('Falling back to Mistral OCR...');
  return await processDocumentWithMistral(path, type);
}
```

### Common Issues
- **API Key missing**: Check `NEXT_CLAUDE_KEY` environment variable
- **Public bucket**: Ensure `public-documents` bucket exists
- **File size**: Max 50MB per document
- **Token limits**: Adjust `maxTokens` for complex documents

## Security Considerations

### Public Storage
- Documents are temporarily stored in public Supabase bucket
- Automatic cleanup after processing
- Consider using signed URLs for sensitive documents

### API Security
- Claude API key should be kept secure
- Use environment variables, never hardcode keys
- Implement proper authentication for API endpoints

## Performance

### Comparison with Other OCR Solutions
| Feature | Claude Vision | Mistral OCR | Tesseract |
|---------|---------------|-------------|-----------|
| Accuracy | 99%+ | High | Medium |
| Speed | Fast | Fast | Slow |
| Cost | ~$0.0004/image | ~$0.01/req | Free |
| PDF Support | Native | Native | Complex |
| Languages | Excellent | Good | Good |

### Optimization Tips
1. Use appropriate image resolution
2. Batch process multiple documents
3. Implement caching for repeated documents
4. Use haiku model for simple documents

## Monitoring

### Logging
All processing steps are logged with detailed information:
- OCR engine selection
- Token usage
- Processing time
- Error details
- Fallback decisions

### Analytics
Track OCR performance in your database:
```sql
SELECT 
  COUNT(*) as total_processed,
  AVG(CASE WHEN processing_status = 'success' THEN 1 ELSE 0 END) as success_rate,
  AVG(token_usage) as avg_tokens
FROM ocr_processing_history 
WHERE created_at > NOW() - INTERVAL '24 hours';
```

## Troubleshooting

### Common Solutions
1. **"Claude API key missing"**: Set `NEXT_CLAUDE_KEY` environment variable
2. **"Public bucket not found"**: Run the SQL setup script
3. **"File too large"**: Reduce image size or split PDF
4. **"Rate limit exceeded"**: Implement exponential backoff

### Debug Mode
Enable detailed logging by setting:
```bash
DEBUG=claude-vision-ocr
```

## Future Enhancements

### Planned Features
- [ ] Batch processing for multiple documents
- [ ] Custom prompts for different document types
- [ ] Caching for frequently processed documents
- [ ] Advanced error recovery
- [ ] Performance analytics dashboard

### Migration Path
If migrating from Mistral OCR:
1. Add `NEXT_CLAUDE_KEY` to environment
2. Run database migrations
3. Test with a subset of documents
4. Monitor performance and costs
5. Gradually increase traffic