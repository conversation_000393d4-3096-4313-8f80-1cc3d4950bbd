# Централизованная система валидации форм

## Обзор

Новая централизованная система валидации заменяет 9 отдельных схем валидации (`step1Schema` - `step9Schema`) единой мастер-схемой, которая динамически генерирует валидацию для каждого шага.

## Преимущества

### ✅ Устранено дублирование
- Общие валидации (телефон, email, даты) определены в одном месте
- Единые правила для похожих полей
- Централизованные сообщения об ошибках

### ✅ Лучшая поддержка
- Легче добавлять новые правила валидации
- Изменения применяются ко всем шагам автоматически
- Единый источник истины для всех правил

### ✅ Лучшая производительность
- Схемы генерируются по требованию
- Возможность кэширования
- Меньше дублирования кода

### ✅ Улучшенная типизация
- Централизованные типы для валидации
- Лучшая поддержка TypeScript
- Автокомплит для полей

## Архитектура

### Основные компоненты

1. **Master Schema** (`masterValidationSchema`) - содержит все поля всех шагов
2. **Step Field Mapping** - определяет какие поля принадлежат каждому шагу
3. **Common Validations** - переиспользуемые правила валидации
4. **Helper Functions** - утилиты для работы с валидацией

### Структура файлов

```
src/utils/
├── validations.ts          # Основная система валидации
├── validationHelpers.ts    # Вспомогательные функции
└── __tests__/
    └── validations.test.ts # Тесты системы валидации
```

## Использование

### В компонентах Step

```typescript
// Старый способ
import { step1Schema } from '../utils/validations';

// Новый способ
import { getStepValidation } from '../utils/validationHelpers';
const validationSchema = getStepValidation(1);
```

### Продвинутое использование

```typescript
import { validateMultipleSteps, getStepFieldNames, useStepValidation } from '../utils/validationHelpers';

// Валидация нескольких шагов
const result = await validateMultipleSteps(formData, [1, 2, 3]);

// Получение полей шага
const fieldNames = getStepFieldNames(2);

// Hook-style валидация
const { schema, fieldNames, validateFields } = useStepValidation(6);
```

## Совмещение полей по шагам

### Шаг 1 - Выбор страны
- `visaDestination` - основная страна
- `otherVisaDestination` - другая страна (условное поле)

### Шаг 2 - Документы и личные данные
- `surname`, `name` - имя и фамилия (латиница)
- `dateOfBirth` - дата рождения (18+ лет)
- `citizenship` - гражданство
- `passportNumber`, `passportIssueDate`, `passportExpiryDate` - паспортные данные
- `iin` - ИИН (12 цифр, опционально)
- `idNumber` - номер удостоверения (9 цифр, опционально)

**Особая валидация**: должен быть указан хотя бы один из идентификаторов (паспорт, ИИН или удостоверение)

### Шаг 3 - Расширенная личная информация
- `fullNameCyrillic` - ФИО на кириллице
- `hasOtherNames`, `otherNames` - другие имена (условное)
- `gender` - пол (male/female)
- `maritalStatus` - семейное положение
- `cityOfBirth`, `countryOfBirth` - место рождения
- `hasOtherCitizenship`, `otherCitizenship` - другое гражданство (условное)
- `isPermanentResidentOtherCountry`, `permanentResidenceCountry` - постоянное резидентство (условное)
- `nationality` - национальность

### Шаг 4 - Цель поездки
- `hasOwnTravelPurpose` - есть ли собственная цель
- `travelPurposeDescription` - описание цели (условное)
- `departureDate`, `returnDate` - даты поездки (условные)
- `destination` - место назначения (условное)
- `hasInvitation`, `invitationFile` - приглашение
- `travelWithOthers`, `travelAsGroup`, `groupName`, `companions` - путешествие с другими

### Шаг 5 - История виз США
- `hasBeenToUSA` - был ли в США
- `visitYear`, `visitPurpose`, `visitDuration`, `visitDurationType` - детали визита (условные)
- `hasUSVisa` - есть ли виза США
- `visaNumber` - номер визы (условное, min 8 символов)
- `lastVisaDate` - дата последней визы
- `hasVisaRejections`, `visaRejections` - отказы в визах
- `hasPreviousDS160`, `previousDS160File` - предыдущая DS-160
- `ssn` - SSN (формат XXX-XX-XXXX)
- `taxId` - Tax ID (формат XX-XXXXXXX)

### Шаг 6 - Контактная информация
- `address` - домашний адрес
- `city` - город
- `stateProvince` - штат/область (опционально)
- `country` - страна
- `zipCode` - почтовый индекс
- `phone` - телефон (формат +7 XXX XXX XX XX)
- `email` - email (опционально, но если указан - должен быть валидным)
- `socialMediaLinks` - ссылки на соцсети

### Шаг 7 - Семейная информация
- `hasSpouse` - есть ли супруг(а)
- `spouseLastName`, `spouseFirstName`, `spouseMiddleName` - данные супруга (условные)
- `spouseCityOfBirth`, `spouseCountryOfBirth` - место рождения супруга (условные)
- `spouseDateOfBirth` - дата рождения супруга (условная, 18+ лет)
- `spouseCitizenship` - гражданство супруга (условное)
- `wasSpouseInUSA` - был ли супруг в США (условное)
- `spouseUSAEntryDate`, `spouseUSAStayDuration`, `spouseUSAStayDurationType` - детали пребывания супруга в США (условные)
- `fatherSurname`, `fatherName`, `fatherDateOfBirth` - данные отца
- `isFatherDateOfBirthUnknown`, `isFatherInUSA`, `fatherUSAReason` - дополнительные данные отца
- `motherSurname`, `motherName`, `motherDateOfBirth` - данные матери
- `isMotherDateOfBirthUnknown`, `isMotherInUSA`, `motherUSAReason` - дополнительные данные матери
- `hasRelativesInUSA`, `relatives` - родственники в США

### Шаг 8 - Образование и работа
- `occupation` - род занятий (предопределенный список)
- `educationStatus` - статус образования
- `educationLocation` - место учебы (условное для студентов без высшего образования)
- `institutionName` - название учебного заведения (условное)
- `hasJob` - есть ли работа
- `companyName`, `workAddress`, `workExperience`, `workPhone`, `position` - данные работы (условные)
- `workState`, `workZipCode`, `income` - дополнительные данные работы
- `institutionState`, `institutionZipCode` - данные учебного заведения
- N/A флаги для опциональных полей
- Унаследованные поля для обратной совместимости

### Шаг 9 - История путешествий
- `visitedCountries` - список посещенных стран
- `hasNotTraveled` - не был за границей

**Особая валидация**: должен быть указан либо список стран, либо флаг "не был за границей"

## Общие правила валидации

### Форматы данных

- **Телефон**: `+7 XXX XXX XX XX` (казахстанский формат)
- **Email**: стандартный email формат (опционально)
- **ИИН**: 12 цифр
- **Номер удостоверения**: 9 цифр
- **SSN**: `XXX-XX-XXXX`
- **Tax ID**: `XX-XXXXXXX`
- **Даты**: стандартный формат дат с проверкой возрастных ограничений

### Возрастные ограничения

- **Минимальный возраст**: 18 лет
- **Максимальный возраст**: 120 лет
- Применяется к датам рождения заявителя, супруга, родителей

### Языковые ограничения

- **Латинские имена**: `surname`, `name` и другие официальные поля
- **Кириллические имена**: `fullNameCyrillic` для русских/казахских имен
- Поддержка казахских букв: әғқңөұүһіӘҒҚҢӨҰҮҺІ

## Условная валидация

Многие поля становятся обязательными только при определенных условиях:

- **Другая страна**: обязательна если `visaDestination === 'other'`
- **Паспортные даны**: обязательны если указан `passportNumber`
- **Данные супруга**: обязательны если `hasSpouse === true`
- **История США**: поля обязательны если `hasBeenToUSA === true`
- **Рабочие данные**: обязательны если `hasJob === true` и профессия не "безработный"

## Обратная совместимость

Старые импорты продолжают работать:

```typescript
// Эти импорты все еще работают
import { step1Schema, step2Schema, step3Schema } from '../utils/validations';
```

Но рекомендуется переходить на новую систему:

```typescript
// Рекомендуемый способ
import { getStepValidation } from '../utils/validationHelpers';
const schema = getStepValidation(1);
```

## Тестирование

Создана обширная система тестов в `src/utils/__tests__/validations.test.ts`:

- ✅ Генерация схем для всех шагов
- ✅ Правильность полей для каждого шага
- ✅ Логика валидации (обязательные поля, форматы, условные поля)
- ✅ Кросс-полевая валидация
- ✅ Мульти-шаговая валидация
- ✅ Обратная совместимость
- ✅ Производительность

## Миграция с старой системы

### Шаг 1: Обновить импорты

```typescript
// Было
import { step6Schema } from '../utils/validations';

// Стало
import { getStepValidation } from '../utils/validationHelpers';
const validationSchema = getStepValidation(6);
```

### Шаг 2: Использовать новую схему

```typescript
// В Formik
<Formik validationSchema={validationSchema} />
```

### Шаг 3: Удалить старые импорты (опционально)

После успешного перехода можно удалить прямые импорты `stepXSchema`.

## Расширение системы

### Добавление нового поля

1. Добавить в `masterValidationSchema`
2. Добавить в соответствующий `stepFields[stepNumber]`
3. Обновить типы если нужно
4. Добавить тесты

### Добавление нового шага

1. Определить поля в `masterValidationSchema`
2. Добавить маппинг в `stepFields`
3. Обновить `StepNumber` тип
4. Добавить тесты

### Добавление общей валидации

1. Добавить в `commonValidations`
2. Использовать в `masterValidationSchema`
3. Добавить тесты

## Производительность

- Схемы генерируются по требованию
- Возможность добавления кэширования в будущем
- Уменьшение дублирования кода
- Более быструю компиляцию TypeScript

## Поддержка и обслуживание

Новая система упрощает:

- Добавление новых правил валидации
- Изменение сообщений об ошибках
- Debugging валидации
- Тестирование правил валидации
- Поддержку кода

Все изменения централизованы и применяются автоматически ко всем шагам.