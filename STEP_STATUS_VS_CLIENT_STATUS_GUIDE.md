# 📋 Step Status vs Client Status Guide

## 🎯 Правильное разделение логики статусов

### ⚠️ ВАЖНО: НЕ ПУТАТЬ!

В системе используются **два разных типа статусов** для **разных целей**:

1. **`step_status`** - технический прогресс анкеты (0-9)
2. **`client_progress_status`** - бизнес-этап клиента (строки)

---

## 📊 `step_status` - Технический прогресс анкеты

### Назначение
Отражает **сколько шагов анкеты** реально заполнил клиент в форме.

### Возможные значения
```typescript
step_status: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9
```

### Примеры
- `0` - клиент НЕ начинал заполнять анкету
- `3` - клиент заполнил 3 из 9 шагов анкеты  
- `9` - клиент полностью прошел всю анкету

### ✅ Правильное использование
```typescript
// Клиент заполнил 5 шагов анкеты
step_status: 5

// Оплаченный клиент, но еще не заполнял анкету
step_status: 0
```

### ❌ Неправильное использование
```typescript
// НЕВЕРНО: step_status НЕ должен отражать бизнес-статусы!
step_status: CLIENT_STEP_STATUS.PACKAGE_PAID // ❌
step_status: "оплатили пакет" // ❌
```

---

## 🏢 `client_progress_status` - Бизнес-этап клиента

### Назначение
Отражает **на каком этапе работы** находится клиент с точки зрения бизнес-процесса.

### Возможные значения
```typescript
client_progress_status: 
  | "Прошли опросник"
  | "Оплатили пакет услуг" 
  | "Сбор информации ботом"
  | "Ожидает приглашения"
  | "Согласование кейса"
  | "Заполнение анкеты + обучение"
  | "Подано"
```

### ✅ Правильное использование
```typescript
// Клиент оплатил пакет, но еще не заполнял анкету
client_progress_status: "Оплатили пакет услуг"

// Клиент подал документы в консульство
client_progress_status: "Подано"
```

---

## 📋 Правильные сочетания статусов

| Сценарий                           | `step_status` | `client_progress_status`      | Объяснение                                |
|-----------------------------------|---------------|-------------------------------|-------------------------------------------|
| **Новый оплаченный клиент**       | `0`           | `"Оплатили пакет услуг"`      | Оплатил, но анкету не начинал             |
| **Заполняет анкету**              | `3`           | `"Прошли опросник"`           | Прошел 3/9 шагов анкеты                  |
| **Завершил анкету**               | `9`           | `"Прошли опросник"`           | Полностью заполнил анкету                 |
| **Ожидает документы**             | `9`           | `"Ожидает приглашения"`       | Анкета готова, ждет документы             |
| **Подал в консульство**           | `9`           | `"Подано"`                    | Все готово и подано                       |

---

## 🛠️ Реализация в коде

### При создании оплаченного клиента
```typescript
// ✅ ПРАВИЛЬНО
const newApplication = {
  step_status: 0, // Еще не заполнял анкету
  client_progress_status: "Оплатили пакет услуг", // Бизнес-статус
  // ...
};

// ❌ НЕПРАВИЛЬНО  
const newApplication = {
  step_status: CLIENT_STEP_STATUS.PACKAGE_PAID, // НЕ ДЕЛАТЬ ТАК!
  client_progress_status: "Оплатили пакет услуг",
  // ...
};
```

### При обновлении прогресса анкеты
```typescript
// Клиент заполнил 6-й шаг анкеты
const update = {
  step_status: 6, // Технический прогресс
  client_progress_status: "Прошли опросник", // Бизнес-статус
};
```

### При смене бизнес-этапа
```typescript
// Клиент перешел к следующему этапу (документы поданы)
const update = {
  step_status: 9, // Анкета полностью заполнена
  client_progress_status: "Подано", // Новый бизнес-этап
};
```

---

## 🚫 Что НЕ ДЕЛАТЬ

### ❌ Не используйте step_status для бизнес-логики
```typescript
// НЕВЕРНО:
if (client.step_status === CLIENT_STEP_STATUS.PACKAGE_PAID) {
  // Логика для оплаченных клиентов
}

// ПРАВИЛЬНО:
if (client.client_progress_status === "Оплатили пакет услуг") {
  // Логика для оплаченных клиентов  
}
```

### ❌ Не смешивайте технический и бизнес-прогресс
```typescript
// НЕВЕРНО:
step_status: "Ожидает приглашения" // step_status должен быть числом!

// ПРАВИЛЬНО:
step_status: 7, // Технический прогресс анкеты
client_progress_status: "Ожидает приглашения" // Бизнес-этап
```

---

## 🎯 Главные принципы

1. **`step_status`** = Технология (0-9 шагов анкеты)
2. **`client_progress_status`** = Бизнес (этапы работы с клиентом)
3. **Один НЕ заменяет другой** - это разные измерения прогресса
4. **Всегда устанавливайте оба поля** при создании/обновлении клиента

---

## 📝 Файлы для справки

- `src/types/universal-status.ts` - Типы и enum'ы статусов
- `src/pages/api/admin/add-client.ts` - Пример правильного создания
- `src/utils/clientStatusValidation.ts` - Валидация статусов 