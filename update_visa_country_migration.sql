-- Migration to add visaCountry field to existing visa applications
-- This migration converts visaDestination values to normalized visaCountry codes

-- Update existing records where visaCountry is missing but visaDestination exists
UPDATE visa_applications 
SET form_data = form_data || jsonb_build_object('visaCountry', 
  CASE 
    WHEN form_data->>'visaDestination' = 'usa' THEN 'US'
    WHEN form_data->>'visaDestination' = 'uk' THEN 'UK'
    WHEN form_data->>'visaDestination' = 'canada' THEN 'Canada'
    WHEN form_data->>'visaDestination' = 'australia' THEN 'Australia'
    WHEN form_data->>'visaDestination' = 'schengen' THEN 'EU'
    -- Handle 'other' case by checking otherVisaDestination
    WHEN form_data->>'visaDestination' = 'other' AND 
         (lower(form_data->>'otherVisaDestination') LIKE '%сша%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%америк%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%usa%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%america%') THEN 'US'
    WHEN form_data->>'visaDestination' = 'other' AND 
         (lower(form_data->>'otherVisaDestination') LIKE '%британ%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%англи%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%uk%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%britain%') THEN 'UK'
    WHEN form_data->>'visaDestination' = 'other' AND 
         (lower(form_data->>'otherVisaDestination') LIKE '%канад%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%canada%') THEN 'Canada'
    WHEN form_data->>'visaDestination' = 'other' AND 
         (lower(form_data->>'otherVisaDestination') LIKE '%австрал%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%australia%') THEN 'Australia'
    WHEN form_data->>'visaDestination' = 'other' AND 
         (lower(form_data->>'otherVisaDestination') LIKE '%китай%' OR 
          lower(form_data->>'otherVisaDestination') LIKE '%china%') THEN 'CN'
    WHEN form_data->>'visaDestination' = 'other' THEN coalesce(form_data->>'otherVisaDestination', 'Other')
    -- Fallback to original value if no mapping found
    ELSE coalesce(form_data->>'visaDestination', 'US')
  END
)
WHERE 
  -- Only update records that don't have visaCountry but have visaDestination
  (form_data->>'visaCountry' IS NULL OR form_data->>'visaCountry' = '') 
  AND form_data->>'visaDestination' IS NOT NULL 
  AND form_data->>'visaDestination' != '';

-- Update records that have neither visaCountry nor visaDestination but might have country info
UPDATE visa_applications 
SET form_data = form_data || jsonb_build_object('visaCountry', 'US')
WHERE 
  (form_data->>'visaCountry' IS NULL OR form_data->>'visaCountry' = '') 
  AND (form_data->>'visaDestination' IS NULL OR form_data->>'visaDestination' = '')
  AND step_status >= 3; -- Only for applications that have progressed beyond initial steps

-- Create index for faster filtering by visaCountry if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_visa_applications_visa_country 
ON visa_applications USING GIN ((form_data->>'visaCountry'));

-- Verify the migration results
SELECT 
  form_data->>'visaDestination' as visa_destination,
  form_data->>'visaCountry' as visa_country,
  count(*) as count
FROM visa_applications 
WHERE form_data->>'visaDestination' IS NOT NULL OR form_data->>'visaCountry' IS NOT NULL
GROUP BY form_data->>'visaDestination', form_data->>'visaCountry'
ORDER BY count DESC; 