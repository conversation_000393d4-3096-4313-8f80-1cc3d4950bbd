const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixTestTestovDate() {
  console.log('🔧 Fixing Test Testov creation date...\n');
  
  // Test Testov ID from previous check
  const testTestovId = '619bed1b-3b39-4faf-af3e-1eedd70e3821';
  
  // Since Test Testov completed step 9, set created_at to updated_at (which should be the submission time)
  const { data: current, error: fetchError } = await supabase
    .from('visa_applications')
    .select('updated_at, step_status')
    .eq('id', testTestovId)
    .single();
    
  if (fetchError) {
    console.error('❌ Error fetching current data:', fetchError);
    return;
  }
  
  console.log('Current Test Testov data:');
  console.log(`  Step Status: ${current.step_status}`);
  console.log(`  Updated At: ${current.updated_at}`);
  
  // Set created_at to be the same as updated_at (which represents when they actually submitted)
  const { data, error } = await supabase
    .from('visa_applications')
    .update({
      created_at: current.updated_at // Use the existing updated_at as the creation time
    })
    .eq('id', testTestovId)
    .select();
    
  if (error) {
    console.error('❌ Error updating:', error);
    return;
  }
  
  console.log('✅ Successfully updated Test Testov creation date');
  console.log('Updated record:');
  console.log(`  New Created At: ${data[0].created_at}`);
  console.log(`  Updated At: ${data[0].updated_at}`);
  
  // Verify the fix
  const newDate = new Date(data[0].created_at);
  console.log(`\n📅 New creation date: ${newDate.toLocaleDateString('ru-RU')} ${newDate.toLocaleTimeString('ru-RU')}`);
}

fixTestTestovDate().catch(console.error);