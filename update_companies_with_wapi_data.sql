-- Migration: Add WhatsApp API fields and populate with dummy data
-- Execute this script in your Supabase SQL editor

-- Step 1: Add WhatsApp API fields to companies table
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS wapi_token TEXT,
ADD COLUMN IF NOT EXISTS wapi_profile_id TEXT,
ADD COLUMN IF NOT EXISTS wapi_webhook_url TEXT;

-- Add comments for documentation
COMMENT ON COLUMN public.companies.wapi_token IS 'WhatsApp API Token for integration';
COMMENT ON COLUMN public.companies.wapi_profile_id IS 'WhatsApp API Profile ID';
COMMENT ON COLUMN public.companies.wapi_webhook_url IS 'WhatsApp API Webhook URL for callbacks';

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_companies_wapi_profile_id ON public.companies(wapi_profile_id) WHERE wapi_profile_id IS NOT NULL;

-- Step 2: Update existing companies with dummy WhatsApp API data
-- Update Visa Pro Kazakhstan
UPDATE public.companies 
SET 
  wapi_token = 'vp_token_9a8b7c6d5e4f3g2h1i0j',
  wapi_profile_id = 'visa_pro_profile_001',
  wapi_webhook_url = 'https://api.visapro.kz/webhook/whatsapp'
WHERE name = 'Visa Pro Kazakhstan';

-- Update Elite Visa Consulting  
UPDATE public.companies 
SET 
  wapi_token = 'ev_token_1j2k3l4m5n6o7p8q9r0s',
  wapi_profile_id = 'elite_visa_profile_002', 
  wapi_webhook_url = 'https://api.elitevisa.kz/webhook/whatsapp'
WHERE name = 'Elite Visa Consulting';

-- Update Globalis Kazakhstan
UPDATE public.companies 
SET 
  wapi_token = 'gl_token_s0r9q8p7o6n5m4l3k2j1',
  wapi_profile_id = 'globalis_profile_003',
  wapi_webhook_url = 'https://api.globalis.kz/webhook/whatsapp'
WHERE name = 'Globalis Kazakhstan';

-- Step 3: If companies don't exist, create them with WhatsApp API data
INSERT INTO public.companies (id, name, phone_number, email, slug, wapi_token, wapi_profile_id, wapi_webhook_url, is_blocked, created_at, updated_at) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'Visa Pro Kazakhstan', '****** 123 4567', '<EMAIL>', 'visa-pro-kazakhstan', 'vp_token_9a8b7c6d5e4f3g2h1i0j', 'visa_pro_profile_001', 'https://api.visapro.kz/webhook/whatsapp', false, NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440002', 'Elite Visa Consulting', '****** 987 6543', '<EMAIL>', 'elite-visa-consulting', 'ev_token_1j2k3l4m5n6o7p8q9r0s', 'elite_visa_profile_002', 'https://api.elitevisa.kz/webhook/whatsapp', false, NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440003', 'Globalis Kazakhstan', '****** 456 7890', '<EMAIL>', 'globalis-kz', 'gl_token_s0r9q8p7o6n5m4l3k2j1', 'globalis_profile_003', 'https://api.globalis.kz/webhook/whatsapp', false, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  wapi_token = EXCLUDED.wapi_token,
  wapi_profile_id = EXCLUDED.wapi_profile_id,
  wapi_webhook_url = EXCLUDED.wapi_webhook_url,
  updated_at = NOW();

-- Step 4: Verify the data
SELECT 
  id,
  name,
  slug,
  wapi_token,
  wapi_profile_id,
  wapi_webhook_url,
  is_blocked,
  created_at
FROM public.companies 
ORDER BY created_at DESC; 