const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function debugApplications() {
  console.log('🔍 Debugging applications visibility issue...\n');

  try {
    // 1. Check all companies first
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('*')
      .order('created_at', { ascending: false });

    if (companiesError) {
      console.error('❌ Error fetching companies:', companiesError);
      return;
    }

    console.log('📋 All companies in database:');
    companies.forEach(company => {
      console.log(`  - ID: ${company.id}`);
      console.log(`    Name: ${company.name}`);
      console.log(`    Slug: ${company.slug}`);
      console.log(`    Blocked: ${company.is_blocked}`);
      console.log(`    Created: ${new Date(company.created_at).toLocaleString()}`);
      console.log('');
    });

    // Find specific companies
    const visaProKz = companies.find(c => c.slug === 'visa-pro-kz');
    const company123 = companies.find(c => c.slug === '123');

    console.log('🎯 Target companies:');
    if (visaProKz) {
      console.log(`  ✅ visa-pro-kz found: ID ${visaProKz.id}, blocked: ${visaProKz.is_blocked}`);
    } else {
      console.log('  ❌ visa-pro-kz NOT found');
    }
    
    if (company123) {
      console.log(`  ✅ Company 123 found: ID ${company123.id}, blocked: ${company123.is_blocked}`);
    } else {
      console.log('  ❌ Company 123 NOT found');
    }

    // 2. Check ALL applications in database
    const { data: allApps, error: allAppsError } = await supabase
      .from('visa_applications')
      .select(`
        id,
        agent_id,
        form_data,
        step_status,
        company_id,
        service_payment_status,
        visa_status,
        client_progress_status,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .limit(50); // Get last 50 applications

    if (allAppsError) {
      console.error('❌ Error fetching applications:', allAppsError);
      return;
    }

    console.log(`\n📊 Total applications found: ${allApps.length}`);
    console.log('\n📋 Recent applications (last 50):');
    
    let visaProCount = 0;
    let company123Count = 0;
    let nullCompanyCount = 0;
    let otherCompanyCount = 0;

    allApps.forEach((app, index) => {
      const name = `${app.form_data?.name || app.form_data?.firstName || ''} ${app.form_data?.surname || app.form_data?.lastName || ''}`.trim() || 'Unknown';
      const phone = app.form_data?.phone || app.form_data?.phoneNumber || 'No phone';
      const visaCountry = app.form_data?.visaDestination || app.form_data?.visaCountry || 'No country';
      
      console.log(`  ${index + 1}. ${name} (${phone})`);
      console.log(`     Agent: ${app.agent_id}`);
      console.log(`     Company ID: ${app.company_id || 'NULL'}`);
      console.log(`     Step: ${app.step_status}, Payment: ${app.service_payment_status}`);
      console.log(`     Visa: ${visaCountry}, Status: ${app.visa_status || 'none'}`);
      console.log(`     Created: ${new Date(app.created_at).toLocaleString()}`);
      
      // Count by company
      if (app.company_id === visaProKz?.id) {
        visaProCount++;
        console.log(`     🎯 MATCHES visa-pro-kz`);
      } else if (app.company_id === company123?.id) {
        company123Count++;
        console.log(`     🎯 MATCHES company 123`);
      } else if (!app.company_id) {
        nullCompanyCount++;
        console.log(`     ⚠️  NULL company_id`);
      } else {
        otherCompanyCount++;
        const companyName = companies.find(c => c.id === app.company_id)?.name || 'Unknown';
        console.log(`     📌 Other company: ${companyName} (${app.company_id})`);
      }
      console.log('');
    });

    console.log('📈 Summary by company:');
    console.log(`  - visa-pro-kz (${visaProKz?.id}): ${visaProCount} applications`);
    console.log(`  - company 123 (${company123?.id}): ${company123Count} applications`);
    console.log(`  - NULL company_id: ${nullCompanyCount} applications`);
    console.log(`  - Other companies: ${otherCompanyCount} applications`);

    // 3. Test the admin API exactly as the dashboard would call it
    console.log('\n🔧 Testing admin API calls...');

    // Test for all companies (superadmin view)
    const testAllResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/applications?companyId=all&_t=${Date.now()}`);
    if (testAllResponse.ok) {
      const allData = await testAllResponse.json();
      console.log(`📡 API test (all companies): ${allData.applications.length} applications, total: ${allData.total}`);
    } else {
      console.log('❌ API test (all companies) failed:', testAllResponse.status);
    }

    // Test for visa-pro-kz specifically
    if (visaProKz) {
      const testVisaProResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/applications?companyId=${visaProKz.id}&_t=${Date.now()}`);
      if (testVisaProResponse.ok) {
        const visaProData = await testVisaProResponse.json();
        console.log(`📡 API test (visa-pro-kz): ${visaProData.applications.length} applications, total: ${visaProData.total}`);
      } else {
        console.log('❌ API test (visa-pro-kz) failed:', testVisaProResponse.status);
      }
    }

    // Test for company 123 specifically
    if (company123) {
      const test123Response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/applications?companyId=${company123.id}&_t=${Date.now()}`);
      if (test123Response.ok) {
        const data123 = await test123Response.json();
        console.log(`📡 API test (company 123): ${data123.applications.length} applications, total: ${data123.total}`);
      } else {
        console.log('❌ API test (company 123) failed:', test123Response.status);
      }
    }

    // 4. Check for blocked companies
    const blockedCompanies = companies.filter(c => c.is_blocked);
    if (blockedCompanies.length > 0) {
      console.log('\n⚠️  Blocked companies found:');
      blockedCompanies.forEach(c => {
        console.log(`  - ${c.name} (${c.slug}) - ID: ${c.id}`);
      });
    }

    // 5. Check recent applications with detailed form_data
    console.log('\n🔍 Checking form_data for recent applications...');
    const recentApps = allApps.slice(0, 5);
    recentApps.forEach((app, index) => {
      console.log(`\n${index + 1}. Application ${app.id}:`);
      console.log(`   Agent ID: ${app.agent_id}`);
      console.log(`   Company ID: ${app.company_id}`);
      console.log(`   Step Status: ${app.step_status}`);
      console.log(`   Form Data Keys: ${Object.keys(app.form_data || {}).join(', ')}`);
      
      if (app.form_data) {
        console.log(`   Name fields: ${JSON.stringify({
          name: app.form_data.name,
          firstName: app.form_data.firstName,
          surname: app.form_data.surname,
          lastName: app.form_data.lastName,
          fullNameCyrillic: app.form_data.fullNameCyrillic
        })}`);
        
        console.log(`   Contact: ${JSON.stringify({
          phone: app.form_data.phone,
          phoneNumber: app.form_data.phoneNumber,
          email: app.form_data.email
        })}`);
        
        console.log(`   Visa: ${JSON.stringify({
          visaDestination: app.form_data.visaDestination,
          visaCountry: app.form_data.visaCountry,
          country: app.form_data.country
        })}`);
      }
    });

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the debug
debugApplications();