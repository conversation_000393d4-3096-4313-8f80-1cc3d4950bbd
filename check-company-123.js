const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkCompany123() {
  console.log('🔍 Checking company "123" applications...\n');
  
  // Get applications for company "123"
  const companyId = 'a65d3caf-de59-405e-ab14-61767839bf53';
  
  const { data: apps, error } = await supabase
    .from('visa_applications')
    .select('*')
    .eq('company_id', companyId)
    .order('created_at', { ascending: false });
    
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  console.log(`📊 Total Applications for company "123": ${apps.length}\n`);
  
  // Find Test Testov
  const testTestov = apps.find(app => {
    const name = `${app.form_data?.name || ''} ${app.form_data?.surname || ''}`.trim();
    return name.toLowerCase().includes('testov') || name.toLowerCase().includes('test');
  });
  
  if (testTestov) {
    console.log('✅ Test Testov found in company "123":');
    console.log(`   Name: ${testTestov.form_data?.name} ${testTestov.form_data?.surname}`);
    console.log(`   Status: ${testTestov.client_progress_status}`);
    console.log(`   Payment: ${testTestov.service_payment_status}`);
    console.log(`   Should appear in "Прошли опросник" column ✅`);
  } else {
    console.log('❌ Test Testov NOT found in company "123"');
  }
  
  // Count "Прошли опросник" applications
  const paidApps = apps.filter(app => 
    app.client_progress_status === 'Прошли опросник'
  );
  
  console.log(`\n📋 "Прошли опросник" applications in company "123": ${paidApps.length}`);
  paidApps.forEach((app, index) => {
    const name = `${app.form_data?.name || ''} ${app.form_data?.surname || ''}`.trim() || 'Unknown';
    console.log(`   ${index + 1}. ${name} - Payment: ${app.service_payment_status}`);
  });
  
  // Get company info
  const { data: company } = await supabase
    .from('companies')
    .select('*')
    .eq('id', companyId)
    .single();
    
  if (company) {
    console.log(`\n🏢 Company Details:`);
    console.log(`   Name: ${company.name}`);
    console.log(`   Slug: ${company.slug}`);
    console.log(`   Blocked: ${company.is_blocked}`);
  }
}

checkCompany123().catch(console.error);