const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkTestTestov() {
  console.log('🔍 Checking Test Testov application...\n');
  
  const { data: apps, error } = await supabase
    .from('visa_applications')
    .select('*')
    .order('created_at', { ascending: false });
    
  // Filter in JavaScript since Supabase doesn't support JSONB ilike
  const testTestovApps = apps ? apps.filter(app => {
    const name = app.form_data?.name || '';
    const surname = app.form_data?.surname || '';
    return name.toLowerCase().includes('test') || surname.toLowerCase().includes('testov');
  }) : [];
    
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  console.log(`Found ${testTestovApps.length} Test Testov applications:\n`);
  
  testTestovApps.forEach((app, index) => {
    const name = `${app.form_data?.name || ''} ${app.form_data?.surname || ''}`.trim();
    console.log(`${index + 1}. ${name}`);
    console.log(`   ID: ${app.id}`);
    console.log(`   Agent: ${app.agent_id}`);
    console.log(`   Company ID: ${app.company_id}`);
    console.log(`   Step Status: ${app.step_status}`);
    console.log(`   Client Progress Status: ${app.client_progress_status}`);
    console.log(`   Service Payment Status: ${app.service_payment_status}`);
    console.log(`   Visa Status: ${app.visa_status}`);
    console.log(`   Created: ${new Date(app.created_at).toLocaleString()}`);
    console.log(`   Form Data Occupation: ${app.form_data?.occupation}`);
    console.log(`   Form Data DOB: ${app.form_data?.dateOfBirth}`);
    console.log('');
  });
}

checkTestTestov().catch(console.error);