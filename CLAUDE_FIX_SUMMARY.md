# 🔧 Claude Vision OCR - Исправление проблемы

## ❌ Проблема

Claude Vision API возвращал ошибку:
```
Claude Vision API error: Invalid bearer token
```

## ✅ Решение

### 1. Основная проблема - неправильный заголовок авторизации

**Было:**
```javascript
headers: {
  'Authorization': `Bearer ${config.apiKey}`,
  'anthropic-version': '2023-06-01'
}
```

**Стало:**
```javascript
headers: {
  'x-api-key': config.apiKey,
  'anthropic-version': '2023-06-01'
}
```

### 2. Что изменилось

1. **Заменил `Authorization: Bearer` на `x-api-key`**
   - Anthropic API использует `x-api-key` вместо стандартного Bearer token
   - Это официальный формат аутентификации Anthropic API

2. **Добавил валидацию формата ключа**
   - Проверка, что ключ начинается с `sk-ant-api03-`
   - Предупреждение, если формат неправильный

3. **Улучшил обработку ошибок**
   - Более подробные сообщения об ошибках
   - Специальные сообщения для 401, 400, 429 кодов
   - Вывод HTTP статуса для диагностики

### 3. Файлы изменены

- `src/utils/claude-vision-ocr.ts` - исправлен заголовок авторизации
- `scripts/test-claude-api.js` - создан тест для проверки API

## 🧪 Тестирование

Создан тест для проверки API connectivity:
```bash
node scripts/test-claude-api.js
```

Результат:
```
✅ API Key found: sk-ant-api03-9cnRAVh...
✅ Key format: Valid
✅ API Test successful!
Response: API test successful
```

## 🎯 Итог

Теперь Claude Vision OCR должен работать корректно! Основная проблема была в неправильном формате заголовка авторизации. Anthropic API требует `x-api-key` вместо `Authorization: Bearer`.

## 🔄 Следующие шаги

1. Перезапустить Next.js dev server
2. Протестировать загрузку документа
3. Проверить, что Claude OCR работает в первую очередь
4. Убедиться в корректном fallback на Mistral/Tesseract