# 🚀 Supabase Database Migration Instructions

## Overview
This migration script will fix all missing fields, relationships, and data in your Supabase database to resolve the company filtering and application management issues.

## What This Migration Does

### ✅ **Database Structure Fixes**
- Adds all missing columns to `visa_applications` table:
  - `company_id` (links applications to companies)
  - `service_payment_status` 
  - `service_package_id`
  - `visa_status`
  - `client_progress_status`
  - `manual_fix_status`
  - `consular_fee_paid`
  - `requires_fixes`
  - `fix_comment`
  - `invitation_file_url`
  - `added_by_manager`
  - `last_updated`

### ✅ **Creates Complete Table Structure**
- `companies` table with sample companies (Visa Pro Kz, Globalis, Elite Visa)
- `employees` table with admin users and proper roles
- `price_list` table with service packages for each company
- All necessary indexes for optimal performance

### ✅ **Fixes Data Issues**
- Assigns existing applications to companies (distributed evenly)
- Sets proper default values for all new fields
- Creates proper relationships between tables

## How to Run the Migration

### Step 1: Open Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor** in the left sidebar

### Step 2: Execute the Migration Script
1. Open the file `supabase-complete-migration.sql` in a text editor
2. Copy the entire content of the file
3. Paste it into the Supabase SQL Editor
4. Click **Run** to execute the migration

### Step 3: Verify the Migration
The script will show a summary with:
- Number of companies created
- Number of employees created  
- Number of price packages created
- Number of applications updated

You should see output like:
```
✅ MIGRATION COMPLETED SUCCESSFULLY!
📊 Database Summary:
   • Companies: 3
   • Employees: 4
   • Price Packages: 14
   • Total Applications: [your current count]
   • Applications with Company: [your current count]

🔐 Default Login Credentials:
   • Super Admin: <EMAIL> / password
   • Visa Pro Kz: <EMAIL> / password
   • Globalis: <EMAIL> / password
   • Elite Visa: <EMAIL> / password
```

## Expected Results After Migration

### ✅ **Company Filtering Fixed**
- "все компании" will now show ALL applications (highest count)
- Individual companies will show subset of applications
- Country cards will display accurate counts

### ✅ **Admin Panel Fully Functional**
- All company management features working
- Client status tracking working
- Payment status tracking working
- File management working

### ✅ **Login Credentials Ready**
Use these credentials to test the admin panel:
- **Super Admin**: `<EMAIL>` / `password`
- **Visa Pro Kz**: `<EMAIL>` / `password` 
- **Globalis**: `<EMAIL>` / `password`
- **Elite Visa**: `<EMAIL>` / `password`

## Safety Features

- ✅ **IF NOT EXISTS** logic prevents duplicate columns
- ✅ **ON CONFLICT** handling prevents duplicate data
- ✅ **Preserves existing data** - no data loss
- ✅ **Can be run multiple times safely**

## After Migration

1. **Test Company Filtering**: Check that "все компании" shows more applications than individual companies
2. **Test Admin Login**: Login with the provided credentials
3. **Verify Data**: Check that existing applications now have company assignments
4. **Check Performance**: All indexes are created for optimal query performance

## Troubleshooting

If you encounter any issues:
1. Check the Supabase logs for error details
2. Ensure you have the correct permissions in Supabase
3. The script can be run multiple times safely if needed

## Next Steps

After successful migration:
1. Your company filtering issue will be resolved
2. All admin panel features will work properly  
3. Applications will be properly distributed among companies
4. You can start using the full admin system

🎉 **Ready to test the fixed system!**
