# Извлечение данных паспорта с Mistral OCR

Этот модуль позволяет извлекать полные данные паспорта (имя, фамилию, даты, номер, национальность) из документов, используя Mistral OCR с fallback на Tesseract.

## Функциональность

### ✅ Что поддерживается:

1. **Извлечение из MRZ строки** (Machine Readable Zone)

   - Имя и фамилия: `P<SURNAME<<FIRSTNAME<<<<<<<<<<<<<<<<`
   - Даты в формате YYMMDD (дата рождения, окончание действия)
   - Номер паспорта: `N162240532KAZ0306148M3401044...`
   - Автоматическое определение века для дат (00-30 = 2000-2030, 31-99 = 1931-1999)

2. **Извлечение из обычного текста**

   - Поиск по ключевым словам: "Name", "Surname", "Имя", "Фамилия", "Дата рождения", etc.
   - Поддержка нескольких языков (английский, русский, казахский)
   - Различные форматы дат: DD.MM.YYYY, DD/MM/YYYY, DD MMM YYYY, русские месяцы
   - Контекстное определение типа даты (рождение, выдача, окончание)
   - Номера паспортов, национальность

3. **Fallback на Tesseract**
   - Если Mistral OCR не смог найти данные
   - Обрабатывает изображения в base64 из ответа Mistral
   - Использует существующую конфигурацию Tesseract с многоязычной поддержкой

## Основные функции

### `parseDocumentNameData()`

**Самый простой способ** - обрабатывает документ и извлекает имя/фамилию одним вызовом:

```typescript
import { parseDocumentNameData } from "./src/utils/mistral-ocr";

const result = await parseDocumentNameData(
  "/path/to/passport.jpg", // путь к файлу
  "image", // 'image' или 'pdf'
  "passport.jpg" // опционально: оригинальное имя файла
);

console.log({
  name: result.name, // "SUNNATULLO"
  surname: result.surname, // "KAZERGASHOV"
  dateOfBirth: result.dateOfBirth, // "03.06.2014"
  passportNumber: result.passportNumber, // "N162240532"
  passportExpiryDate: result.passportExpiryDate, // "04.01.2034"
  nationality: result.nationality, // "ӨЗБЕК"
  source: result.source, // "mistral" или "tesseract"
});
```

### `parseNameFromMistralOcr()`

Для более тонкого контроля - принимает готый результат OCR:

```typescript
import {
  processDocumentWithMistral,
  parseNameFromMistralOcr,
} from "./src/utils/mistral-ocr";

// Сначала обрабатываем документ
const ocrResult = await processDocumentWithMistral(
  "/path/to/file.jpg",
  "image"
);

// Затем извлекаем имя/фамилию
const nameData = await parseNameFromMistralOcr(ocrResult);
```

## Типы данных

```typescript
interface ParsedNameData {
  name?: string; // Имя
  surname?: string; // Фамилия
  dateOfBirth?: string; // Дата рождения
  passportIssueDate?: string; // Дата выдачи паспорта
  passportExpiryDate?: string; // Дата окончания действия
  passportNumber?: string; // Номер паспорта
  nationality?: string; // Национальность
  rawText: string; // Полный текст (для отладки)
  source: "mistral" | "tesseract"; // Источник данных
}
```

## Алгоритм работы

1. **Пробует извлечь из MRZ строки** (машиночитаемая зона паспорта)
2. **Пробует найти в обычном тексте** по ключевым словам
3. **Если частично найдено** - пробует Tesseract для недостающих частей
4. **Если ничего не найдено** - полностью переключается на Tesseract

## Настройки

Убедитесь что в конфигурации включены изображения:

```typescript
// src/utils/mistral-ocr.ts
const DEFAULT_CONFIG = {
  baseUrl: "https://api.mistral.ai/v1/ocr",
  includeImages: true, // 👈 Должно быть true для fallback на Tesseract
  timeout: 60000,
};
```

## Требования

- **MISTRAL_API_KEY** в переменных окружения
- Tesseract.js для fallback функционала
- Node.js с поддержкой Blob/File API

## Примеры использования

### Реальные данные от Mistral OCR:

```
# МӘЛІМЕТ / OBSERVATIONS

ҰЛТЫ / НАЦИОНАЛЬНОСТЬ
ӨЗБЕК / УЗБЕК

P<KAZERGASHOV<<SUNNATULLO<<<<<<<<<<<<<<<<<<< N162240532KAZ0306148M3401044030614501193<<10
```

**Результат:**

- `surname: "KAZERGASHOV"`
- `name: "SUNNATULLO"`
- `dateOfBirth: "03.06.2014"` (из MRZ: 030614)
- `passportExpiryDate: "04.01.2034"` (из MRZ: 340104)
- `passportNumber: "N162240532"`
- `nationality: "ӨЗБЕК"` (из текста)
- `source: "mistral"`

### Если Mistral OCR не смог извлечь:

1. Функция автоматически возьмет изображения из `response.images[]`
2. Конвертирует base64 в формат для Tesseract
3. Обработает через Tesseract с поддержкой многоязычности
4. Попробует найти все данные паспорта в результате Tesseract

## Отладка

Все функции имеют подробное логирование:

- Источник данных (Mistral/Tesseract)
- Найденные паттерны
- Длина текста
- Количество изображений
- Ошибки и предупреждения

## Ошибки

- `Invalid Mistral OCR result` - неверный формат входных данных
- `No passport data found and no images available` - нет данных для Tesseract fallback
- `Failed to extract passport data from both Mistral OCR and Tesseract` - ни один метод не сработал

## Особенности работы с датами

### MRZ формат (YYMMDD):

- `030614` → `03.06.2014` (год 14 интерпретируется как 2014)
- `340104` → `04.01.2034` (год 34 интерпретируется как 1934, но скорее всего это ошибка OCR)

### Контекстное определение дат:

- Ищет ключевые слова: "birth", "рождения", "issue", "выдачи", "expiry", "действует до"
- Сортирует по хронологии если контекст не найден
- Поддерживает множество форматов: DD.MM.YYYY, DD/MM/YYYY, DD MMM YYYY

### Определение национальности:

- Извлекает из текста рядом с ключевыми словами "НАЦИОНАЛЬНОСТЬ", "ҰЛТЫ"
- Пример: "ӨЗБЕК" (узбек на казахском языке)
