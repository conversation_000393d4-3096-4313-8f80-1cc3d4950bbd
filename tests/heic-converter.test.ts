import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  isHeicFile, 
  isHeicFileBySignature, 
  processHeicFile, 
  convertHeicToJpeg,
  getOptimalJpegQuality 
} from '../src/utils/heic-converter';
import fs from 'fs';

// Mock dependencies
vi.mock('fs');
vi.mock('heic-convert');

const mockFs = vi.mocked(fs);

describe('HEIC Converter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('isHeicFile', () => {
    it('should detect HEIC files by extension', () => {
      expect(isHeicFile('/path/to/image.heic')).toBe(true);
      expect(isHeicFile('/path/to/image.HEIC')).toBe(true);
      expect(isHeicFile('/path/to/image.heif')).toBe(true);
      expect(isHeicFile('/path/to/image.HEIF')).toBe(true);
    });

    it('should not detect non-HEIC files', () => {
      expect(isHeicFile('/path/to/image.jpg')).toBe(false);
      expect(isHeicFile('/path/to/image.png')).toBe(false);
      expect(isHeicFile('/path/to/document.pdf')).toBe(false);
      expect(isHeicFile('/path/to/file.txt')).toBe(false);
    });
  });

  describe('isHeicFileBySignature', () => {
    it('should detect HEIC files by signature', async () => {
      const heicSignature = Buffer.from([
        0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63
      ]);

      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer && length >= 12) {
          heicSignature.copy(buffer, 0, 0, 12);
        }
        return 12;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await isHeicFileBySignature('/test/image.heic');
      expect(result).toBe(true);
    });

    it('should detect HEIF files by signature', async () => {
      const heifSignature = Buffer.from([
        0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x6D, 0x69, 0x66, 0x31
      ]);

      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer && length >= 12) {
          heifSignature.copy(buffer, 0, 0, 12);
        }
        return 12;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await isHeicFileBySignature('/test/image.heif');
      expect(result).toBe(true);
    });

    it('should not detect non-HEIC files by signature', async () => {
      const jpegSignature = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]);

      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer) {
          jpegSignature.copy(buffer, 0, 0, Math.min(buffer.length, jpegSignature.length));
        }
        return jpegSignature.length;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await isHeicFileBySignature('/test/image.jpg');
      expect(result).toBe(false);
    });

    it('should handle file read errors', async () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockImplementation(() => {
        throw new Error('File read error');
      });

      const result = await isHeicFileBySignature('/test/image.heic');
      expect(result).toBe(false);
    });

    it('should handle non-existent files', async () => {
      mockFs.existsSync.mockReturnValue(false);

      const result = await isHeicFileBySignature('/test/nonexistent.heic');
      expect(result).toBe(false);
    });
  });

  describe('getOptimalJpegQuality', () => {
    it('should return lower quality for large files', () => {
      expect(getOptimalJpegQuality(6 * 1024 * 1024)).toBe(0.6); // > 5MB
      expect(getOptimalJpegQuality(3 * 1024 * 1024)).toBe(0.7); // > 2MB
      expect(getOptimalJpegQuality(1 * 1024 * 1024)).toBe(0.8); // < 2MB
    });
  });

  describe('processHeicFile', () => {
    it('should return original path for non-HEIC files', async () => {
      const jpegSignature = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]);

      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer) {
          jpegSignature.copy(buffer, 0, 0, Math.min(buffer.length, jpegSignature.length));
        }
        return jpegSignature.length;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await processHeicFile('/test/image.jpg');

      expect(result.success).toBe(true);
      expect(result.wasConverted).toBe(false);
      expect(result.convertedPath).toBe('/test/image.jpg');
      expect(result.originalPath).toBe('/test/image.jpg');
    });

    it('should handle conversion errors gracefully', async () => {
      const heicSignature = Buffer.from([
        0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63
      ]);

      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer && length >= 12) {
          heicSignature.copy(buffer, 0, 0, 12);
        }
        return 12;
      });
      mockFs.closeSync.mockImplementation(() => {});
      mockFs.readFileSync.mockImplementation(() => {
        throw new Error('File read error');
      });

      const result = await processHeicFile('/test/image.heic');

      expect(result.success).toBe(false);
      expect(result.wasConverted).toBe(false);
      expect(result.error).toContain('File read error');
    });
  });

  describe('convertHeicToJpeg', () => {
    it('should handle conversion errors', async () => {
      mockFs.readFileSync.mockImplementation(() => {
        throw new Error('File read error');
      });

      await expect(convertHeicToJpeg('/test/image.heic')).rejects.toThrow('Failed to convert HEIC to JPEG');
    });
  });

  describe('Integration scenarios', () => {
    it('should handle the complete HEIC processing workflow', async () => {
      // Mock HEIC file detection
      const heicSignature = Buffer.from([
        0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63
      ]);

      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer && length >= 12) {
          heicSignature.copy(buffer, 0, 0, 12);
        }
        return 12;
      });
      mockFs.closeSync.mockImplementation(() => {});

      // Mock file reading for conversion (but expect it to fail in test environment)
      mockFs.readFileSync.mockImplementation(() => {
        throw new Error('heic-convert not available in test environment');
      });

      const result = await processHeicFile('/test/image.heic');

      // Should detect as HEIC but fail conversion in test environment
      expect(result.success).toBe(false);
      expect(result.originalPath).toBe('/test/image.heic');
      expect(result.wasConverted).toBe(false);
      expect(result.error).toContain('heic-convert not available in test environment');
    });

    it('should handle mixed file types correctly', async () => {
      const testCases = [
        { path: '/test/image.jpg', shouldConvert: false },
        { path: '/test/image.png', shouldConvert: false },
        { path: '/test/image.heic', shouldConvert: true },
        { path: '/test/document.pdf', shouldConvert: false }
      ];

      for (const testCase of testCases) {
        // Mock appropriate signatures
        let signature: Buffer;
        if (testCase.path.includes('.heic')) {
          signature = Buffer.from([0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63]);
        } else if (testCase.path.includes('.jpg')) {
          signature = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]);
        } else if (testCase.path.includes('.png')) {
          signature = Buffer.from([0x89, 0x50, 0x4E, 0x47]);
        } else {
          signature = Buffer.from([0x25, 0x50, 0x44, 0x46]); // PDF
        }

        mockFs.existsSync.mockReturnValue(true);
        mockFs.openSync.mockReturnValue(3 as any);
        mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
          if (buffer instanceof Buffer) {
            const copyLength = Math.min(buffer.length, signature.length);
            signature.copy(buffer, 0, 0, copyLength);
          }
          return signature.length;
        });
        mockFs.closeSync.mockImplementation(() => {});

        if (testCase.shouldConvert) {
          // Mock conversion failure for test environment
          mockFs.readFileSync.mockImplementation(() => {
            throw new Error('Conversion test');
          });
        }

        const result = await processHeicFile(testCase.path);

        if (testCase.shouldConvert) {
          // Should attempt conversion but fail in test environment
          expect(result.success).toBe(false);
          expect(result.error).toContain('Conversion test');
        } else {
          // Should not attempt conversion
          expect(result.success).toBe(true);
          expect(result.wasConverted).toBe(false);
          expect(result.convertedPath).toBe(testCase.path);
        }
      }
    });
  });
});
