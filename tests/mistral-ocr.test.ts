import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { processDocumentWithMistral, processImageWithMistral, processPdfWithMistral } from '../src/utils/mistral-ocr';
import { uploadTempFile } from '../src/utils/file-storage';

// Mock dependencies
vi.mock('../src/utils/file-storage');
vi.mock('fs');

const mockUploadTempFile = vi.mocked(uploadTempFile);

describe('Mistral OCR', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Set up environment variables
    process.env.MISTRAL_API_KEY = 'test-api-key';
    
    // Mock fetch globally
    global.fetch = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('processImageWithMistral', () => {
    it('should successfully process an image', async () => {
      // Mock temp file upload
      const mockCleanup = vi.fn();
      mockUploadTempFile.mockResolvedValue({
        path: 'temp/test-image.jpg',
        url: 'https://example.com/temp/test-image.jpg',
        cleanup: mockCleanup
      });

      // Mock successful API response
      const mockResponse = {
        id: 'test-id',
        object: 'ocr',
        created: Date.now(),
        model: 'mistral-ocr-latest',
        usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 },
        choices: [{
          index: 0,
          message: { role: 'assistant', content: 'Extracted text from image' },
          finish_reason: 'stop'
        }],
        images: []
      };

      (global.fetch as any).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await processImageWithMistral('/test/image.jpg', 'image.jpg');

      expect(result.success).toBe(true);
      expect(result.text).toBe('Extracted text from image');
      expect(mockUploadTempFile).toHaveBeenCalledWith('/test/image.jpg', 'image.jpg');
      expect(mockCleanup).toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      // Mock temp file upload
      const mockCleanup = vi.fn();
      mockUploadTempFile.mockResolvedValue({
        path: 'temp/test-image.jpg',
        url: 'https://example.com/temp/test-image.jpg',
        cleanup: mockCleanup
      });

      // Mock API error response
      (global.fetch as any).mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({
          error: { message: 'API Error', type: 'invalid_request' }
        })
      });

      const result = await processImageWithMistral('/test/image.jpg');

      expect(result.success).toBe(false);
      expect(result.error).toContain('API Error');
      expect(mockCleanup).toHaveBeenCalled();
    });

    it('should handle missing API key', async () => {
      delete process.env.MISTRAL_API_KEY;

      const result = await processImageWithMistral('/test/image.jpg');

      expect(result.success).toBe(false);
      expect(result.error).toContain('MISTRAL_API_KEY');
    });
  });

  describe('processPdfWithMistral', () => {
    it('should successfully process a PDF', async () => {
      // Mock temp file upload
      const mockCleanup = vi.fn();
      mockUploadTempFile.mockResolvedValue({
        path: 'temp/test-document.pdf',
        url: 'https://example.com/temp/test-document.pdf',
        cleanup: mockCleanup
      });

      // Mock successful API response
      const mockResponse = {
        id: 'test-id',
        object: 'ocr',
        created: Date.now(),
        model: 'mistral-ocr-latest',
        usage: { prompt_tokens: 15, completion_tokens: 25, total_tokens: 40 },
        choices: [{
          index: 0,
          message: { role: 'assistant', content: 'Extracted text from PDF' },
          finish_reason: 'stop'
        }],
        images: []
      };

      (global.fetch as any).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await processPdfWithMistral('/test/document.pdf', 'document.pdf');

      expect(result.success).toBe(true);
      expect(result.text).toBe('Extracted text from PDF');
      expect(mockUploadTempFile).toHaveBeenCalledWith('/test/document.pdf', 'document.pdf');
      expect(mockCleanup).toHaveBeenCalled();
    });
  });

  describe('processDocumentWithMistral', () => {
    it('should route to image processing for image files', async () => {
      // Mock temp file upload
      const mockCleanup = vi.fn();
      mockUploadTempFile.mockResolvedValue({
        path: 'temp/test-image.jpg',
        url: 'https://example.com/temp/test-image.jpg',
        cleanup: mockCleanup
      });

      // Mock successful API response
      const mockResponse = {
        id: 'test-id',
        object: 'ocr',
        created: Date.now(),
        model: 'mistral-ocr-latest',
        usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 },
        choices: [{
          index: 0,
          message: { role: 'assistant', content: 'Image text' },
          finish_reason: 'stop'
        }]
      };

      (global.fetch as any).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await processDocumentWithMistral('/test/image.jpg', 'image');

      expect(result.success).toBe(true);
      expect(result.text).toBe('Image text');
    });

    it('should route to PDF processing for PDF files', async () => {
      // Mock temp file upload
      const mockCleanup = vi.fn();
      mockUploadTempFile.mockResolvedValue({
        path: 'temp/test-document.pdf',
        url: 'https://example.com/temp/test-document.pdf',
        cleanup: mockCleanup
      });

      // Mock successful API response
      const mockResponse = {
        id: 'test-id',
        object: 'ocr',
        created: Date.now(),
        model: 'mistral-ocr-latest',
        usage: { prompt_tokens: 15, completion_tokens: 25, total_tokens: 40 },
        choices: [{
          index: 0,
          message: { role: 'assistant', content: 'PDF text' },
          finish_reason: 'stop'
        }]
      };

      (global.fetch as any).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await processDocumentWithMistral('/test/document.pdf', 'pdf');

      expect(result.success).toBe(true);
      expect(result.text).toBe('PDF text');
    });
  });

  describe('API Request Structure', () => {
    it('should send correct request for image processing', async () => {
      // Mock temp file upload
      const mockCleanup = vi.fn();
      mockUploadTempFile.mockResolvedValue({
        path: 'temp/test-image.jpg',
        url: 'https://example.com/temp/test-image.jpg',
        cleanup: mockCleanup
      });

      // Mock successful API response
      (global.fetch as any).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'test' } }]
        })
      });

      await processImageWithMistral('/test/image.jpg');

      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.mistral.ai/v1/ocr',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key'
          },
          body: JSON.stringify({
            model: 'mistral-ocr-latest',
            document: {
              type: 'image_url',
              image_url: 'https://example.com/temp/test-image.jpg'
            },
            include_image_base64: true
          })
        })
      );
    });

    it('should send correct request for PDF processing', async () => {
      // Mock temp file upload
      const mockCleanup = vi.fn();
      mockUploadTempFile.mockResolvedValue({
        path: 'temp/test-document.pdf',
        url: 'https://example.com/temp/test-document.pdf',
        cleanup: mockCleanup
      });

      // Mock successful API response
      (global.fetch as any).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'test' } }]
        })
      });

      await processPdfWithMistral('/test/document.pdf');

      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.mistral.ai/v1/ocr',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key'
          },
          body: JSON.stringify({
            model: 'mistral-ocr-latest',
            document: {
              type: 'document_url',
              document_url: 'https://example.com/temp/test-document.pdf'
            },
            include_image_base64: true
          })
        })
      );
    });
  });
});
