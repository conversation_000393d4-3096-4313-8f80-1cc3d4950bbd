# Admin Panel API Endpoints

This document lists all the API endpoints created for the Visa AI Admin Panel.

## Environment Variables Required

Make sure you have these environment variables set in your `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## API Endpoints

### Authentication
- `POST /api/admin/auth` - Admin login with hardcoded credentials

### Applications/Clients
- `GET /api/admin/applications` - Get all applications with filtering
- `PUT /api/admin/applications` - Update application status
- `POST /api/admin/clients` - Create new client
- `PUT /api/admin/clients` - Update existing client
- `DELETE /api/admin/clients/[clientId]` - Delete client
- `PUT /api/admin/clients/[clientId]` - Update specific client
- `PUT /api/admin/clients/[clientId]/workflow` - Update client workflow status

### Analytics
- `GET /api/admin/analytics` - Get analytics data

### Companies (Super Admin only)
- `GET /api/admin/companies` - Get all companies
- `POST /api/admin/companies` - Create new company
- `PUT /api/admin/companies` - Update company
- `DELETE /api/admin/companies` - Delete company

### Employees
- `GET /api/admin/companies/[companyId]/employees` - Get employees for company
- `POST /api/admin/companies/[companyId]/employees` - Add new employee
- `PUT /api/admin/employees/[employeeId]` - Update employee status
- `DELETE /api/admin/employees/[employeeId]` - Delete employee

### Settings
- `PUT /api/admin/profile` - Update admin profile
- `GET /api/admin/preferences` - Get admin preferences
- `PUT /api/admin/preferences` - Update admin preferences

## Data Integration

The APIs integrate with Supabase for:
- **visa_applications table**: Stores all client applications
- **Future tables**: companies, employees, admin_profiles (to be created)

## Admin Credentials

Static credentials for testing:
- **Super Admin**: `superadmin` / `admin123`
- **Visa Admin**: `visaadmin` / `visa123`

## Features Implemented

✅ **Authentication**: Role-based access (Super Admin / Visa Admin)
✅ **Client Management**: Full CRUD operations on visa applications
✅ **Advanced Filtering**: 10+ filter types including income, profession, history
✅ **Workflow Management**: Manual status progression with visual indicators
✅ **Analytics**: KPIs, charts, period-based statistics
✅ **Company Management**: Super admin can manage visa companies
✅ **Employee Management**: Company admins can manage their employees
✅ **Settings**: Profile and preferences management
✅ **Similar Cases**: Shows successful cases for sales reference

## Notes

- Some endpoints use mock data (companies, employees) - these would be moved to Supabase in production
- Client data is stored in the existing `visa_applications` table
- All endpoints include proper error handling and validation
- APIs support pagination and advanced filtering 