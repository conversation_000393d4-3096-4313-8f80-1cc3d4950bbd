// Test script for country normalization function
// Extracted from src/types/client-status.ts

const COUNTRY_MAPPINGS = [
  {
    code: 'US',
    displayName: 'США',
    variations: ['США', 'US', 'USA', 'United States', 'America', 'Америка', 'usa']
  },
  {
    code: 'UK', 
    displayName: 'Великобритания',
    variations: ['Великобритания', 'UK', 'United Kingdom', 'Britain', 'Британия', 'uk']
  },
  {
    code: 'EU',
    displayName: 'Германия',
    variations: ['Германия', 'Франция', 'Италия', 'Испания', 'Шенген', 'Germany', 'France', 'Italy', 'Spain', 'Schengen', 'EU', 'schengen']
  },
  {
    code: 'CN',
    displayName: 'Китай', 
    variations: ['Китай', 'CN', 'China']
  }
];

const normalizeCountryName = (country) => {
  if (!country) return '';
  
  // First try to find direct match
  const directMapping = COUNTRY_MAPPINGS.find(m => 
    m.variations.includes(country)
  );
  
  if (directMapping) {
    return directMapping.code;
  }
  
  // Try partial match
  const partialMapping = COUNTRY_MAPPINGS.find(m => 
    m.variations.some(v => 
      v.toLowerCase().includes(country.toLowerCase()) ||
      country.toLowerCase().includes(v.toLowerCase())
    )
  );
  
  return partialMapping?.code || country;
};

// Test cases
const testCases = [
  { input: "США", expected: "US", description: "Russian for USA" },
  { input: "United States", expected: "US", description: "English USA" },
  { input: "Великобритания", expected: "UK", description: "Russian for UK" },
  { input: "Германия", expected: "EU", description: "Russian for Germany" },
  // Additional test cases for better coverage
  { input: "usa", expected: "US", description: "Lowercase usa" },
  { input: "uk", expected: "UK", description: "Lowercase uk" },
  { input: "Germany", expected: "EU", description: "English Germany" },
  { input: "France", expected: "EU", description: "English France" },
  { input: "Italy", expected: "EU", description: "English Italy" },
  { input: "Spain", expected: "EU", description: "English Spain" },
  { input: "China", expected: "CN", description: "English China" },
  { input: "Китай", expected: "CN", description: "Russian for China" },
  { input: "NonExistentCountry", expected: "NonExistentCountry", description: "Unknown country should return as-is" },
  { input: "", expected: "", description: "Empty string should return empty" }
];

console.log("=".repeat(60));
console.log("COUNTRY NORMALIZATION TEST RESULTS");
console.log("=".repeat(60));

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const result = normalizeCountryName(testCase.input);
  const success = result === testCase.expected;
  
  console.log(`\nTest ${index + 1}: ${testCase.description}`);
  console.log(`Input: "${testCase.input}"`);
  console.log(`Expected: "${testCase.expected}"`);
  console.log(`Actual: "${result}"`);
  console.log(`Status: ${success ? "✅ PASS" : "❌ FAIL"}`);
  
  if (success) {
    passed++;
  } else {
    failed++;
  }
});

console.log("\n" + "=".repeat(60));
console.log("SUMMARY");
console.log("=".repeat(60));
console.log(`Total tests: ${testCases.length}`);
console.log(`Passed: ${passed}`);
console.log(`Failed: ${failed}`);
console.log(`Success rate: ${((passed / testCases.length) * 100).toFixed(1)}%`);

if (failed === 0) {
  console.log("\n🎉 All tests passed! Country normalization is working correctly.");
} else {
  console.log(`\n⚠️  ${failed} test(s) failed. Please check the normalization logic.`);
}

console.log("\n" + "=".repeat(60));