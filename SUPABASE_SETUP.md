# 🚀 Supabase Configuration for Visa AI Admin Panel

## Required Environment Variables

Create a `.env.local` file in your project root with the following variables:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase Service Role Key (Required for Admin User Creation)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## How to Get These Values

1. **Go to your Supabase Dashboard**: https://app.supabase.com/
2. **Select your project** or create a new one
3. **Go to Settings > API**
4. **Copy the values**:
   - `Project URL` → `NEXT_PUBLIC_SUPABASE_URL`
   - `anon public` key → `NEXT_PUBLIC_SUPABASE_ANON_KEY`  
   - `service_role` key → `SUPABASE_SERVICE_ROLE_KEY`

## ⚠️ Important Notes

- **Service Role Key**: Required for creating admin users in Supabase Auth
- **Keep Service Role Key Secret**: Never commit it to version control
- **Test the Integration**: After setting up, test creating a new company to verify admin user creation

## Features Using Supabase

✅ **Admin User Creation**: When creating new visa companies, admin users are automatically created in Supabase Auth  
✅ **User Roles**: Users get `visa_admin` role with company metadata  
✅ **Admin Deletion**: When companies are deleted, associated admin users are removed from Supabase  
✅ **Client Data Storage**: Client information stored securely  

## Troubleshooting

- **401 Unauthorized**: Check if SUPABASE_SERVICE_ROLE_KEY is set correctly
- **404 Not Found**: Verify NEXT_PUBLIC_SUPABASE_URL is correct
- **Company Creation Fails**: Ensure all environment variables are set

## Testing

1. Create a new company through the admin panel
2. Check Supabase Auth dashboard to see if admin user was created
3. Verify user metadata contains role and company information

## API Endpoints Using Supabase

- `POST /api/admin/companies` - Creates company + admin user
- `DELETE /api/admin/companies` - Deletes company + admin user  
- `POST /api/admin/clients` - Creates client record
- `DELETE /api/admin/clients/[clientId]` - Deletes client record 