-- Migration: Sync Client Status Based on Payment
-- Date: 2024-12-19
-- Purpose: Update clients who have paid for a package to show correct status

-- Start transaction for safety
BEGIN;

-- Log the current state before changes
SELECT 
    COUNT(*) as total_paid_clients,
    COUNT(CASE WHEN client_progress_status = 'прошли опросник' THEN 1 END) as incorrect_status_count
FROM visa_applications 
WHERE service_payment_status = 'оплачено';

-- Update client_progress_status for paid clients who still show 'прошли опросник'
UPDATE visa_applications 
SET 
    client_progress_status = 'оплатили пакет',
    updated_at = NOW()
WHERE 
    service_payment_status = 'оплачено' 
    AND client_progress_status = 'прошли опросник';

-- Log the number of updated rows
SELECT 
    ROW_COUNT() as rows_updated,
    'client_progress_status updated to оплатили пакет for paid clients' as description;

-- Verify the changes
SELECT 
    COUNT(*) as total_paid_clients,
    COUNT(CASE WHEN client_progress_status = 'оплатили пакет' THEN 1 END) as correct_status_count,
    COUNT(CASE WHEN client_progress_status = 'прошли опросник' THEN 1 END) as remaining_incorrect_count
FROM visa_applications 
WHERE service_payment_status = 'оплачено';

-- Commit the transaction
COMMIT;

-- Additional verification query (run separately)
-- SELECT id, agent_id, service_payment_status, client_progress_status, step_status, updated_at
-- FROM visa_applications 
-- WHERE service_payment_status = 'оплачено' 
-- ORDER BY updated_at DESC 
-- LIMIT 10; 