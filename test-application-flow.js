const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Generate a unique test agent ID
const generateTestAgentId = () => {
  return 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// Mock form data for testing
const createMockFormData = () => ({
  // Step 1 data
  visaDestination: 'usa',
  passportNumber: 'N12345678',
  passportIssueDate: '2020-01-01',
  passportExpiryDate: '2030-01-01',
  passportCountry: 'Kazakhstan',
  
  // Step 2 data  
  name: 'Test',
  surname: 'User',
  gender: 'male',
  birthDate: '1990-01-01',
  birthPlace: 'Almaty',
  
  // Step 3 data
  phone: '+77001234567',
  email: '<EMAIL>',
  address: 'Test Address 123',
  city: 'Almaty',
  postalCode: '050000',
  country: 'Kazakhstan',
  
  // Additional data to make it look complete
  maritalStatus: 'single',
  occupation: 'computer_science',
  educationStatus: 'completed_education',
  institutionName: 'Test University',
  visitedCountries: ['Turkey', 'UAE']
});

async function testApplicationSubmissionFlow() {
  console.log('🧪 Testing Application Submission Flow...\n');
  
  const testAgentId = generateTestAgentId();
  const mockFormData = createMockFormData();
  
  try {
    // Step 1: Check companies exist
    console.log('1️⃣ Checking if test companies exist...');
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('id, name, slug, is_blocked')
      .in('slug', ['visa-pro-kz', '123']);
    
    if (companiesError) {
      console.error('❌ Error fetching companies:', companiesError);
      return;
    }
    
    console.log('📋 Found companies:');
    companies.forEach(company => {
      console.log(`  - ${company.name} (${company.slug}) - ID: ${company.id}, blocked: ${company.is_blocked}`);
    });
    
    const testCompany = companies.find(c => c.slug === 'visa-pro-kz') || companies[0];
    if (!testCompany) {
      console.error('❌ No test companies found. Please create companies first.');
      return;
    }
    
    console.log(`✅ Using company: ${testCompany.name} (ID: ${testCompany.id})\n`);
    
    // Step 2: Simulate form submission through steps 3-10
    console.log('2️⃣ Simulating form submission...');
    
    const steps = [3, 5, 7, 10]; // Test key steps
    
    for (const step of steps) {
      console.log(`📝 Submitting step ${step}...`);
      
      // Call the same API endpoint that the form uses
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/save-form-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId: testAgentId,
          formData: mockFormData,
          step: step,
          companyId: testCompany.id,
          uploadedFiles: {}
        }),
      });
      
      if (!response.ok) {
        console.error(`❌ Failed to submit step ${step}:`, response.status, response.statusText);
        
        // Try direct database insert as fallback
        console.log(`🔄 Trying direct database insert for step ${step}...`);
        const { data: directData, error: directError } = await supabase
          .from('visa_applications')
          .upsert({
            agent_id: testAgentId,
            step_status: step,
            form_data: mockFormData,
            uploaded_files: {},
            phone_number: '+77001234567',
            company_id: testCompany.id,
            client_progress_status: step >= 10 ? 'Подано' : step >= 7 ? 'Заполнение анкеты + обучение' : step >= 5 ? 'Сбор информации ботом' : 'Прошли опросник',
            service_payment_status: 'не оплачено',
            visa_status: 'ожидает',
            consular_fee_paid: false,
            requires_fixes: false,
            whatsapp_redirected: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }, {
            onConflict: 'agent_id'
          })
          .select();
          
        if (directError) {
          console.error(`❌ Direct insert also failed for step ${step}:`, directError);
          continue;
        } else {
          console.log(`✅ Direct insert successful for step ${step}`);
        }
      } else {
        const result = await response.json();
        console.log(`✅ Step ${step} submitted successfully`);
      }
      
      // Small delay between steps
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n3️⃣ Verifying application was saved...');
    
    // Check if application exists in database
    const { data: savedApp, error: fetchError } = await supabase
      .from('visa_applications')
      .select(`
        id,
        agent_id,
        step_status,
        form_data,
        company_id,
        client_progress_status,
        service_payment_status,
        visa_status,
        consular_fee_paid,
        requires_fixes,
        whatsapp_redirected,
        created_at,
        updated_at
      `)
      .eq('agent_id', testAgentId)
      .single();
    
    if (fetchError) {
      console.error('❌ Error fetching saved application:', fetchError);
      return;
    }
    
    if (!savedApp) {
      console.error('❌ Application was not saved to database');
      return;
    }
    
    console.log('✅ Application saved successfully:');
    console.log(`  - Agent ID: ${savedApp.agent_id}`);
    console.log(`  - Step Status: ${savedApp.step_status}`);
    console.log(`  - Company ID: ${savedApp.company_id}`);
    console.log(`  - Client Progress: ${savedApp.client_progress_status}`);
    console.log(`  - Payment Status: ${savedApp.service_payment_status}`);
    console.log(`  - Visa Status: ${savedApp.visa_status}`);
    console.log(`  - Consular Fee: ${savedApp.consular_fee_paid}`);
    console.log(`  - Requires Fixes: ${savedApp.requires_fixes}`);
    console.log(`  - WhatsApp Redirected: ${savedApp.whatsapp_redirected}`);
    console.log(`  - Created: ${new Date(savedApp.created_at).toLocaleString()}`);
    
    const name = `${savedApp.form_data?.name || ''} ${savedApp.form_data?.surname || ''}`.trim();
    console.log(`  - Name: ${name}`);
    console.log(`  - Phone: ${savedApp.form_data?.phone || 'N/A'}`);
    console.log(`  - Email: ${savedApp.form_data?.email || 'N/A'}`);
    
    console.log('\n4️⃣ Testing admin API visibility...');
    
    // Test admin API call for all companies
    console.log('📡 Testing admin API (all companies)...');
    const allResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/applications?companyId=all&_t=${Date.now()}`);
    
    if (allResponse.ok) {
      const allData = await allResponse.json();
      const foundInAll = allData.applications.find(app => app.agent_id === testAgentId);
      console.log(`📊 All companies API: ${allData.applications.length} total applications`);
      console.log(`🎯 Test application found in all companies: ${foundInAll ? '✅ YES' : '❌ NO'}`);
      
      if (foundInAll) {
        console.log(`  - Status in API: ${foundInAll.client_progress_status}`);
        console.log(`  - Payment in API: ${foundInAll.service_payment_status}`);
        console.log(`  - Company in API: ${foundInAll.company_id}`);
      }
    } else {
      console.log('❌ Admin API (all companies) failed:', allResponse.status);
    }
    
    // Test admin API call for specific company
    console.log('\n📡 Testing admin API (specific company)...');
    const companyResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/applications?companyId=${testCompany.id}&_t=${Date.now()}`);
    
    if (companyResponse.ok) {
      const companyData = await companyResponse.json();
      const foundInCompany = companyData.applications.find(app => app.agent_id === testAgentId);
      console.log(`📊 Company-specific API: ${companyData.applications.length} applications for ${testCompany.name}`);
      console.log(`🎯 Test application found in company filter: ${foundInCompany ? '✅ YES' : '❌ NO'}`);
      
      if (foundInCompany) {
        console.log(`  - Status in company API: ${foundInCompany.client_progress_status}`);
        console.log(`  - Payment in company API: ${foundInCompany.service_payment_status}`);
      }
    } else {
      console.log('❌ Admin API (company-specific) failed:', companyResponse.status);
    }
    
    // Test with no date filter
    console.log('\n📡 Testing admin API (no date filter)...');
    const noDateResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/applications?companyId=all&noDateFilter=true&_t=${Date.now()}`);
    
    if (noDateResponse.ok) {
      const noDateData = await noDateResponse.json();
      const foundNoDate = noDateData.applications.find(app => app.agent_id === testAgentId);
      console.log(`📊 No date filter API: ${noDateData.applications.length} applications`);
      console.log(`🎯 Test application found with no date filter: ${foundNoDate ? '✅ YES' : '❌ NO'}`);
    } else {
      console.log('❌ Admin API (no date filter) failed:', noDateResponse.status);
    }
    
    console.log('\n5️⃣ Summary:');
    console.log(`🆔 Test Agent ID: ${testAgentId}`);
    console.log(`🏢 Company Used: ${testCompany.name} (${testCompany.id})`);
    console.log(`📱 Phone: ${mockFormData.phone}`);
    console.log(`✉️  Email: ${mockFormData.email}`);
    console.log(`👤 Name: ${mockFormData.name} ${mockFormData.surname}`);
    
    console.log('\n🎯 To manually verify in admin dashboard:');
    console.log(`1. Go to admin dashboard`);
    console.log(`2. Select company: ${testCompany.name}`);
    console.log(`3. Look for: ${mockFormData.name} ${mockFormData.surname}`);
    console.log(`4. Phone should be: ${mockFormData.phone}`);
    console.log(`5. Agent ID: ${testAgentId}`);
    
    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    const { error: deleteError } = await supabase
      .from('visa_applications')
      .delete()
      .eq('agent_id', testAgentId);
    
    if (deleteError) {
      console.log('⚠️  Could not clean up test data:', deleteError.message);
      console.log('   Please manually delete application with agent_id:', testAgentId);
    } else {
      console.log('✅ Test data cleaned up successfully');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testApplicationSubmissionFlow();