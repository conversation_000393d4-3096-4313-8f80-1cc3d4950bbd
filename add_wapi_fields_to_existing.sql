-- Add WhatsApp API fields to existing companies table
-- Run this in your Supabase SQL Editor

-- Step 1: Add the WhatsApp API columns
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS wapi_token TEXT,
ADD COLUMN IF NOT EXISTS wapi_profile_id TEXT,
ADD COLUMN IF NOT EXISTS wapi_webhook_url TEXT;

-- Step 2: Add comments for documentation
COMMENT ON COLUMN public.companies.wapi_token IS 'WhatsApp API Token for integration';
COMMENT ON COLUMN public.companies.wapi_profile_id IS 'WhatsApp API Profile ID';
COMMENT ON COLUMN public.companies.wapi_webhook_url IS 'WhatsApp API Webhook URL for callbacks';

-- Step 3: Create index for performance
CREATE INDEX IF NOT EXISTS idx_companies_wapi_profile_id ON public.companies(wapi_profile_id) WHERE wapi_profile_id IS NOT NULL;

-- Step 4: Update existing companies with dummy WhatsApp API data
UPDATE public.companies 
SET 
  wapi_token = CASE 
    WHEN name = 'Visa Pro Kazakhstan' THEN 'vp_token_9a8b7c6d5e4f3g2h1i0j'
    WHEN name = 'Elite Visa Consulting' THEN 'ev_token_1j2k3l4m5n6o7p8q9r0s'
    WHEN name = 'Globalis Kazakhstan' THEN 'gl_token_s0r9q8p7o6n5m4l3k2j1'
    WHEN name = '123' THEN 'test_token_123_xyz'
    WHEN name = 'Yersultan Co' THEN 'yco_token_abc123def456'
    ELSE 'dummy_token_' || substr(id::text, 1, 8)
  END,
  wapi_profile_id = CASE 
    WHEN name = 'Visa Pro Kazakhstan' THEN 'visa_pro_profile_001'
    WHEN name = 'Elite Visa Consulting' THEN 'elite_visa_profile_002'
    WHEN name = 'Globalis Kazakhstan' THEN 'globalis_profile_003'
    WHEN name = '123' THEN 'test_profile_123'
    WHEN name = 'Yersultan Co' THEN 'yco_profile_001'
    ELSE 'profile_' || substr(id::text, 1, 8)
  END,
  wapi_webhook_url = CASE 
    WHEN name = 'Visa Pro Kazakhstan' THEN 'https://api.visapro.kz/webhook/whatsapp'
    WHEN name = 'Elite Visa Consulting' THEN 'https://api.elitevisa.kz/webhook/whatsapp'
    WHEN name = 'Globalis Kazakhstan' THEN 'https://api.globalis.kz/webhook/whatsapp'
    WHEN name = '123' THEN 'https://api.test123.kz/webhook/whatsapp'
    WHEN name = 'Yersultan Co' THEN 'https://api.yersultan.co/webhook/whatsapp'
    ELSE 'https://api.' || lower(replace(name, ' ', '')) || '.kz/webhook/whatsapp'
  END,
  updated_at = NOW()
WHERE wapi_token IS NULL;

-- Step 5: Verify the update
SELECT 
  id,
  name,
  slug,
  wapi_token,
  wapi_profile_id,
  wapi_webhook_url,
  updated_at
FROM public.companies 
ORDER BY created_at DESC; 