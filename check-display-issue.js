const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkDisplayIssue() {
  console.log('🔍 Checking display discrepancy issue...\n');
  
  // Get applications for a specific company (Visa Pro Kazakhstan)
  const companyId = '550e8400-e29b-41d4-a716-446655440001';
  
  const { data: apps, error } = await supabase
    .from('visa_applications')
    .select('*')
    .eq('company_id', companyId)
    .order('created_at', { ascending: false });
    
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  console.log(`📊 Total Applications for Visa Pro Kazakhstan: ${apps.length}\n`);
  
  // Count "Прошли опросник" with unpaid status
  const unpaidQuestionnaires = apps.filter(app => 
    app.client_progress_status === 'Прошли опросник' && 
    app.service_payment_status === 'не оплачено'
  );
  
  console.log(`💰 "Прошли опросник" (Unpaid): ${unpaidQuestionnaires.length}`);
  console.log('Sample applications:');
  unpaidQuestionnaires.slice(0, 10).forEach((app, index) => {
    const name = `${app.form_data?.name || ''} ${app.form_data?.surname || ''}`.trim() || 'Unknown';
    console.log(`   ${index + 1}. ${name} - Created: ${new Date(app.created_at).toLocaleDateString()}`);
  });
  
  // Count "Требуют правок"
  const requiresFixes = apps.filter(app => app.requires_fixes === true);
  
  console.log(`\n🔧 Requires Fixes: ${requiresFixes.length}`);
  console.log('Sample applications:');
  requiresFixes.slice(0, 10).forEach((app, index) => {
    const name = `${app.form_data?.name || ''} ${app.form_data?.surname || ''}`.trim() || 'Unknown';
    console.log(`   ${index + 1}. ${name} - Status: ${app.client_progress_status}, Created: ${new Date(app.created_at).toLocaleDateString()}`);
  });
  
  // Check date range
  const today = new Date();
  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(today.getFullYear() - 1);
  
  const recentApps = apps.filter(app => new Date(app.created_at) > oneYearAgo);
  const olderApps = apps.filter(app => new Date(app.created_at) <= oneYearAgo);
  
  console.log(`\n📅 Date Distribution:`);
  console.log(`   - Last 12 months: ${recentApps.length}`);
  console.log(`   - Older than 12 months: ${olderApps.length}`);
  
  // Check step_status distribution
  const stepCounts = {};
  apps.forEach(app => {
    stepCounts[app.step_status] = (stepCounts[app.step_status] || 0) + 1;
  });
  
  console.log(`\n📈 Step Status Distribution:`);
  Object.entries(stepCounts).sort(([a], [b]) => parseInt(a) - parseInt(b)).forEach(([step, count]) => {
    console.log(`   - Step ${step}: ${count}`);
  });
}

checkDisplayIssue().catch(console.error);