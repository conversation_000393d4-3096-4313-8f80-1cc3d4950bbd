# Internationalization (i18n) Guide

## Overview
This application supports Russian (default) and English languages using the next-i18next library.

## Language Structure

### Translation Files Location
```
public/locales/
├── ru/                     # Russian translations
│   ├── common.json         # Common UI elements, auth, countries
│   ├── navigation.json     # Sidebar navigation items
│   ├── clients.json        # Client management interface
│   └── companies.json      # Company management interface
└── en/                     # English translations
    ├── common.json         # English equivalents
    ├── navigation.json     # English navigation
    ├── clients.json        # English client management
    └── companies.json      # English company management
```

## Usage in Components

### Basic Translation Hook
```typescript
import { useTranslation } from 'next-i18next';

function MyComponent() {
  const { t } = useTranslation('common'); // Load 'common' namespace
  
  return (
    <div>
      <h1>{t('dashboard.title')}</h1>
      <button>{t('common.save')}</button>
    </div>
  );
}
```

### Multiple Namespaces
```typescript
const { t } = useTranslation(['common', 'navigation', 'clients']);

// Usage:
t('common:loading')          // From common.json
t('navigation:dashboard')    // From navigation.json  
t('clients:title')          // From clients.json
```

### With Fallback Values
```typescript
// If translation key doesn't exist, show fallback
t('some.missing.key', 'Default Text')
```

## Language Switching

### Using the Language Switcher Component
```typescript
import LanguageSwitcher from '../common/LanguageSwitcher';

<LanguageSwitcher 
  className="w-full" 
  showFlags={true} 
  showText={false}
/>
```

### Using the Language Hook
```typescript
import { useLanguage } from '../../hooks/useLanguage';

function MyComponent() {
  const { 
    currentLanguage,    // 'ru' or 'en'
    isRussian,         // boolean
    isEnglish,         // boolean
    changeLanguage,    // function to change language
    toggleLanguage     // function to toggle between ru/en
  } = useLanguage();
  
  return (
    <button onClick={() => changeLanguage('en')}>
      Switch to English
    </button>
  );
}
```

## Server-Side Rendering (SSR)

### For Static Pages (getStaticProps)
```typescript
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'navigation'])),
    },
  };
}
```

### For Dynamic Pages (getServerSideProps)
```typescript
export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'clients', 'companies'])),
    },
  };
}
```

## Adding New Translations

### 1. Add to Translation Files
Add the new key to both `ru/` and `en/` files:

**ru/common.json:**
```json
{
  "newFeature": {
    "title": "Новая функция",
    "description": "Описание новой функции"
  }
}
```

**en/common.json:**
```json
{
  "newFeature": {
    "title": "New Feature", 
    "description": "Description of the new feature"
  }
}
```

### 2. Use in Component
```typescript
const { t } = useTranslation('common');

<h2>{t('newFeature.title')}</h2>
<p>{t('newFeature.description')}</p>
```

## Translation Key Conventions

### Naming Structure
- Use dot notation: `section.subsection.key`
- Keep keys descriptive: `dashboard.pendingPayments` not `dashboard.pp`
- Group related translations: `auth.login`, `auth.logout`, `auth.email`

### Common Namespaces
- **common**: UI elements, buttons, status, countries, validation
- **navigation**: Sidebar menu items, breadcrumbs
- **clients**: Client management, workflow states, forms
- **companies**: Company management, employee roles
- **auth**: Login, logout, authentication messages

## Language Routing

### URL Structure
- Default (Russian): `/admin/dashboard`
- English: `/en/admin/dashboard`
- Explicit Russian: `/ru/admin/dashboard`

### Programmatic Navigation
```typescript
import { useRouter } from 'next/router';

const router = useRouter();

// Change language and stay on same page
router.push(router.asPath, router.asPath, { locale: 'en' });
```

## Configuration

### Default Language
Set in `next-i18next.config.js`:
```javascript
module.exports = {
  i18n: {
    defaultLocale: 'ru',    // Default language
    locales: ['ru', 'en'],  // Supported languages
    localeDetection: false, // Disable auto-detection
  },
  fallbackLng: 'ru',       // Fallback if translation missing
};
```

### Adding New Languages
1. Add locale to `next-i18next.config.js`
2. Create new folder in `public/locales/`
3. Copy and translate JSON files
4. Update `LanguageSwitcher` component

## Best Practices

### 1. Always Use Translation Keys
❌ **Don't:**
```typescript
<button>Сохранить</button>
```

✅ **Do:**
```typescript
<button>{t('common.save')}</button>
```

### 2. Organize by Feature
❌ **Don't:** Put everything in one file
✅ **Do:** Separate by feature/page (`clients.json`, `companies.json`)

### 3. Consistent Key Naming
❌ **Don't:** `btn_save`, `SaveButton`, `save_btn`
✅ **Do:** `common.save`, `actions.save`

### 4. Include Fallbacks for Dynamic Content
```typescript
// For user-generated content that might not be translated
{t('status.active', user.status)}
```

### 5. Test Both Languages
Always test functionality in both Russian and English to ensure:
- Text fits in UI components
- No missing translations
- Proper text direction and formatting

## Troubleshooting

### Missing Translation Warnings
Check browser console for missing translation keys and add them to appropriate JSON files.

### Language Not Switching
Ensure `getServerSideProps` or `getStaticProps` includes all required namespaces for the page.

### SSR Hydration Issues
Make sure translation namespaces match between server and client by including them in page-level translation loading. 