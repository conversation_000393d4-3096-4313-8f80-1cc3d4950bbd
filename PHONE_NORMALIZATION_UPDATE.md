# Обновление нормализации телефонных номеров

## Описание изменений

Реализована система нормализации телефонных номеров, которая обеспечивает сохранение всех телефонов в едином формате `+7XXXXXXXXXXX` без пробелов, скобок и других символов форматирования.

## Что было изменено

### 1. Создана функция нормализации (`src/utils/validations.ts`)

Добавлена функция `normalizePhoneNumber()`, которая:
- Удаляет все символы кроме цифр и знака `+`
- Преобразует номера, начинающиеся с `8`, в формат с `+7`
- Добавляет код страны `+7` для номеров без кода страны
- Обрезает лишние цифры до максимум 11 символов (включая код страны)
- Возвращает номер в формате `+7XXXXXXXXXXX`

### 2. Обновлено сохранение в базу данных (`src/utils/supabase.ts`)

- Все телефонные номера теперь нормализуются перед сохранением в поле `phone_number`
- Поиск по телефону также использует нормализованный формат
- Обеспечена консистентность данных в базе

### 3. Обновлен WhatsApp API (`src/pages/api/send-whatsapp.ts`)

- Используется та же функция нормализации для обеспечения консистентности
- Удаляется знак `+` только для отправки в WhatsApp API

### 4. Обновлена схема базы данных

- Добавлено поле `phone_number` в таблицу `visa_applications`
- Создан SQL-скрипт для миграции существующих данных

## Примеры нормализации

| Входной формат | Результат |
|----------------|-----------|
| `****** 234 56 78` | `+77012345678` |
| `8 701 234 56 78` | `+77012345678` |
| `701 234 56 78` | `+77012345678` |
| `(701) 234-56-78` | `+77012345678` |
| `+7 (701) 234-56-78` | `+77012345678` |
| `87012345678` | `+77012345678` |
| `7012345678` | `+77012345678` |

## Файлы, которые были изменены

1. `src/utils/validations.ts` - добавлена функция нормализации
2. `src/utils/supabase.ts` - обновлены функции сохранения и поиска
3. `src/pages/api/send-whatsapp.ts` - использование нормализации
4. `supabase-setup.sql` - обновлена схема базы данных
5. `phone-normalization-migration.sql` - скрипт миграции для существующих данных
6. `src/utils/__tests__/phone-normalization.test.ts` - тесты для функции нормализации

## Миграция существующих данных

Для обновления существующих данных в базе выполните SQL-скрипт:

```bash
# В Supabase SQL Editor выполните:
cat phone-normalization-migration.sql
```

Этот скрипт:
- Добавит поле `phone_number` если его нет
- Нормализует все существующие телефоны из `form_data`
- Создаст индекс для быстрого поиска по телефону
- Покажет статистику миграции

## Тестирование

Создан набор тестов для проверки корректности нормализации различных форматов телефонных номеров. Все тесты проходят успешно.

## Обратная совместимость

- Пользовательский интерфейс остался без изменений
- Форма по-прежнему показывает телефон в удобном для пользователя формате с пробелами
- Валидация формы не изменилась
- Все существующие данные будут автоматически нормализованы при миграции

## Преимущества

1. **Консистентность данных** - все телефоны хранятся в едином формате
2. **Надежный поиск** - поиск по телефону работает независимо от формата ввода
3. **Интеграция с внешними API** - упрощена отправка в WhatsApp и другие сервисы
4. **Простота обслуживания** - единый формат упрощает работу с данными
5. **Масштабируемость** - легко добавить поддержку других стран в будущем
