# Диагностика проблем Step 2 (Загрузка документов)

## Проблема: Кнопка "Далее" не реагирует на нажатие

### Основные требования для активации кнопки "Далее":

1. **Загрузка документов**:
   - Должен быть загружен минимум ОДИН документ (паспорт ИЛИ удостоверение личности)
   - Если не загружено ни одного документа, появится желтое предупреждение

2. **Обязательные поля**:
   - ✅ **Фамилия** (только латинские буквы)
   - ✅ **Имя** (только латинские буквы)  
   - ✅ **Дата рождения**
   - ✅ **Гражданство**
   - ✅ **ИИН** (12 цифр)

3. **Условные поля** (зависят от выбранного документа):
   
   **Если указан номер паспорта:**
   - ✅ Дата выдачи паспорта (не в будущем)
   - ✅ Дата окончания паспорта (минимум 6 месяцев до истечения)
   
   **Если НЕ указан номер паспорта:**
   - ✅ Номер удостоверения личности (9 цифр)

### Как диагностировать проблему:

1. **Откройте Developer Tools (F12)**
2. **Перейдите во вкладку Console**
3. **Попробуйте нажать кнопку "Далее"**

В консоли вы увидите подробную информацию:

```javascript
// Пример вывода в консоли:
"Next button clicked"
"canGoNext: false isFormValid: false"

"Step 2 validation check:"
{
  formValid: false,
  documentUploaded: true,  
  isStepValid: false,
  formErrors: {
    iin: "ИИН должен содержать 12 цифр",
    passportExpiryDate: "Дата окончания паспорта обязательна"
  },
  formValues: {...}
}
```

### Частые проблемы и решения:

#### 1. **Не загружен документ**
**Признак**: `documentUploaded: false`
**Решение**: Загрузите паспорт или удостоверение личности

#### 2. **Неправильный формат ИИН**
**Признак**: `"ИИН должен содержать 12 цифр"`
**Решение**: Введите ровно 12 цифр без пробелов и других символов

#### 3. **Неправильный формат имени/фамилии**
**Признак**: `"Фамилия должна содержать только латинские буквы"`
**Решение**: Используйте только латинские буквы (A-Z, a-z)

#### 4. **Проблемы с датами паспорта**
**Признак**: `"Паспорт должен быть действителен минимум 6 месяцев"`
**Решение**: Убедитесь, что паспорт действителен минимум 6 месяцев с текущей даты

#### 5. **Отсутствует номер документа**
**Признак**: `"Необходимо указать номер паспорта или номер удостоверения личности"`
**Решение**: Заполните номер паспорта ИЛИ номер удостоверения личности

### Новые улучшения валидации:

- ✅ **Условная валидация**: Даты паспорта требуются только при указании номера паспорта
- ✅ **Гибкие требования**: Номер удостоверения требуется только если НЕ указан номер паспорта  
- ✅ **Улучшенные подсказки**: Добавлены подсказки к полям
- ✅ **Детальное логгирование**: Подробная информация об ошибках в консоли

### Порядок заполнения:

1. **Загрузите документ** (паспорт или удостоверение)
2. **Заполните основные поля**: Фамилия, Имя, Дата рождения, Гражданство, ИИН
3. **Заполните данные документа**:
   - Если загружали паспорт: укажите номер паспорта и даты
   - Если загружали удостоверение: укажите номер удостоверения
4. **Проверьте консоль** на наличие ошибок
5. **Нажмите "Далее"**

Если проблема сохраняется после выполнения всех требований, сохраните логи из консоли для дальнейшего анализа.