-- Проверим существующие данные
SELECT COUNT(*) FROM public.visa_applications;

-- Удаляем все существующие записи для чистого старта
DELETE FROM public.visa_applications;

-- Добавляем полные тестовые данные клиентов
INSERT INTO public.visa_applications (
  id,
  agent_id,
  form_data,
  step_status,
  phone_number,
  service_payment_status,
  visa_status,
  client_progress_status,
  consular_fee_paid,
  requires_fixes,
  created_at,
  updated_at
) VALUES 
(
  uuid_generate_v4(),
  'agent-001',
  '{
    "iin": "*********012",
    "ssn": "",
    "city": "Алматы",
    "name": "Иван",
    "email": "<EMAIL>",
    "phone": "+***********",
    "gender": "Мужской",
    "hasSSN": false,
    "income": "150000",
    "address": "ул. Абая 45, кв. 12",
    "country": "Kazakhstan",
    "endDate": "2019-06-15",
    "faculty": "Информационные технологии",
    "surname": "Петров",
    "zipCode": "050000",
    "idNumber": "*********",
    "position": "Старший разработчик",
    "groupName": "",
    "hasOffice": true,
    "hasUSVisa": false,
    "paysTaxes": true,
    "relatives": [{"name": "Анна Петрова", "relation": "жена", "inUSA": false}],
    "startDate": "2015-09-01",
    "workPhone": "+***********",
    "companions": [],
    "fatherName": "Александр",
    "motherName": "Мария",
    "occupation": "Инженер-программист",
    "otherNames": "",
    "taxpayerId": "*********012",
    "visaNumber": "",
    "citizenship": "Казахстан",
    "cityOfBirth": "Алматы",
    "companyName": "Tech Solutions KZ",
    "dateOfBirth": "1992-05-15",
    "nationality": "Казахский",
    "workAddress": "пр. Назарбаева 123, офис 45",
    "businessName": "",
    "businessType": "",
    "hasBeenToUSA": false,
    "hasEmployees": false,
    "lastVisaDate": "",
    "yearlyIncome": "1800000",
    "employeeCount": 0,
    "fatherSurname": "Петров",
    "hasOtherNames": false,
    "hasTaxpayerId": true,
    "isFatherInUSA": false,
    "isMotherInUSA": false,
    "isSameCountry": true,
    "maritalStatus": "Женат",
    "motherSurname": "Петрова",
    "officeAddress": "пр. Назарбаева 123, офис 45",
    "rejectionDate": "",
    "travelAsGroup": false,
    "travelPurpose": "Туризм",
    "businessStatus": "",
    "countryOfBirth": "Казахстан",
    "isSameVisaType": false,
    "passportNumber": "N12345678",
    "universityName": "КазНУ им. аль-Фараби",
    "visaRejections": [],
    "workExperience": "8 лет",
    "businessAddress": "",
    "businessWebsite": "",
    "fatherUSAReason": "",
    "motherUSAReason": "",
    "visaDestination": "США",
    "businessActivity": "",
    "fullNameCyrillic": "Петров Иван Александрович",
    "otherCitizenship": "",
    "socialMediaLinks": ["instagram.com/ivan_petrov"],
    "travelWithOthers": false,
    "visitedCountries": ["Турция", "ОАЭ", "Россия"],
    "fatherDateOfBirth": "1965-03-20",
    "hasBankStatements": true,
    "hasRelativesInUSA": false,
    "hasVisaRejections": false,
    "motherDateOfBirth": "1968-07-10",
    "passportIssueDate": "2020-01-15",
    "rejectionVisaType": "",
    "universityAddress": "пр. аль-Фараби 71",
    "passportExpiryDate": "2030-01-15",
    "hasOtherCitizenship": false,
    "hasPermanentContracts": true,
    "monthlyBusinessIncome": "",
    "businessExperienceYears": 0,
    "hasInternationalClients": false,
    "businessRegistrationDate": "",
    "businessRegistrationType": "",
    "permanentResidenceCountry": "Казахстан",
    "businessRegistrationNumber": "",
    "isFatherDateOfBirthUnknown": false,
    "isMotherDateOfBirthUnknown": false,
    "isPermanentResidentOtherCountry": false
  }'::jsonb,
  5,
  '+***********',
  'оплачено',
  'одобрено', 
  'документы поданы в консульство',
  true,
  false,
  '2024-12-01 10:00:00+00',
  '2024-12-01 10:00:00+00'
),
(
  uuid_generate_v4(),
  'agent-002',
  '{
    "iin": "*********098",
    "ssn": "",
    "city": "Нур-Султан",
    "name": "Анна",
    "email": "<EMAIL>",
    "phone": "+***********",
    "gender": "Женский",
    "hasSSN": false,
    "income": "200000",
    "address": "ул. Кунаева 25, кв. 78",
    "country": "Kazakhstan",
    "endDate": "2020-06-30",
    "faculty": "Медицинский",
    "surname": "Смирнова",
    "zipCode": "010000",
    "idNumber": "*********",
    "position": "Врач-терапевт",
    "groupName": "",
    "hasOffice": true,
    "hasUSVisa": false,
    "paysTaxes": true,
    "relatives": [],
    "startDate": "2016-07-01",
    "workPhone": "+***********",
    "companions": [],
    "fatherName": "Сергей",
    "motherName": "Елена",
    "occupation": "Врач-терапевт",
    "otherNames": "",
    "taxpayerId": "*********098",
    "visaNumber": "",
    "citizenship": "Казахстан",
    "cityOfBirth": "Нур-Султан",
    "companyName": "Городская больница №1",
    "dateOfBirth": "1995-08-22",
    "nationality": "Казахский",
    "workAddress": "ул. Медицинская 15",
    "businessName": "",
    "businessType": "",
    "hasBeenToUSA": false,
    "hasEmployees": false,
    "lastVisaDate": "",
    "yearlyIncome": "2400000",
    "employeeCount": 0,
    "fatherSurname": "Смирнов",
    "hasOtherNames": false,
    "hasTaxpayerId": true,
    "isFatherInUSA": false,
    "isMotherInUSA": false,
    "isSameCountry": true,
    "maritalStatus": "Не замужем",
    "motherSurname": "Смирнова",
    "officeAddress": "ул. Медицинская 15, каб. 205",
    "rejectionDate": "",
    "travelAsGroup": false,
    "travelPurpose": "Медицинская конференция",
    "businessStatus": "",
    "countryOfBirth": "Казахстан",
    "isSameVisaType": false,
    "passportNumber": "N87654321",
    "universityName": "КазНМУ им. С.Д. Асфендиярова",
    "visaRejections": [],
    "workExperience": "7 лет",
    "businessAddress": "",
    "businessWebsite": "",
    "fatherUSAReason": "",
    "motherUSAReason": "",
    "visaDestination": "США",
    "businessActivity": "",
    "fullNameCyrillic": "Смирнова Анна Сергеевна",
    "otherCitizenship": "",
    "socialMediaLinks": ["facebook.com/anna.smirnova"],
    "travelWithOthers": false,
    "visitedCountries": ["Германия", "Франция", "Италия"],
    "fatherDateOfBirth": "1962-12-05",
    "hasBankStatements": true,
    "hasRelativesInUSA": false,
    "hasVisaRejections": false,
    "motherDateOfBirth": "1965-04-18",
    "passportIssueDate": "2021-03-10",
    "rejectionVisaType": "",
    "universityAddress": "ул. Толе би 94",
    "passportExpiryDate": "2031-03-10",
    "hasOtherCitizenship": false,
    "hasPermanentContracts": true,
    "monthlyBusinessIncome": "",
    "businessExperienceYears": 0,
    "hasInternationalClients": false,
    "businessRegistrationDate": "",
    "businessRegistrationType": "",
    "permanentResidenceCountry": "Казахстан",
    "businessRegistrationNumber": "",
    "isFatherDateOfBirthUnknown": false,
    "isMotherDateOfBirthUnknown": false,
    "isPermanentResidentOtherCountry": false
  }'::jsonb,
  3,
  '+***********',
  'не оплачено',
  'ожидает',
  'прошли первичный опросник',
  false,
  true,
  '2024-12-02 14:30:00+00',
  '2024-12-02 14:30:00+00'
),
(
  uuid_generate_v4(),
  'agent-003',
  '{
    "iin": "456*********",
    "ssn": "",
    "city": "Шымкент",
    "name": "Алексей",
    "email": "<EMAIL>",
    "phone": "+***********",
    "gender": "Мужской",
    "hasSSN": false,
    "income": "300000",
    "address": "мкр. Нурсат 15, дом 23, кв. 45",
    "country": "Kazakhstan",
    "endDate": "2018-06-20",
    "faculty": "Менеджмент",
    "surname": "Козлов",
    "zipCode": "160000",
    "idNumber": "*********",
    "position": "IT менеджер",
    "groupName": "",
    "hasOffice": true,
    "hasUSVisa": true,
    "paysTaxes": true,
    "relatives": [{"name": "Ольга Козлова", "relation": "жена", "inUSA": false}],
    "startDate": "2018-09-01",
    "workPhone": "+77771234570",
    "companions": [{"name": "Ольга Козлова", "relation": "жена"}],
    "fatherName": "Дмитрий",
    "motherName": "Светлана",
    "occupation": "IT менеджер",
    "otherNames": "",
    "taxpayerId": "456*********",
    "visaNumber": "V98765432",
    "citizenship": "Казахстан",
    "cityOfBirth": "Шымкент",
    "companyName": "Digital Innovations",
    "dateOfBirth": "1988-11-03",
    "nationality": "Казахский",
    "workAddress": "пр. Республики 67, БЦ Нур Плаза",
    "businessName": "Kozlov Digital",
    "businessType": "IT услуги",
    "hasBeenToUSA": true,
    "hasEmployees": true,
    "lastVisaDate": "2022-05-15",
    "yearlyIncome": "3600000",
    "employeeCount": 5,
    "fatherSurname": "Козлов",
    "hasOtherNames": false,
    "hasTaxpayerId": true,
    "isFatherInUSA": false,
    "isMotherInUSA": false,
    "isSameCountry": true,
    "maritalStatus": "Женат",
    "motherSurname": "Козлова",
    "officeAddress": "пр. Республики 67, офис 1205",
    "rejectionDate": "",
    "travelAsGroup": true,
    "travelPurpose": "Бизнес",
    "businessStatus": "Действующий",
    "countryOfBirth": "Казахстан",
    "isSameVisaType": true,
    "passportNumber": "N45678912",
    "universityName": "ЮКУ им. М. Ауэзова",
    "visaRejections": [],
    "workExperience": "12 лет",
    "businessAddress": "пр. Республики 67, офис 1205",
    "businessWebsite": "kozlovdigital.com",
    "fatherUSAReason": "",
    "motherUSAReason": "",
    "visaDestination": "США",
    "businessActivity": "Разработка программного обеспечения",
    "fullNameCyrillic": "Козлов Алексей Дмитриевич",
    "otherCitizenship": "",
    "socialMediaLinks": ["linkedin.com/in/alexey-kozlov", "instagram.com/kozlov_alex"],
    "travelWithOthers": true,
    "visitedCountries": ["США", "Канада", "Великобритания", "Германия", "Китай"],
    "fatherDateOfBirth": "1960-09-12",
    "hasBankStatements": true,
    "hasRelativesInUSA": false,
    "hasVisaRejections": false,
    "motherDateOfBirth": "1963-01-25",
    "passportIssueDate": "2019-08-05",
    "rejectionVisaType": "",
    "universityAddress": "пр. Тауке хана 5",
    "passportExpiryDate": "2029-08-05",
    "hasOtherCitizenship": false,
    "hasPermanentContracts": true,
    "monthlyBusinessIncome": "500000",
    "businessExperienceYears": 6,
    "hasInternationalClients": true,
    "businessRegistrationDate": "2018-03-15",
    "businessRegistrationType": "ТОО",
    "permanentResidenceCountry": "Казахстан",
    "businessRegistrationNumber": "************",
    "isFatherDateOfBirthUnknown": false,
    "isMotherDateOfBirthUnknown": false,
    "isPermanentResidentOtherCountry": false
  }'::jsonb,
  4,
  '+***********',
  'оплачено',
  'подано',
  'документы готовы к подаче',
  false,
  false,
  '2024-12-03 09:15:00+00',
  '2024-12-03 09:15:00+00'
),
(
  uuid_generate_v4(),
  'agent-004',
  '{
    "iin": "789*********",
    "ssn": "",
    "city": "Уфа",
    "name": "Мария",
    "email": "<EMAIL>",
    "phone": "+***********",
    "gender": "Женский",
    "hasSSN": false,
    "income": "120000",
    "address": "Бикбая 17",
    "country": "Russia",
    "endDate": "2019-05-25",
    "faculty": "Экономический",
    "surname": "Волкова",
    "zipCode": "450000",
    "idNumber": "*********",
    "position": "Финансовый менеджер",
    "groupName": "",
    "hasOffice": true,
    "hasUSVisa": false,
    "paysTaxes": true,
    "relatives": [{"name": "Игорь Волков", "relation": "муж", "inUSA": false}],
    "startDate": "2019-08-01",
    "workPhone": "+***********",
    "companions": [],
    "fatherName": "Владимир",
    "motherName": "Наталья",
    "occupation": "Финансовый менеджер",
    "otherNames": "",
    "taxpayerId": "789*********",
    "visaNumber": "",
    "citizenship": "Россия",
    "cityOfBirth": "Уфа",
    "companyName": "ФинГрупп Башкортостан",
    "dateOfBirth": "1993-02-14",
    "nationality": "Русский",
    "workAddress": "ул. Ленина 56, офис 301",
    "businessName": "",
    "businessType": "",
    "hasBeenToUSA": false,
    "hasEmployees": false,
    "lastVisaDate": "",
    "yearlyIncome": "1440000",
    "employeeCount": 0,
    "fatherSurname": "Иванов",
    "hasOtherNames": false,
    "hasTaxpayerId": true,
    "isFatherInUSA": false,
    "isMotherInUSA": false,
    "isSameCountry": true,
    "maritalStatus": "Замужем",
    "motherSurname": "Иванова",
    "officeAddress": "ул. Ленина 56, офис 301",
    "rejectionDate": "2023-08-15",
    "travelAsGroup": false,
    "travelPurpose": "Туризм",
    "businessStatus": "",
    "countryOfBirth": "Россия",
    "isSameVisaType": false,
    "passportNumber": "*********",
    "universityName": "БашГУ",
    "visaRejections": [{"date": "2023-08-15", "reason": "Недостаточные финансовые гарантии"}],
    "workExperience": "4 года",
    "businessAddress": "",
    "businessWebsite": "",
    "fatherUSAReason": "",
    "motherUSAReason": "",
    "visaDestination": "США",
    "businessActivity": "",
    "fullNameCyrillic": "Волкова Мария Владимировна",
    "otherCitizenship": "",
    "socialMediaLinks": ["vk.com/maria_volkova"],
    "travelWithOthers": false,
    "visitedCountries": ["Турция", "Египет"],
    "fatherDateOfBirth": "1968-06-30",
    "hasBankStatements": true,
    "hasRelativesInUSA": false,
    "hasVisaRejections": true,
    "motherDateOfBirth": "1970-11-08",
    "passportIssueDate": "2022-01-20",
    "rejectionVisaType": "B1/B2",
    "universityAddress": "ул. Заки Валиди 32",
    "passportExpiryDate": "2032-01-20",
    "hasOtherCitizenship": false,
    "hasPermanentContracts": true,
    "monthlyBusinessIncome": "",
    "businessExperienceYears": 0,
    "hasInternationalClients": false,
    "businessRegistrationDate": "",
    "businessRegistrationType": "",
    "permanentResidenceCountry": "Россия",
    "businessRegistrationNumber": "",
    "isFatherDateOfBirthUnknown": false,
    "isMotherDateOfBirthUnknown": false,
    "isPermanentResidentOtherCountry": false
  }'::jsonb,
  2,
  '+***********',
  'не оплачено',
  'отклонено',
  'требуется доработка документов',
  false,
  true,
  '2024-12-04 16:45:00+00',
  '2024-12-04 16:45:00+00'
);

-- Проверяем результат
SELECT COUNT(*) as total_clients FROM public.visa_applications;
SELECT visa_status, COUNT(*) as count FROM public.visa_applications GROUP BY visa_status; 