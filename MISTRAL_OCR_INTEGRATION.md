# Mistral OCR Integration

## Обзор

Этот документ описывает интеграцию Mistral OCR API в проект VisaAI для замены текущей реализации OCR на основе Tesseract.js.

## Изменения

### 1. Новые файлы

#### `src/types/mistral-ocr.ts`
- Типы TypeScript для Mistral OCR API
- Интерфейсы для запросов, ответов и конфигурации
- Типы для обработки изображений и документов

#### `src/utils/temp-storage.ts`
- Утилиты для временного хранения файлов в Supabase Storage
- Функции для загрузки и очистки временных файлов
- Автоматическое определение MIME типов

#### `src/utils/mistral-ocr.ts`
- Основная логика интеграции с Mistral OCR API
- Функции для обработки изображений и PDF документов
- Обработка ошибок и автоматическая очистка ресурсов

#### `tests/mistral-ocr.test.ts`
- Тесты для функциональности Mistral OCR
- Моки для API вызовов и временного хранения
- Проверка корректности запросов к API

#### `tests/heic-support.test.ts`
- Тесты для поддержки HEIC/HEIF файлов
- Проверка детекции файлов по сигнатуре и расширению
- Валидация байтовых сигнатур

### 2. Модифицированные файлы

#### `src/utils/ocr.ts`
- Добавлена поддержка HEIC/HEIF файлов в детекции типов
- Интегрирован переключатель между Mistral OCR и Tesseract.js
- Добавлена функция `processWithTesseract` как fallback
- Обновлена основная функция `processDocument`

#### `.env.local`
- Добавлена переменная `USE_MISTRAL_OCR=true`
- Настройка переключения между OCR движками

## Функциональность

### Поддерживаемые форматы файлов

**Изображения:**
- JPG/JPEG
- PNG
- BMP
- GIF
- WebP
- TIFF/TIF
- **HEIC/HEIF** (новая поддержка)

**Документы:**
- PDF

### OCR движки

#### Mistral OCR (основной)
- Использует Mistral OCR API
- Поддерживает как изображения, так и PDF
- Высокое качество распознавания
- Включает base64 изображения в ответ

#### Tesseract.js (fallback)
- Используется при ошибках Mistral OCR
- Локальная обработка
- Совместимость с serverless окружением

### Переключение OCR движков

Переключение осуществляется через переменные окружения:

```bash
# Использовать Mistral OCR (рекомендуется)
USE_MISTRAL_OCR=true

# Использовать только Tesseract.js
USE_MISTRAL_OCR=false
```

Если `MISTRAL_API_KEY` присутствует, Mistral OCR включается автоматически.

## Архитектура

### Поток обработки документов

1. **Детекция типа файла** - определение PDF или изображение
2. **Выбор OCR движка** - Mistral OCR или Tesseract.js
3. **Временное хранение** (для Mistral OCR) - загрузка в Supabase Storage
4. **OCR обработка** - извлечение текста
5. **Парсинг данных** - извлечение информации о паспорте
6. **Очистка ресурсов** - удаление временных файлов

### Обработка ошибок

- Автоматический fallback на Tesseract.js при ошибках Mistral OCR
- Graceful handling отсутствующих API ключей
- Автоматическая очистка временных файлов
- Подробное логирование для отладки

## Конфигурация

### Переменные окружения

```bash
# Обязательные для Mistral OCR
MISTRAL_API_KEY=your_mistral_api_key

# Опциональные
USE_MISTRAL_OCR=true  # Принудительное включение Mistral OCR
```

### Supabase Storage

Для работы Mistral OCR требуется настроенное Supabase Storage:
- Bucket `temp-ocr-files` создается автоматически
- Публичный доступ для временных файлов
- Автоматическая очистка устаревших файлов

## Тестирование

### Запуск тестов

```bash
# Все тесты
npm test

# Только Mistral OCR тесты
npm test mistral-ocr

# Только HEIC тесты
npm test heic-support
```

### Покрытие тестами

- ✅ Обработка изображений с Mistral OCR
- ✅ Обработка PDF с Mistral OCR
- ✅ Обработка ошибок API
- ✅ Детекция HEIC/HEIF файлов
- ✅ Fallback на Tesseract.js
- ✅ Временное хранение файлов

## Производительность

### Преимущества Mistral OCR

- **Высокая точность** - лучшее качество распознавания
- **Поддержка PDF** - нативная обработка PDF документов
- **HEIC поддержка** - современные форматы изображений
- **Масштабируемость** - облачная обработка

### Оптимизации

- Автоматическая очистка временных файлов
- Timeout для API запросов (60 секунд)
- Fallback на локальную обработку
- Кэширование конфигурации

## Мониторинг

### Логирование

Все операции логируются с префиксами:
- `=== USING MISTRAL OCR ===`
- `=== USING TESSERACT.JS ===`
- `=== PROCESSING PDF WITH MISTRAL OCR ===`
- `=== PROCESSING IMAGE WITH MISTRAL OCR ===`

### Метрики

- Время обработки документов
- Успешность OCR операций
- Использование fallback механизма
- Размер обработанных файлов

## Безопасность

- API ключи хранятся в переменных окружения
- Временные файлы автоматически удаляются
- Публичные URL имеют ограниченное время жизни
- Валидация типов файлов по сигнатуре

## Развертывание

### Vercel

Проект готов к развертыванию на Vercel:
- Настроены переменные окружения
- Включены WASM файлы для Tesseract.js
- Оптимизирована конфигурация Next.js

### Переменные окружения для продакшена

```bash
MISTRAL_API_KEY=your_production_api_key
USE_MISTRAL_OCR=true
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Миграция

### Обратная совместимость

- Существующий API остается неизменным
- Автоматический fallback на Tesseract.js
- Постепенный переход на Mistral OCR

### План миграции

1. ✅ Развертывание с включенным Mistral OCR
2. ✅ Мониторинг производительности
3. 🔄 Постепенное увеличение нагрузки на Mistral OCR
4. 📋 Отключение Tesseract.js после стабилизации

## Поддержка

### Известные ограничения

- Требует интернет соединение для Mistral OCR
- Зависимость от Supabase Storage
- Ограничения API Mistral (rate limits)

### Решение проблем

1. **Ошибки Mistral OCR** - автоматический fallback на Tesseract.js
2. **Проблемы с Storage** - локальная обработка
3. **Отсутствие API ключа** - использование Tesseract.js

## Заключение

Интеграция Mistral OCR значительно улучшает качество распознавания текста в проекте VisaAI, добавляя поддержку современных форматов файлов и обеспечивая высокую надежность через fallback механизм.
