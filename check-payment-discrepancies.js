const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkPaidClients() {
  console.log('🔍 Checking clients with payment discrepancies...\n');
  
  const { data: apps, error } = await supabase
    .from('visa_applications')
    .select('id, agent_id, client_progress_status, service_payment_status, form_data, created_at')
    .order('created_at', { ascending: false })
    .limit(20);
    
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  console.log('Found', apps.length, 'recent applications\n');
  
  apps.forEach((app, index) => {
    const name = `${app.form_data?.name || app.form_data?.firstName || ''} ${app.form_data?.surname || app.form_data?.lastName || ''}`.trim() || 'Unknown';
    const isPaid = app.service_payment_status === 'оплачено';
    const progressStatus = app.client_progress_status;
    
    // Check for discrepancies
    const hasDiscrepancy = (isPaid && progressStatus !== 'Оплатили пакет услуг') || 
                          (!isPaid && progressStatus === 'Оплатили пакет услуг');
    
    if (hasDiscrepancy || index < 5) { // Show first 5 + all discrepancies
      console.log(`${index + 1}. ${name}`);
      console.log(`   Payment Status: ${app.service_payment_status}`);
      console.log(`   Progress Status: ${progressStatus}`);
      console.log(`   Agent: ${app.agent_id}`);
      console.log(`   Created: ${new Date(app.created_at).toLocaleString()}`);
      if (hasDiscrepancy) {
        console.log('   ⚠️  DISCREPANCY DETECTED!');
      }
      console.log('');
    }
  });
  
  const discrepancies = apps.filter(app => {
    const isPaid = app.service_payment_status === 'оплачено';
    const progressStatus = app.client_progress_status;
    return (isPaid && progressStatus !== 'Оплатили пакет услуг') || 
           (!isPaid && progressStatus === 'Оплатили пакет услуг');
  });
  
  console.log(`📊 Summary: ${discrepancies.length} clients with payment/progress status discrepancies`);
}

checkPaidClients().catch(console.error);