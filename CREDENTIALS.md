# 🔐 Visa Form Application - Complete Credentials Guide

## 🏢 Supabase Database Credentials

### Production Database


## 👥 Admin Login Credentials

### Super Administrator Accounts

#### Primary Super Admin
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: Super Administrator
- **Access**: Full system access, all companies, all features
- **Permissions**: Company blocking/unblocking, system configuration

#### Backup Super Admin
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: System Administrator
- **Access**: Full system access, all companies, all features
- **Permissions**: Company blocking/unblocking, system configuration

## 🏢 Visa Company Credentials

### VisaProKZ - Premium Visa Services
- **Company ID**: `550e8400-e29b-41d4-a716-************`
- **Admin Email**: `<EMAIL>`
- **Admin Password**: `password`
- **Role**: Visa Admin
- **Company Name**: VisaProKZ
- **Services**: USA, UK, EU, China visa processing
- **Status**: Active ✅
- **Blocking Status**: Can be blocked/unblocked by Super Admin
- **Access**: Company-specific applications and employees

### Globalis - International Visa Solutions
- **Company ID**: `550e8400-e29b-41d4-a716-446655440002`
- **Admin Email**: `<EMAIL>`
- **Admin Password**: `password`
- **Role**: Visa Admin
- **Company Name**: Globalis
- **Services**: Comprehensive visa and immigration services
- **Status**: Active ✅
- **Blocking Status**: Can be blocked/unblocked by Super Admin
- **Access**: Company-specific applications and employees

### EliteVisa - Luxury Visa Services
- **Company ID**: `550e8400-e29b-41d4-a716-************`
- **Admin Email**: `<EMAIL>`
- **Admin Password**: `password`
- **Role**: Visa Admin
- **Company Name**: EliteVisa
- **Services**: Premium visa processing for high-net-worth individuals
- **Status**: Active ✅
- **Blocking Status**: Can be blocked/unblocked by Super Admin
- **Access**: Company-specific applications and employees

## 👨‍💼 Employee Accounts

### VisaProKZ Employee
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: Employee
- **Company**: VisaProKZ
- **Access**: Limited to assigned tasks and applications

### Globalis Employee
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: Employee
- **Company**: Globalis
- **Access**: Limited to assigned tasks and applications

### EliteVisa Employee
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: Employee
- **Company**: EliteVisa
- **Access**: Limited to assigned tasks and applications

## 🔑 Access Levels & Permissions

### Super Admin
- ✅ View all companies and applications
- ✅ Create/edit/delete companies
- ✅ Block/unblock companies (with confirmation)
- ✅ Manage all employees across companies
- ✅ Access all analytics and reports
- ✅ System configuration and settings
- ✅ Full client management across all countries
- ✅ Database administration privileges

### Visa Admin (Company Level)
- ✅ View company-specific applications
- ✅ Manage company employees
- ✅ Company analytics and reports
- ✅ Client management for company
- ✅ Process visa applications
- ❌ Cannot access other companies
- ❌ Cannot block/unblock companies
- ❌ Cannot access system-wide settings

### Employee
- ✅ View assigned applications
- ✅ Update application status
- ✅ Basic client interaction
- ✅ Upload and process documents
- ❌ Cannot manage other employees
- ❌ Limited analytics access
- ❌ Cannot modify company settings

## 🌐 Application URLs

### Development Environment
- **Main Application**: `http://localhost:3000`
- **Admin Login**: `http://localhost:3000/admin/login`
- **Admin Dashboard**: `http://localhost:3000/admin/dashboard`
- **Company Management**: `http://localhost:3000/admin/dashboard` (Companies tab)

### Production Environment
- **Main Application**: `https://your-domain.com`
- **Admin Login**: `https://your-domain.com/admin/login`
- **Admin Dashboard**: `https://your-domain.com/admin/dashboard`

## 📊 Database Statistics

### Applications Data
- **Total Applications**: 39
  - USA Visas: 16 applications
  - UK Visas: 8 applications
  - EU Visas: 8 applications
  - China Visas: 7 applications

### Company Distribution
- **Active Companies**: 3 visa service providers
- **Total Employees**: 6 (2 super admins, 3 company admins, 2 employees)
- **Company Blocking**: Implemented with confirmation modals

### Features Available
- ✅ Full Trello-style workflow management
- ✅ Client management system
- ✅ Analytics and reporting
- ✅ Company blocking/unblocking functionality
- ✅ Document upload and OCR processing
- ✅ Multi-country visa processing

## 🔒 Security Features

### Authentication
- JWT-based authentication
- Role-based access control (RBAC)
- Company-level data isolation
- Session management

### Company Blocking System
- **Database-Based Blocking**: Uses `companies.is_blocked` column for persistent state
- **Immediate Enforcement**: Direct database validation during authentication
- **Persistent State**: Blocking survives server restarts and deployments
- **Confirmation Modals**: UI confirmation for all blocking/unblocking actions
- **Visual Status Indicators**: Real-time status badges in company management
- **Super Admin Bypass**: Super admins always have access regardless of blocking
- **API Endpoints**: 
  - Block: `PUT /api/admin/companies` with `{"companyId": "xxx", "isBlocked": true}`
  - Unblock: `PUT /api/admin/companies` with `{"companyId": "xxx", "isBlocked": false}`
- **Error Messages**: Localized blocking messages in Russian for blocked users

### Data Protection
- Supabase Row Level Security (RLS)
- **Company Data Isolation**: Each company sees only their own data
- **Role-Based Access Control**: Super admin sees all data, company users see company-specific data
- Encrypted API keys
- Secure file upload handling
- GDPR compliance ready

## ⚠️ Important Notes

### Development Guidelines
- All passwords are set to simple values for development/testing
- In production, use strong passwords and enable 2FA
- Super admin accounts have no company restrictions
- Company admins can only see their company's data
- All accounts are active and ready for immediate use

### Company Blocking
- Blocked companies cannot log in to the admin panel
- Blocking requires confirmation via modal dialog
- Visual indicators show company status (Active/Blocked)
- Only Super Admins can block/unblock companies

### Database Schema
- Companies table includes blocking functionality
- Applications linked to specific companies
- Employee accounts tied to company access
- Comprehensive audit logging available

## 🛠️ Technical Configuration

### Environment Variables Required
```bash
NEXT_PUBLIC_SUPABASE_URL=https://jobktdnksbweojxfqvcs.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### Database Tables
- `companies` - Visa company information with `is_blocked` column for persistent blocking
- `employees` - User accounts and role management
- `applications` - Visa application data
- `documents` - File uploads and OCR results
- `analytics` - Usage and performance metrics

## Database Schema & Data

### Countries
- **Data Language**: English (standardized from Russian)
- **Supported Countries**: 
  - United States (6 applications)
  - Germany (1 application) 
  - Italy (1 application)
  - Spain (1 application)
- **Country Categories**:
  - US: United States visa applications
  - UK: United Kingdom visa applications (none currently)
  - EU: European Union/Schengen countries (Germany, Italy, Spain)
  - CN: China visa applications (none currently)
- **Country Mapping**: Direct English names, no translation needed

---

**Last Updated**: December 2024
**Version**: 2.0
**Status**: Production Ready ✅ 