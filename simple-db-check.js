const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function simpleDbCheck() {
  console.log('🔍 Simple Database Check...\n');

  try {
    // 1. Check recent applications
    console.log('1️⃣ Checking recent applications...');
    const { data: recentApps, error: recentError } = await supabase
      .from('visa_applications')
      .select(`
        id,
        agent_id,
        step_status,
        company_id,
        client_progress_status,
        service_payment_status,
        visa_status,
        form_data,
        created_at
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    if (recentError) {
      console.error('❌ Error fetching recent applications:', recentError);
      return;
    }

    console.log(`📊 Found ${recentApps.length} recent applications:`);
    recentApps.forEach((app, index) => {
      const name = `${app.form_data?.name || app.form_data?.firstName || ''} ${app.form_data?.surname || app.form_data?.lastName || ''}`.trim() || 'Unknown';
      const phone = app.form_data?.phone || app.form_data?.phoneNumber || 'No phone';
      
      console.log(`\n${index + 1}. ${name} (${phone})`);
      console.log(`   Agent: ${app.agent_id}`);
      console.log(`   Company ID: ${app.company_id || 'NULL'}`);
      console.log(`   Step: ${app.step_status}`);
      console.log(`   Progress: ${app.client_progress_status || 'NULL'}`);
      console.log(`   Payment: ${app.service_payment_status || 'NULL'}`);
      console.log(`   Visa: ${app.visa_status || 'NULL'}`);
      console.log(`   Created: ${new Date(app.created_at).toLocaleString()}`);
    });

    // 2. Check companies
    console.log('\n2️⃣ Checking companies...');
    const { data: companies, error: compError } = await supabase
      .from('companies')
      .select('id, name, slug, is_blocked');

    if (compError) {
      console.error('❌ Error fetching companies:', compError);
      return;
    }

    console.log(`📋 Found ${companies.length} companies:`);
    companies.forEach(company => {
      console.log(`  - ${company.name} (${company.slug}) - ID: ${company.id}, blocked: ${company.is_blocked}`);
    });

    // 3. Count applications by company
    console.log('\n3️⃣ Applications by company...');
    const companyCounts = {};
    let nullCompanyCount = 0;

    recentApps.forEach(app => {
      if (!app.company_id) {
        nullCompanyCount++;
      } else {
        companyCounts[app.company_id] = (companyCounts[app.company_id] || 0) + 1;
      }
    });

    for (const [companyId, count] of Object.entries(companyCounts)) {
      const company = companies.find(c => c.id === companyId);
      const name = company ? company.name : `Unknown (${companyId})`;
      console.log(`  - ${name}: ${count} applications`);
    }

    if (nullCompanyCount > 0) {
      console.log(`  - NULL company: ${nullCompanyCount} applications`);
    }

    // 4. Check for missing status fields
    console.log('\n4️⃣ Checking for missing status fields...');
    let missingProgressStatus = 0;
    let missingPaymentStatus = 0;
    let missingVisaStatus = 0;

    recentApps.forEach(app => {
      if (!app.client_progress_status) missingProgressStatus++;
      if (!app.service_payment_status) missingPaymentStatus++;
      if (!app.visa_status) missingVisaStatus++;
    });

    console.log(`📊 Missing status fields in recent applications:`);
    console.log(`  - Missing client_progress_status: ${missingProgressStatus}`);
    console.log(`  - Missing service_payment_status: ${missingPaymentStatus}`);
    console.log(`  - Missing visa_status: ${missingVisaStatus}`);

    // 5. Test admin API
    console.log('\n5️⃣ Testing admin API...');
    try {
      const apiResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/applications?companyId=all&limit=5&_t=${Date.now()}`);
      
      if (apiResponse.ok) {
        const apiData = await apiResponse.json();
        console.log(`✅ Admin API working: ${apiData.applications.length} applications returned`);
        console.log(`   Total in API response: ${apiData.total}`);
        
        if (apiData.applications.length > 0) {
          const firstApp = apiData.applications[0];
          const name = `${firstApp.form_data?.name || firstApp.form_data?.firstName || ''} ${firstApp.form_data?.surname || firstApp.form_data?.lastName || ''}`.trim() || 'Unknown';
          console.log(`   First application: ${name} (${firstApp.agent_id})`);
          console.log(`   Company ID: ${firstApp.company_id}`);
          console.log(`   Status: ${firstApp.client_progress_status}`);
        }
      } else {
        console.log(`❌ Admin API failed: ${apiResponse.status} ${apiResponse.statusText}`);
      }
    } catch (apiError) {
      console.log(`❌ Admin API error:`, apiError.message);
    }

    console.log('\n📋 Summary:');
    console.log(`- Total recent applications: ${recentApps.length}`);
    console.log(`- Applications with company_id: ${recentApps.filter(app => app.company_id).length}`);
    console.log(`- Applications with NULL company_id: ${nullCompanyCount}`);
    console.log(`- Companies available: ${companies.length}`);
    console.log(`- Missing progress status: ${missingProgressStatus}`);
    console.log(`- Missing payment status: ${missingPaymentStatus}`);
    console.log(`- Missing visa status: ${missingVisaStatus}`);

  } catch (error) {
    console.error('❌ Check failed:', error);
  }
}

// Run the check
simpleDbCheck();