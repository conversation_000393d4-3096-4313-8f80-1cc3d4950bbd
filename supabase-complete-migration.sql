-- 🚀 COMPLETE SUPABASE MIGRATION FOR VISA APPLICATION SYSTEM
-- This script ensures all necessary tables and fields are present
-- Execute this script in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- 1. COMPANIES TABLE - Core company management
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.companies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  phone_number TEXT,
  email TEXT,
  is_blocked BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- 2. EMPLOYEES TABLE - User management with role-based access
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.employees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('super_admin', 'visa_admin', 'manager')),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  full_name TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- 3. PRICE LIST TABLE - Service packages for each company
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.price_list (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  country TEXT NOT NULL,
  title TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  duration TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- 4. VISA APPLICATIONS TABLE - Complete field set
-- ============================================================================

-- First ensure the basic table exists
CREATE TABLE IF NOT EXISTS public.visa_applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  agent_id TEXT NOT NULL,
  form_data JSONB NOT NULL DEFAULT '{}'::jsonb,
  step_status INTEGER NOT NULL DEFAULT 1,
  uploaded_files JSONB NOT NULL DEFAULT '{}'::jsonb,
  phone_number TEXT,
  whatsapp_redirected BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add ALL missing columns one by one with IF NOT EXISTS logic
DO $$ 
BEGIN 
    -- Company relationship
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'company_id') THEN
        ALTER TABLE public.visa_applications ADD COLUMN company_id UUID REFERENCES companies(id) ON DELETE SET NULL;
    END IF;

    -- Payment and service tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'service_payment_status') THEN
        ALTER TABLE public.visa_applications ADD COLUMN service_payment_status TEXT DEFAULT 'не оплачено' 
        CHECK (service_payment_status IN ('оплачено', 'не оплачено'));
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'service_package_id') THEN
        ALTER TABLE public.visa_applications ADD COLUMN service_package_id UUID REFERENCES price_list(id) ON DELETE SET NULL;
    END IF;

    -- Visa status tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'visa_status') THEN
        ALTER TABLE public.visa_applications ADD COLUMN visa_status TEXT DEFAULT 'ожидает' 
        CHECK (visa_status IN ('ожидает', 'одобрено', 'отклонено', 'подано'));
    END IF;

    -- Client progress tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'client_progress_status') THEN
        ALTER TABLE public.visa_applications ADD COLUMN client_progress_status TEXT DEFAULT 'прошли опросник';
    END IF;

    -- Manual fixes and status management
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'manual_fix_status') THEN
        ALTER TABLE public.visa_applications ADD COLUMN manual_fix_status TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'consular_fee_paid') THEN
        ALTER TABLE public.visa_applications ADD COLUMN consular_fee_paid BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'requires_fixes') THEN
        ALTER TABLE public.visa_applications ADD COLUMN requires_fixes BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'fix_comment') THEN
        ALTER TABLE public.visa_applications ADD COLUMN fix_comment TEXT;
    END IF;

    -- File management
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'invitation_file_url') THEN
        ALTER TABLE public.visa_applications ADD COLUMN invitation_file_url TEXT;
    END IF;

    -- Manager tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'added_by_manager') THEN
        ALTER TABLE public.visa_applications ADD COLUMN added_by_manager BOOLEAN DEFAULT false;
    END IF;

    -- Legacy timestamp field
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' AND column_name = 'last_updated') THEN
        ALTER TABLE public.visa_applications ADD COLUMN last_updated TIMESTAMP WITH TIME ZONE DEFAULT now();
    END IF;

END $$;

-- ============================================================================
-- 5. INDEXES FOR PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Company indexes
CREATE INDEX IF NOT EXISTS idx_companies_name ON public.companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_is_blocked ON public.companies(is_blocked);

-- Employee indexes
CREATE INDEX IF NOT EXISTS idx_employees_company_id ON public.employees(company_id);
CREATE INDEX IF NOT EXISTS idx_employees_email ON public.employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_role ON public.employees(role);

-- Price list indexes
CREATE INDEX IF NOT EXISTS idx_price_list_company_id ON public.price_list(company_id);
CREATE INDEX IF NOT EXISTS idx_price_list_country ON public.price_list(country);

-- Visa applications indexes
CREATE INDEX IF NOT EXISTS idx_visa_applications_agent_id ON public.visa_applications(agent_id);
CREATE INDEX IF NOT EXISTS idx_visa_applications_company_id ON public.visa_applications(company_id);
CREATE INDEX IF NOT EXISTS idx_visa_applications_phone_number ON public.visa_applications(phone_number);
CREATE INDEX IF NOT EXISTS idx_visa_applications_service_package_id ON public.visa_applications(service_package_id);
CREATE INDEX IF NOT EXISTS idx_visa_applications_service_payment_status ON public.visa_applications(service_payment_status);
CREATE INDEX IF NOT EXISTS idx_visa_applications_visa_status ON public.visa_applications(visa_status);
CREATE INDEX IF NOT EXISTS idx_visa_applications_step_status ON public.visa_applications(step_status);
CREATE INDEX IF NOT EXISTS idx_visa_applications_created_at ON public.visa_applications(created_at);

-- JSONB indexes for form_data searches
CREATE INDEX IF NOT EXISTS idx_visa_applications_form_data_gin ON public.visa_applications USING gin (form_data);
CREATE INDEX IF NOT EXISTS idx_visa_applications_visa_country ON public.visa_applications USING gin ((form_data->>'visaCountry'));

-- ============================================================================
-- 6. ROW LEVEL SECURITY (RLS) SETUP
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.price_list ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.visa_applications ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow read access to companies" ON public.companies;
DROP POLICY IF EXISTS "Allow insert companies for super admin" ON public.companies;
DROP POLICY IF EXISTS "Allow update companies for super admin" ON public.companies;
DROP POLICY IF EXISTS "Allow delete companies for super admin" ON public.companies;

-- Companies policies - allow all access for now (can be restricted later)
CREATE POLICY "Allow all access to companies"
  ON public.companies FOR ALL
  USING (true)
  WITH CHECK (true);

-- Drop existing employee policies
DROP POLICY IF EXISTS "Allow read access to employees" ON public.employees;
DROP POLICY IF EXISTS "Allow insert employees" ON public.employees;
DROP POLICY IF EXISTS "Allow update employees" ON public.employees;
DROP POLICY IF EXISTS "Allow delete employees" ON public.employees;

-- Employees policies
CREATE POLICY "Allow all access to employees"
  ON public.employees FOR ALL
  USING (true)
  WITH CHECK (true);

-- Drop existing price list policies
DROP POLICY IF EXISTS "Allow read access to price_list" ON public.price_list;
DROP POLICY IF EXISTS "Allow insert price_list" ON public.price_list;
DROP POLICY IF EXISTS "Allow update price_list" ON public.price_list;
DROP POLICY IF EXISTS "Allow delete price_list" ON public.price_list;

-- Price list policies
CREATE POLICY "Allow all access to price_list"
  ON public.price_list FOR ALL
  USING (true)
  WITH CHECK (true);

-- Drop existing visa applications policies
DROP POLICY IF EXISTS "Read visa applications by agent_id" ON public.visa_applications;
DROP POLICY IF EXISTS "Insert visa applications" ON public.visa_applications;
DROP POLICY IF EXISTS "Update visa applications by agent_id" ON public.visa_applications;

-- Visa applications policies - allow all access for admin panel
CREATE POLICY "Allow all access to visa_applications"
  ON public.visa_applications FOR ALL
  USING (true)
  WITH CHECK (true);

-- ============================================================================
-- 7. SAMPLE DATA INSERTION
-- ============================================================================

-- Insert sample companies (only if they don't exist)
INSERT INTO public.companies (id, name, phone_number, email, is_blocked) VALUES 
  ('550e8400-e29b-41d4-a716-************', 'Visa Pro Kz', '******-123-4567', '<EMAIL>', false),
  ('550e8400-e29b-41d4-a716-************', 'Globalis', '******-234-5678', '<EMAIL>', false),
  ('550e8400-e29b-41d4-a716-************', 'Elite Visa', '******-345-6789', '<EMAIL>', false)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  phone_number = EXCLUDED.phone_number,
  email = EXCLUDED.email,
  is_blocked = EXCLUDED.is_blocked;

-- Insert sample employees with proper password hashes
INSERT INTO public.employees (id, email, password_hash, role, company_id, full_name, is_active) VALUES 
  ('660e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', NULL, 'Super Administrator', true),
  ('660e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'visa_admin', '550e8400-e29b-41d4-a716-************', 'Visa Pro Admin', true),
  ('660e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'visa_admin', '550e8400-e29b-41d4-a716-************', 'Globalis Admin', true),
  ('660e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'visa_admin', '550e8400-e29b-41d4-a716-************', 'Elite Visa Admin', true)
ON CONFLICT (email) DO UPDATE SET
  password_hash = EXCLUDED.password_hash,
  role = EXCLUDED.role,
  company_id = EXCLUDED.company_id,
  full_name = EXCLUDED.full_name,
  is_active = EXCLUDED.is_active;

-- Insert comprehensive price list data
INSERT INTO public.price_list (company_id, country, title, price, duration, description) VALUES 
  -- Visa Pro Kz (Company 1) - USA packages
  ('550e8400-e29b-41d4-a716-************', 'США', 'Базовый', 450.00, '4-5 недель', 'Стандартное оформление туристической визы'),
  ('550e8400-e29b-41d4-a716-************', 'США', 'Стандарт', 650.00, '3-4 недели', 'Ускоренное оформление с консультацией'),
  ('550e8400-e29b-41d4-a716-************', 'США', 'Экспресс', 850.00, '1-2 недели', 'Максимально быстрое оформление'),
  
  -- Visa Pro Kz - UK packages
  ('550e8400-e29b-41d4-a716-************', 'Великобритания', 'Стандарт', 550.00, '3-4 недели', 'Оформление британской визы'),
  ('550e8400-e29b-41d4-a716-************', 'Великобритания', 'Премиум', 750.00, '2-3 недели', 'Ускоренное оформление с поддержкой'),
  
  -- Globalis (Company 2) - USA packages
  ('550e8400-e29b-41d4-a716-************', 'США', 'Эконом', 500.00, '5-6 недель', 'Бюджетное оформление американской визы'),
  ('550e8400-e29b-41d4-a716-************', 'США', 'Стандарт Плюс', 700.00, '3-4 недели', 'Стандарт с дополнительными услугами'),
  ('550e8400-e29b-41d4-a716-************', 'США', 'Премиум', 950.00, '1-2 недели', 'Премиум пакет с гарантией'),
  
  -- Globalis - Canada packages  
  ('550e8400-e29b-41d4-a716-************', 'Канада', 'Стандарт', 600.00, '4-5 недель', 'Оформление канадской визы'),
  ('550e8400-e29b-41d4-a716-************', 'Канада', 'Экспресс', 800.00, '2-3 недели', 'Ускоренное оформление'),
  
  -- Elite Visa (Company 3) - Multiple countries
  ('550e8400-e29b-41d4-a716-************', 'США', 'Элит Стандарт', 750.00, '2-3 недели', 'Элитное оформление американской визы'),
  ('550e8400-e29b-41d4-a716-************', 'США', 'Элит Премиум', 1100.00, '1 неделя', 'Максимальный сервис и скорость'),
  ('550e8400-e29b-41d4-a716-************', 'Великобритания', 'UK Элит', 650.00, '2-3 недели', 'Элитное оформление британской визы'),
  ('550e8400-e29b-41d4-a716-************', 'Франция', 'Шенген Элит', 500.00, '2-3 недели', 'Элитное оформление шенгена через Францию')
ON CONFLICT DO NOTHING;

-- ============================================================================
-- 8. UPDATE EXISTING APPLICATIONS WITH COMPANY ASSIGNMENTS
-- ============================================================================

-- Assign existing visa applications to companies (distributed evenly)
UPDATE public.visa_applications 
SET company_id = (
  CASE 
    WHEN (row_number() OVER (ORDER BY created_at)) % 3 = 1 
    THEN '550e8400-e29b-41d4-a716-************'::UUID  -- Visa Pro Kz
    WHEN (row_number() OVER (ORDER BY created_at)) % 3 = 2 
    THEN '550e8400-e29b-41d4-a716-************'::UUID  -- Globalis
    ELSE '550e8400-e29b-41d4-a716-************'::UUID  -- Elite Visa
  END
)
WHERE company_id IS NULL;

-- Update applications with proper default statuses
UPDATE public.visa_applications 
SET 
  service_payment_status = COALESCE(service_payment_status, 'не оплачено'),
  visa_status = COALESCE(visa_status, 'ожидает'),
  client_progress_status = COALESCE(client_progress_status, 'прошли опросник'),
  consular_fee_paid = COALESCE(consular_fee_paid, false),
  requires_fixes = COALESCE(requires_fixes, false),
  added_by_manager = COALESCE(added_by_manager, false),
  last_updated = COALESCE(last_updated, updated_at)
WHERE service_payment_status IS NULL 
   OR visa_status IS NULL 
   OR client_progress_status IS NULL 
   OR consular_fee_paid IS NULL 
   OR requires_fixes IS NULL
   OR added_by_manager IS NULL
   OR last_updated IS NULL;

-- ============================================================================
-- 9. UTILITY FUNCTIONS
-- ============================================================================

-- Function to update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers to update updated_at automatically
DROP TRIGGER IF EXISTS update_companies_updated_at ON public.companies;
CREATE TRIGGER update_companies_updated_at 
    BEFORE UPDATE ON public.companies 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employees_updated_at ON public.employees;
CREATE TRIGGER update_employees_updated_at 
    BEFORE UPDATE ON public.employees 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_price_list_updated_at ON public.price_list;
CREATE TRIGGER update_price_list_updated_at 
    BEFORE UPDATE ON public.price_list 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_visa_applications_updated_at ON public.visa_applications;
CREATE TRIGGER update_visa_applications_updated_at 
    BEFORE UPDATE ON public.visa_applications 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 10. FINAL SUMMARY
-- ============================================================================

DO $$
DECLARE
    company_count INTEGER;
    employee_count INTEGER;
    price_list_count INTEGER;
    application_count INTEGER;
    applications_with_company INTEGER;
BEGIN
    SELECT COUNT(*) INTO company_count FROM public.companies;
    SELECT COUNT(*) INTO employee_count FROM public.employees;
    SELECT COUNT(*) INTO price_list_count FROM public.price_list;
    SELECT COUNT(*) INTO application_count FROM public.visa_applications;
    SELECT COUNT(*) INTO applications_with_company FROM public.visa_applications WHERE company_id IS NOT NULL;
    
    RAISE NOTICE '✅ MIGRATION COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '📊 Database Summary:';
    RAISE NOTICE '   • Companies: %', company_count;
    RAISE NOTICE '   • Employees: %', employee_count;
    RAISE NOTICE '   • Price Packages: %', price_list_count;
    RAISE NOTICE '   • Total Applications: %', application_count;
    RAISE NOTICE '   • Applications with Company: %', applications_with_company;
    RAISE NOTICE '';
    RAISE NOTICE '🔐 Default Login Credentials:';
    RAISE NOTICE '   • Super Admin: <EMAIL> / password';
    RAISE NOTICE '   • Visa Pro Kz: <EMAIL> / password';
    RAISE NOTICE '   • Globalis: <EMAIL> / password';
    RAISE NOTICE '   • Elite Visa: <EMAIL> / password';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 System ready for use!';
END $$;
