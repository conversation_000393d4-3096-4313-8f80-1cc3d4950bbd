-- Migration script to add phone_number column and normalize existing phone numbers
-- This script should be run on existing Supabase installations

-- Add phone_number column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'visa_applications' 
                   AND column_name = 'phone_number' 
                   AND table_schema = 'public') THEN
        ALTER TABLE public.visa_applications ADD COLUMN phone_number TEXT;
        RAISE NOTICE 'Added phone_number column to visa_applications table';
    ELSE
        RAISE NOTICE 'phone_number column already exists in visa_applications table';
    END IF;
END $$;

-- Function to normalize phone numbers in PostgreSQL
CREATE OR REPLACE FUNCTION normalize_phone_number(phone_input TEXT)
RETURNS TEXT AS $$
DECLARE
    cleaned TEXT;
BEGIN
    -- Return empty string if input is null or empty
    IF phone_input IS NULL OR phone_input = '' THEN
        RETURN '';
    END IF;
    
    -- Remove all non-digit characters except +
    cleaned := regexp_replace(phone_input, '[^\d+]', '', 'g');
    
    -- Remove + if present
    cleaned := regexp_replace(cleaned, '^\+', '');
    
    -- If phone starts with 8, replace with 7
    IF cleaned ~ '^8' THEN
        cleaned := '7' || substring(cleaned from 2);
    END IF;
    
    -- If phone doesn't start with 7, add 7 prefix for Kazakhstan
    IF NOT cleaned ~ '^7' THEN
        cleaned := '7' || cleaned;
    END IF;
    
    -- Ensure we have exactly 11 digits (7 + 10 digits)
    IF length(cleaned) > 11 THEN
        cleaned := substring(cleaned from 1 for 11);
    END IF;
    
    -- Add + prefix
    RETURN '+' || cleaned;
END;
$$ LANGUAGE plpgsql;

-- Update existing records to normalize phone numbers
-- This will extract phone numbers from form_data and normalize them
UPDATE public.visa_applications 
SET phone_number = normalize_phone_number(form_data->>'phone')
WHERE form_data->>'phone' IS NOT NULL 
  AND form_data->>'phone' != ''
  AND (phone_number IS NULL OR phone_number = '');

-- Create index on phone_number for faster searches
CREATE INDEX IF NOT EXISTS idx_visa_applications_phone_number 
ON public.visa_applications(phone_number);

-- Display summary of the migration
DO $$
DECLARE
    total_records INTEGER;
    records_with_phone INTEGER;
    normalized_records INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_records FROM public.visa_applications;
    SELECT COUNT(*) INTO records_with_phone FROM public.visa_applications WHERE form_data->>'phone' IS NOT NULL AND form_data->>'phone' != '';
    SELECT COUNT(*) INTO normalized_records FROM public.visa_applications WHERE phone_number IS NOT NULL AND phone_number != '';
    
    RAISE NOTICE 'Migration Summary:';
    RAISE NOTICE 'Total records: %', total_records;
    RAISE NOTICE 'Records with phone in form_data: %', records_with_phone;
    RAISE NOTICE 'Records with normalized phone_number: %', normalized_records;
END $$;

-- Clean up the temporary function (optional)
-- DROP FUNCTION IF EXISTS normalize_phone_number(TEXT);
