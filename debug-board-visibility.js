// Debug script to check why client doesn't appear in board view

const CLIENT_PROGRESS_STATUS = {
  COMPLETED_QUESTIONNAIRE: 'Прошли опросник',
  PAID_PACKAGE: 'Оплатили пакет услуг',
  BOT_COLLECTING: 'Сбор информации ботом',
  WAITING_INVITATION: 'Ожидает приглашения',
  CASE_COORDINATION: 'Согласование кейса',
  FORM_FILLING: 'Заполнение анкеты + обучение',
  SUBMITTED: 'Подано'
};

const SERVICE_PAYMENT_STATUS = {
  PAID: 'оплачено',
  UNPAID: 'не оплачено',
  PENDING: 'ожидает оплаты'
};

// Test client data
const testClient = {
  id: '18ac5fc1-5a62-443a-87ee-47449d8bd28e',
  company_id: '550e8400-e29b-41d4-a716-446655440001',
  client_progress_status: 'Прошли опросник',
  service_payment_status: 'не оплачено',
  visa_status: 'ожидает',
  created_at: '2025-06-30T12:00:00',
  form_data: {
    name: 'Амир',
    surname: 'Ниязов',
    visaCountry: 'США'
  }
};

console.log('🔍 Debugging board visibility for client:', testClient.id);
console.log('\n📋 Client details:');
console.log('- Status:', testClient.client_progress_status);
console.log('- Payment:', testClient.service_payment_status);
console.log('- Country:', testClient.form_data.visaCountry);
console.log('- Created:', testClient.created_at);

// Test normalization
console.log('\n🔄 Testing status normalization:');
const normalizedStatus = testClient.client_progress_status.toLowerCase() === 'прошли опросник' 
  ? CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE 
  : testClient.client_progress_status;
console.log('- Original:', testClient.client_progress_status);
console.log('- Normalized:', normalizedStatus);
console.log('- Matches constant:', normalizedStatus === CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE);

// Test step mapping
console.log('\n📍 Testing step mapping:');
const progressToStepMap = {
  [CLIENT_PROGRESS_STATUS.COMPLETED_QUESTIONNAIRE]: 1,
  [CLIENT_PROGRESS_STATUS.PAID_PACKAGE]: 2,
  [CLIENT_PROGRESS_STATUS.BOT_COLLECTING]: 3,
  [CLIENT_PROGRESS_STATUS.WAITING_INVITATION]: 4,
  [CLIENT_PROGRESS_STATUS.CASE_COORDINATION]: 5,
  [CLIENT_PROGRESS_STATUS.FORM_FILLING]: 6,
  [CLIENT_PROGRESS_STATUS.SUBMITTED]: 7
};

console.log('Progress map keys:', Object.keys(progressToStepMap));
console.log('Looking for:', normalizedStatus);

let stepId = 1; // Default

// Priority check: payment status
if (testClient.service_payment_status === SERVICE_PAYMENT_STATUS.PAID) {
  stepId = 2;
  console.log('✅ Client is PAID -> Step 2');
} else if (progressToStepMap[normalizedStatus]) {
  stepId = progressToStepMap[normalizedStatus];
  console.log('✅ Found in map -> Step', stepId);
} else {
  console.log('❌ Not found in map, using default -> Step 1');
}

console.log('\n🎯 Result: Client should appear in Step', stepId, '(Прошли опросник)');

// Check date filtering
console.log('\n📅 Checking date filtering:');
const createdDate = new Date(testClient.created_at);
const today = new Date();
const daysSinceCreation = Math.floor((today - createdDate) / (1000 * 60 * 60 * 24));
console.log('- Created:', daysSinceCreation, 'days ago');
console.log('- Date range should include clients from at least', daysSinceCreation, 'days ago');

// Possible issues
console.log('\n⚠️  Possible issues to check:');
console.log('1. Date filter might be excluding clients created on', testClient.created_at);
console.log('2. Country filter might be active (client country:', testClient.form_data.visaCountry + ')');
console.log('3. Company filter might be excluding company ID:', testClient.company_id);
console.log('4. The applications array might be empty or not containing this client');
console.log('5. Frontend filtering in filteredApplications might be excluding the client');