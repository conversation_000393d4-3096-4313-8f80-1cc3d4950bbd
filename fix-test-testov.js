const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixTestTestov() {
  console.log('🔧 Fixing Test Testov status...\n');
  
  // Fix the specific Test Testov application with wrong status
  const { data, error } = await supabase
    .from('visa_applications')
    .update({
      client_progress_status: 'Прошли опросник'
    })
    .eq('id', '619bed1b-3b39-4faf-af3e-1eedd70e3821')
    .select();
    
  if (error) {
    console.error('❌ Error updating:', error);
    return;
  }
  
  console.log('✅ Successfully updated Test Testov status to "Прошли опросник"');
  console.log('Updated record:', data);
}

fixTestTestov().catch(console.error);