# Manual Setup Instructions for WhatsApp API Fields

## Issue
The companies tab is showing as empty because the database migration hasn't been applied or there are no companies in the database.

## Solution

### Step 1: Apply the Database Migration

Go to your **Supabase Dashboard** → **SQL Editor** and run this migration:

```sql
-- Migration: Add WhatsApp API fields and populate with dummy data
-- Execute this script in your Supabase SQL editor

-- Step 1: Add WhatsApp API fields to companies table
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS wapi_token TEXT,
ADD COLUMN IF NOT EXISTS wapi_profile_id TEXT,
ADD COLUMN IF NOT EXISTS wapi_webhook_url TEXT;

-- Add comments for documentation
COMMENT ON COLUMN public.companies.wapi_token IS 'WhatsApp API Token for integration';
COMMENT ON COLUMN public.companies.wapi_profile_id IS 'WhatsApp API Profile ID';
COMMENT ON COLUMN public.companies.wapi_webhook_url IS 'WhatsApp API Webhook URL for callbacks';

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_companies_wapi_profile_id ON public.companies(wapi_profile_id) WHERE wapi_profile_id IS NOT NULL;

-- Step 2: Update existing companies with dummy WhatsApp API data
-- Update Visa Pro Kazakhstan
UPDATE public.companies 
SET 
  wapi_token = 'vp_token_9a8b7c6d5e4f3g2h1i0j',
  wapi_profile_id = 'visa_pro_profile_001',
  wapi_webhook_url = 'https://api.visapro.kz/webhook/whatsapp'
WHERE name = 'Visa Pro Kazakhstan';

-- Update Elite Visa Consulting  
UPDATE public.companies 
SET 
  wapi_token = 'ev_token_1j2k3l4m5n6o7p8q9r0s',
  wapi_profile_id = 'elite_visa_profile_002', 
  wapi_webhook_url = 'https://api.elitevisa.kz/webhook/whatsapp'
WHERE name = 'Elite Visa Consulting';

-- Update Globalis Kazakhstan
UPDATE public.companies 
SET 
  wapi_token = 'gl_token_s0r9q8p7o6n5m4l3k2j1',
  wapi_profile_id = 'globalis_profile_003',
  wapi_webhook_url = 'https://api.globalis.kz/webhook/whatsapp'
WHERE name = 'Globalis Kazakhstan';

-- Step 3: If companies don't exist, create them with WhatsApp API data
INSERT INTO public.companies (id, name, phone_number, email, slug, wapi_token, wapi_profile_id, wapi_webhook_url, is_blocked, created_at, updated_at) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'Visa Pro Kazakhstan', '****** 123 4567', '<EMAIL>', 'visa-pro-kazakhstan', 'vp_token_9a8b7c6d5e4f3g2h1i0j', 'visa_pro_profile_001', 'https://api.visapro.kz/webhook/whatsapp', false, NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440002', 'Elite Visa Consulting', '****** 987 6543', '<EMAIL>', 'elite-visa-consulting', 'ev_token_1j2k3l4m5n6o7p8q9r0s', 'elite_visa_profile_002', 'https://api.elitevisa.kz/webhook/whatsapp', false, NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440003', 'Globalis Kazakhstan', '****** 456 7890', '<EMAIL>', 'globalis-kz', 'gl_token_s0r9q8p7o6n5m4l3k2j1', 'globalis_profile_003', 'https://api.globalis.kz/webhook/whatsapp', false, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  wapi_token = EXCLUDED.wapi_token,
  wapi_profile_id = EXCLUDED.wapi_profile_id,
  wapi_webhook_url = EXCLUDED.wapi_webhook_url,
  updated_at = NOW();

-- Step 4: Verify the data
SELECT 
  id,
  name,
  slug,
  wapi_token,
  wapi_profile_id,
  wapi_webhook_url,
  is_blocked,
  created_at
FROM public.companies 
ORDER BY created_at DESC;
```

### Step 2: Refresh the Admin Panel

After running the migration:
1. Go back to your admin panel
2. Refresh the page (Ctrl+F5 or Cmd+Shift+R)
3. Navigate to the Companies tab
4. You should now see 3 companies with WhatsApp API data

### Step 3: Test the Functionality

You should now be able to:
1. **View Companies**: See all 3 companies with their WhatsApp API fields
2. **Edit Companies**: Click the edit button (pencil icon) to modify WhatsApp API settings
3. **Create New Companies**: Use the "Add Company" button to create new companies with WhatsApp API fields

### Expected Result

The companies tab should display:
- **Visa Pro Kazakhstan** with WhatsApp API data
- **Elite Visa Consulting** with WhatsApp API data  
- **Globalis Kazakhstan** with WhatsApp API data

Each company will have:
- ✅ **API Token**: Populated with dummy token
- ✅ **Profile ID**: Populated with dummy profile ID
- ✅ **Webhook URL**: Populated with dummy webhook URL

### Troubleshooting

If companies still don't appear:
1. Check browser console for JavaScript errors
2. Check network tab for API call failures
3. Verify Supabase connection is working
4. Check if the migration was applied successfully by running:
   ```sql
   SELECT column_name, data_type, is_nullable
   FROM information_schema.columns 
   WHERE table_name = 'companies' 
   ORDER BY ordinal_position;
   ```

### WhatsApp API Fields Added

The migration adds these new fields to the `companies` table:
- `wapi_token` (TEXT) - WhatsApp API Token for integration
- `wapi_profile_id` (TEXT) - WhatsApp API Profile ID
- `wapi_webhook_url` (TEXT) - WhatsApp API Webhook URL for callbacks

All fields are optional and can be updated later through the admin interface. 